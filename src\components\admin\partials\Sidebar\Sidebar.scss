.active-svg > svg,
.active-svg > svg > path {
  fill: #13aba3 !important;
}
.active-stroke > svg > path {
  stroke: #13aba3 !important;
}
.child-icon {
  margin-top: -2px;
}
.search-icon {
  stroke: #333;
}
.search-icon-stroke {
  stroke: #13aba3 !important;
}
.fill-primary {
  svg {
    fill: #333333;
    path {
      fill: #333333;
    }
  }
}

.v-navigation-drawer {
  top: 64px !important;
  height: calc(100vh - 64px) !important;
  .v-navigation-drawer__content {
    margin-top: 18px;
    &::-webkit-scrollbar {
      width: 3px;
    }
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #b9b9b9;
      border-radius: 36px;
      border: none;
    }
  }
  .v-list {
    padding: 0;
  }
}

.v-navigation-drawer--temporary.v-navigation-drawer--clipped {
  z-index: 5;
}

.treeview-custom-sidebar {
  .v-list-item__prepend > .v-badge .v-icon,
  .v-list-item__prepend > .v-icon,
  .v-list-item__append > .v-badge .v-icon,
  .v-list-item__append > .v-icon {
    opacity: 1;
  }

  /* Make the background of the active item transparent by default */
  .v-list-item.v-list-item--active .v-list-item__overlay {
    background-color: transparent !important;
    transition: none !important; /* Removes any transition effect */
  }

  /* Ensure that the background remains transparent when not hovering */
  .v-list-item__overlay,
  .v-btn__overlay {
    background-color: transparent !important;
    transition: none !important; /* Ensures no transition effect */
  }

  /* Add hover effect for both active and non-active items */
  .v-list-item:hover .v-list-item__overlay,
  .v-list-group__header:hover .v-list-item__overlay,
  .v-list-item.v-list-item--link:hover .v-list-item__overlay {
    background-color: #1baba3 !important;
    opacity: 0.1 !important; /* Adjusts the transparency on hover */
  }

  /* Change text and icon colors on hover for both active and non-active items */
  .v-list-item:hover,
  .v-list-item:hover * {
    // color: #13aba3 !important;
    fill: #13aba3 !important; /* Ensures icons and text change to the desired color */
    stroke: #13aba3 !important;
  }

  /* Ensure that the focus and active states do not change the background */
  .v-list-item:focus .v-list-item__overlay,
  .v-list-item:active .v-list-item__overlay {
    background-color: transparent !important;
    opacity: 1 !important;
  }

  .v-list-item__prepend {
    display: block;
  }
  .page-link {
    background-color: transparent;
    color: #333;
    text-decoration: none;
    height: 46px;
    display: inline-flex;
    width: 100%;
    align-items: center;
    position: absolute;
    height: 100%;
    &:hover {
      color: #1baba3;
    }
  }

  .v-list-item-action {
    position: absolute;
    right: 10px;
    top: 10px;
    color: #00000080;
  }
}
.v-treeview-node__level {
  width: 0 !important;
}
.drawer-mini {
  .treeview-custom-sidebar {
    .v-treeview-node__root {
      .v-treeview-node__toggle {
        display: none;
      }
    }
  }
}

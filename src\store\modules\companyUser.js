import axios from '@/services/api';

function initialState() {
  return {
    status: '',
    singleCompanyUser: {},
    allCompanyUsers: [],
    csvData: null,
  };
}

const state = initialState();

const getters = {
  getAllCompanyUsers: (state) => state.allCompanyUsers,
  getSingleCompanyUser: (state) => state.singleCompanyUser,
  getCompanyUserCsvData: (state) => state.csvData,
};

const actions = {
  ['COMPANY_USER_CREATE']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      axios
        .post(`/admin/company-user/create`, params)
        .then((response) => {
          commit('COMPANY_USER_CREATE_SUCCESS', response.data);
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },
  ['COMPANY_USERS_GET_ALL']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      axios
        .get(`/admin/company-users`, { params })
        .then((response) => {
          commit('COMPANY_USERS_GET_ALL_SUCCESS', response.data);
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },
  ['GET_SINGLE_COMPANY_USER']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      axios
        .get(`/admin/company-user/${params.id}`, params)
        .then((response) => {
          commit('GET_SINGLE_COMPANY_USER_SUCCESS', response.data);
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },
  ['COMPANY_USER_UPDATE']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      axios
        .post(`/admin/company-user/${params.id}`, params)
        .then((response) => {
          commit('COMPANY_USER_CREATE_SUCCESS', response.data);
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },
  ['COMPANY_USER_EXPORT_CSV']: ({ commit, dispatch }) => {
    dispatch('API_PROCESSING', true, { root: true });
    return new Promise((resolve, reject) => {
      axios
        .get('/admin/companyuserexport')
        .then((response) => {
          commit('COMPANY_USER_EXPORT_CSV_SUCCESS', response.data);
          resolve(response);
        })
        .catch((error) => {
          reject(error);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },
};

const mutations = {
  ['COMPANY_USER_CREATE_SUCCESS']: (state, payload) => {
    state.status = 'success';
    state.singleCompanyUser = payload?.data?.data;
  },
  ['COMPANY_USERS_GET_ALL_SUCCESS']: (state, payload) => {
    state.status = 'success';
    state.allCompanyUsers = payload.data;
  },
  ['GET_SINGLE_COMPANY_USER_SUCCESS']: (state, payload) => {
    state.status = 'success';
    state.singleCompanyUser = payload.data;
  },
  ['COMPANY_USER_EXPORT_CSV_SUCCESS']: (state, payload) => {
    state.csvData = payload;
  },
};

export default {
  state,
  getters,
  actions,
  mutations,
};

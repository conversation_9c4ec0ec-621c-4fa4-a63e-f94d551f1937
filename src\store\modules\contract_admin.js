import axios from '@/services/api';

import {
  CONTRACT_REQUEST,
  CONTRACT_GET_ALL,
  CONTRACT_GET_ALL_SUCCESS,
  CONTRACT_GET,
  CONTRACT_GET_SUCCESS,
  CONTRACT_CREATE,
  CONTRACT_CREATE_SUCCESS,
  CONTRACT_UPDATE,
  CONTRACT_UPDATE_SUCCESS,
  CONTRACT_DELETE,
  CONTRACT_DELETE_SUCCESS,
  UPDATE_CONTRACT_ADMIN_READ,
  UPDATE_CONTRACT_ADMIN_READ_SUCCESS,
} from '@/store/actions/contract_admin';

function initialState() {
  return {
    status: '',
    contract: [],
    singleContract: {},
    contractPagination: null,
    csvData: null,
    active: 0,
    inActive: 0,
    draft: 0,
    statusCount: 0,
    generatedContract: null,
  };
}

const state = initialState();

const getters = {
  getAllContractAdmin: (state) => state.contract,
  getSingleContractAdmin: (state) => state.singleContract,
  getContractPagination: (state) => state.contractPagination,
  getContractCategories: (state) => state.contractCategories,
  getContractCsvData: (state) => state.csvData,
  getContractActiveCount: (state) => state.active,
  getContractInactiveCount: (state) => state.inActive,
  getContractDraftCount: (state) => state.draft,
  getContractStatusCount: (state) => state.statusCount,
  getSingleGeneratedContractAdmin: (state) => state.generatedContract,
};

const actions = {
  [UPDATE_CONTRACT_ADMIN_READ]: ({ commit }, params) => {
    return new Promise((resolve, reject) => {
      axios
        .get('/admin/contract/admin-read', { params })
        .then((response) => {
          commit(
            'UPDATE_TOTAL_UNREAD_APPLICATIONS',
            response?.data?.data?.counts?.total_admin_unread
          );
          commit(UPDATE_CONTRACT_ADMIN_READ_SUCCESS, response);
          resolve(response);
        })
        .catch((error) => {
          reject(error);
        });
    });
  },
  ['CONTRACT_EXPORT_CSV']: ({ commit, dispatch }, params) => {
    dispatch('API_PROCESSING', true, { root: true });
    return new Promise((resolve, reject) => {
      axios
        .get('/admin/export/contract', { params })
        .then((response) => {
          commit('CONTRACT_EXPORT_CSV_SUCCESS', response.data);
          resolve(response);
        })
        .catch((error) => {
          reject(error);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },
  ['UNCONTRACT_EXPORT_CSV']: ({ commit, dispatch }, params) => {
    dispatch('API_PROCESSING', true, { root: true });
    return new Promise((resolve, reject) => {
      axios
        .get('/admin/export/uncontract', { params })
        .then((response) => {
          commit('CONTRACT_EXPORT_CSV_SUCCESS', response.data);
          resolve(response);
        })
        .catch((error) => {
          reject(error);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },
  [CONTRACT_GET_ALL]: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('CONTRACT_REQUEST');
      axios
        .get(`/admin/contract`, { params })
        .then((response) => {
          commit(
            'UPDATE_TOTAL_UNREAD_APPLICATIONS',
            response?.data?.data?.counts?.total_admin_unread
          );
          commit('CONTRACT_GET_ALL_SUCCESS', response.data);
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  [CONTRACT_GET]: ({ commit, dispatch }, id) => {
    let idContract = id
    if (id && id.isPDF) idContract = id.id
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('CONTRACT_REQUEST');
      axios
        .get(`admin/contract/` + idContract)
        .then((response) => {
          commit('CONTRACT_GET_SUCCESS', response);
          resolve(response);
        })
        .catch((error) => {
          reject(error);
        })
        .finally(() => {
          if (!id.isPDF) dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['CONTRACT_STUDENT_GET']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('CONTRACT_REQUEST');
      axios
        .get(`/admin/student/contract/${params.id}/${params.status}`, {
          params,
        })
        .then((response) => {
          commit('CONTRACT_GET_SUCCESS', response.data);
          resolve(response);
        })
        .catch((err) => {
          commit('CONTRACT_GET_ERROR');
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  [CONTRACT_CREATE]: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('CONTRACT_REQUEST');
      axios
        .post(`/admin/company/${params.id}`, params)
        .then((response) => {
          let data = response.data.data;
          commit('CONTRACT_CREATE_SUCCESS', { data });
          resolve(response);
        })
        .catch((err) => {
          commit('CONTRACT_CREATE_ERROR');
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  [CONTRACT_UPDATE]: ({ commit, dispatch }, params) => {
    dispatch('API_PROCESSING', true, { root: true });
    return new Promise((resolve, reject) => {
      commit('CONTRACT_REQUEST');
      axios
        .put(`/admin/contract/${params.id}`, params)
        .then((response) => {
          let data = response.data.contract;
          commit('CONTRACT_UPDATE_SUCCESS', { data });
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  [CONTRACT_DELETE]: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      if (!params?.id) {
        return;
      }

      dispatch('API_PROCESSING', true, { root: true });
      commit('USERS_REQUEST');
      axios
        .delete(`/admin/company/${params.id}`)
        .then((response) => {
          let data = response.data.data;
          commit('CONTRACT_DELETE_SUCCESS', { data });
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['REMOVE_SINGLE_CONTRACT']: ({commit}) => {
    commit('REMOVE_SINGLE_CONTRACT');
  },
};

const mutations = {
  [UPDATE_CONTRACT_ADMIN_READ_SUCCESS]: (state, payload) => {
    state.contractCounts = payload.data.data.counts;
  },
  ['CONTRACT_EXPORT_CSV_SUCCESS']: (state, payload) => {
    state.csvData = payload;
  },
  [CONTRACT_REQUEST]: (state) => {
    state.status = 'loading';
  },

  [CONTRACT_GET_ALL_SUCCESS]: (state, payload) => {
    state.status = 'success';
    state.contract = payload.data.data;
    state.contractPagination = payload.paginate;
    state.inActive = payload.counts.inactive;
    state.active = payload.counts.active;
    state.draft = payload.counts.draft;
    state.statusCount = payload.counts.status_1;
  },

  [CONTRACT_GET_SUCCESS]: (state, payload) => {
    state.status = 'success';
    state.singleContract = payload.data;
  },

  [CONTRACT_CREATE_SUCCESS]: (state) => {
    state.status = 'success';
  },

  [CONTRACT_UPDATE_SUCCESS]: (state, data) => {
    state.status = 'success';
    state.generatedContract = data.data;
  },

  [CONTRACT_DELETE_SUCCESS]: (state) => {
    state.status = 'success';
  },

  ['REMOVE_SINGLE_CONTRACT']: (state) => {
    state.generatedContract = '';
  },
};

export default {
  state,
  getters,
  actions,
  mutations,
};

import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import createI18nInstance from '@/plugins/i18n'; // Import the i18n async function
import '@/plugins/vee-validate'; // Vee-validate configuration
import PageTitle from '@/components/ui/PageTitle.vue';
import 'moment/locale/ja';
import { VueMaskDirective } from 'v-mask'; // Correct import from the latest version of v-mask
import '@/services/api';
// Import Vuetify
import 'vuetify/styles'; // Import Vuetify core styles
import '@mdi/font/css/materialdesignicons.css'; // Import Material Design Icons (optional)
import { createMetaManager } from 'vue-meta';
import vuetify from '@/plugins/vuetify';

// Import Vee-Validate components and configure them with i18n
import { Form, Field, ErrorMessage, configure } from 'vee-validate';
import { localize } from '@vee-validate/i18n';

import globalMixin from '@/mixins/index.js';

// This function will initialize the app after i18n is set up
async function setupApp() {
  // Await the i18n instance creation
  const i18n = await createI18nInstance();

  // Setup Vee Validate after i18n is loaded
  configure({
    generateMessage: localize({
      en: {
        messages: {
          required: (field) =>
            i18n.global.t('field_required_message', { field }),
          max: (field) => {
            const length = field?.rule?.params[0] ?? 'unknown'; // Safely access length with a fallback
            return i18n.global.t('field_max_message', { field, length });
          },
          min: (field) => {
            const length = field?.rule?.params[0] ?? 'unknown'; // Safely access length with a fallback
            return i18n.global.t('field_min_message', { field, length });
          },
          size: (field) => {
            const size = field?.rule?.params[0] ?? 'unknown'; // Safely access size with a fallback
            return i18n.global.t('field_size_message', { field, size });
          },
          email: () => i18n.global.t('field_email_message'),
          min_value: (field, { min }) =>
            i18n.global.t('field_min_value_message', { field, min }),
          max_value: (field, { max }) =>
            i18n.global.t('field_max_value_message', { field, max }),
          confirmed: () => i18n.global.t('field_password_confirmed_message'),
          url: () => i18n.global.t('field_must_be_url_message'),
          verify_password: () => i18n.global.t('field_verify_password_message'),
          slug: () => i18n.global.t('field_valid_slug_message'),
          maxDimensions: () => i18n.global.t('dimension'),
        },
      },
    }),
    validateOnInput: true, // Validate as you type
  });

  const metaManager = createMetaManager();

  const app = createApp(App);
  app.mixin(globalMixin);
  // Register Vee-Validate components globally in the app
  app.component('Form', Form);
  app.component('Field', Field);
  app.component('ErrorMessage', ErrorMessage);

  // Register global components
  app.component('PageTitle', PageTitle);

  // Register v-mask directive globally
  app.directive('mask', VueMaskDirective);

  // Use plugins
  app.use(router);
  app.use(store);
  app.use(i18n); // Use the i18n instance after it's loaded
  app.use(metaManager);
  app.use(vuetify);

  // Mount the app
  app.mount('#app');
}

// Run the setup function to initialize the app
setupApp();

<template>
  <div
    v-if="!isLoading"
    class="full-width bg-white px-16 border-radius elevation"
    :class="dataFound ? '' : 'h-585'"
  >
    <template v-if="!dataFound && !isLoading">
      <div
        class="full-height d-flex align-center flex-column justify-center mh-585"
        style="min-height: 585px"
      >
        <div class="text-8e8e font-14px fw-400">フィードバックはありません</div>
        <div class="d-flex align-center justify-center pt-5">
          <v-btn
            @click="createFeedback"
            color="primary"
            width="236px"
            height="35px"
          >
            <div class="px-9">フィードバックを作成する</div>
          </v-btn>
        </div>
      </div>
    </template>
    <template v-else>
      <div  class="d-flex align-center justify-center mt-14 pt-6">
        <div class="container-report" style="width: 746px;">
          <div class="row-report">
            <div class="label-report">姓名</div>
            <div class="value-report">{{ getStudentData?.family_name }} {{ getStudentData?.first_name }}</div>
          </div>
          <div class="row-report">
            <div class="label-report">セイメイ</div>
            <div class="value-report">{{ getStudentData?.family_name_furigana }} {{ getStudentData?.first_name_furigana }}</div>
          </div>
        </div>
      </div>
      <div
        class="d-flex align-center fw-500 font-18px full-width pt-6 mb-7 justify-center"
      >
        Kotonaru Power 8
      </div>
      <div class="d-flex align-center justify-center">
        <div
          v-for="(table, i) in feedbackTables"
          :key="i"
          :class="[i > 0 ? 'ml-12' : '']"
        >
          <thead>
            <tr>
              <th class="text-left"></th>
              <th class="text-center px-0">
                <v-icon size="16">$leadership</v-icon>
              </th>
              <th class="text-left pl-10 pr-16">
                <v-icon size="16">$indepth</v-icon>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in table" :key="index">
              <td
                style="border-bottom: 1px solid #e5e5e5"
                class="pr-16 pl-0 my-2 py-3"
              >
                <v-sheet
                  flat
                  elevation="0"
                  width="129"
                  height="24"
                  class="d-flex align-center justify-center bg-fffbf0 rounded-16"
                >
                  <div class="font-14px">{{ item.title }}</div>
                </v-sheet>
              </td>
              <td style="border-bottom: 1px solid #e5e5e5" class="px-0">
                {{ item.superPowerReview }}
              </td>
              <td style="border-bottom: 1px solid #e5e5e5" class="pl-10 pr-16">
                {{ item.growthIdeaReview }}
              </td>
            </tr>
          </tbody>
        </div>
      </div>
      <div class="mt-16 fw-500 font-18px">フィードバック一覧</div>
      <v-divider
        style="background-color: #e5e5e5"
        class="mt-4"
      ></v-divider>
      <br />
      <div class="full-width panel-parent pb-6">
        <v-expansion-panels
          v-for="(panel, i) in panels"
          :key="i"
          class="mb-2"
          flat
          v-model="expandedPanels"
          accordion
          multiple
        >
          <v-expansion-panel :class="[i === panels.length - 1 ? '' : 'panel']">
            <v-expansion-panel-title class="px-0 py-0 my-0">
              <template #default>
              <div style="width: 100%" class="d-flex align-center my-0 justify-space-between d-flex">
                <div class="fw-500 font-14px">{{ panel.title }}</div>
                <v-icon
                  :class="{ 'rotate-icon': expandedPanels && expandedPanels.includes(i) }"
                  color="primary"
                >
                  $expand
                </v-icon>
              </div>
              </template>
            </v-expansion-panel-title>

            <v-expansion-panel-text class="panel-content pr-0">
              <v-expansion-panels multiple flat class="mt-2" accordion v-model="panel.currentNestedExpanded">
                <v-expansion-panel v-for="(item, j) in panel.feedbacks" :key="j" :class="[j === panel.feedbacks.length - 1 ? '' : 'panel']">
                  <v-expansion-panel-title class="px-0 py-0 my-0">
                    <template #default>
                      <div class="full-width d-flex align-center font-14px fw-400 text-8e8e justify-space-between">
                      <div class="d-flex">
                          <div class="d-flex align-center">
                            <div>{{ item.date }}</div>
                          </div>
                          <v-btn
                            icon
                            variant="text"
                            @click.stop="goTo('AddressedFeedbackEdit', item.id)"
                            aria-label="Edit Feedback"
                          >
                            <v-icon size="13">$edit</v-icon>
                          </v-btn>
                        </div>
                      </div>
                      <div>
                        <v-icon
                          :class="{ 'rotate-icon': panel.currentNestedExpanded && panel.currentNestedExpanded.includes(j) }"
                          color="primary"
                        >
                          $expand
                        </v-icon>
                      </div>
                    </template>
                  </v-expansion-panel-title>

                  <v-expansion-panel-text class="panel-content pr-0">
                    <div
                      class="full-width"
                      v-for="(feedback, index) in item.feedbacks"
                      :key="index"
                    >
                      <div
                        class="d-flex align-center mb-2"
                        :class="[index > 0 ? 'mt-4' : '']"
                      >
                        <v-icon size="16" class="mr-2">{{ feedback.icon }}</v-icon>
                        <div class="fw-500 font-14px">{{ feedback.title }}</div>
                      </div>
                      <div class="feedback-description">
                        {{ linebreaks(feedback.description) }}
                      </div>
                    </div>
                  </v-expansion-panel-text>
                </v-expansion-panel>
              </v-expansion-panels>
              </v-expansion-panel-text>
          </v-expansion-panel>
        </v-expansion-panels>
      </div>
    </template>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter, useRoute } from 'vue-router';
import moment from 'moment';

export default {
  setup() {
    /**
     * Core Vue Composition API Setup
     */
    const store = useStore();
    const router = useRouter();
    const route = useRoute();
    store.dispatch('API_PROCESSING', true);

    /**
     * Reactive State Management
     */
    const dataFound = ref(false); // Tracks if any feedback data exists

    /**
     * Kotonaru Power 8 Table Configuration
     * Defines the 8 personality traits split into two tables
     * Left table: Leadership traits
     * Right table: Collaborative traits
     */
    const feedbackTables = ref([
      [
        { title: 'リーダーシップ', superPowerReview: 0, growthIdeaReview: 0 }, // Leadership
        { title: '大胆さ', superPowerReview: 0, growthIdeaReview: 0 }, // Boldness
        { title: '外向的', superPowerReview: 0, growthIdeaReview: 0 }, // Extroversion
        { title: '創造性', superPowerReview: 0, growthIdeaReview: 0 }, // Creativity
      ],
      [
        { title: '協調性', superPowerReview: 0, growthIdeaReview: 0 }, // Cooperation
        { title: '綿密さ', superPowerReview: 0, growthIdeaReview: 0 }, // Meticulousness
        { title: '内省的', superPowerReview: 0, growthIdeaReview: 0 }, // Introspection
        { title: '論理性', superPowerReview: 0, growthIdeaReview: 0 }, // Logical thinking
      ],
    ]);

    // Stores feedback panels data
    const panels = ref([]);

    /**
     * Vuex Store Getters
     * Access to global state data
     */
    const getSingleStudent = computed(() => store.getters.getSingleStudent);
    const getStudentFeedback = computed(() => store.getters.getStudentFeedback);
    const getStudentComments = computed(() => store.getters.getStudentComments);
    const getMasterData = computed(() => store.getters.getMasterData);
    const getStudentData = computed(() => store.getters.getStudentData);


    /**
     * Utility Functions
     */
    // Converts Windows-style line breaks to Unix-style
    const linebreaks = (feedback) => feedback.split('\r\n').join('\n');

    const formatDate = (date) => moment(date).format('YYYY年M月');


    /**
     * Navigation Handlers
     */
    // Navigate to specific feedback page
    const goTo = (name, id, student_id = null) => {
      router.push({
        name,
        params: { id },
        query: { student_id },
      });
    };

    // Navigate to feedback creation page
    const createFeedback = () => {
      router.push({
        name: 'AddressedFeedbackCreate',
        params: {
          company_id: '-',
          student_id: getSingleStudent.value.student_internal_id,
        },
      });
    };

    /**
     * Data Processing Functions
     */
    const assignData = () => {
      // Process and organize feedback comments by company
      panels.value = getStudentComments.value.map((data) => {
        const company = data.company_info;
        // Sort comments by posted date (newest first)
        const comments = data.comments.sort((a, b) => {
          const difference = moment(a.work_month_feedback_target, 'YYYY-MM').diff(
            moment(b.work_month_feedback_target, 'YYYY-MM'),
            'month'
          );
          return difference > 0 ? -1 : difference < 0 ? 1 : 0;
        });

        // Transform comments into structured feedback data
        const feedbacks = comments.map((comment) => ({
          date: comment.work_month_feedback_target ? moment(comment.work_month_feedback_target, 'YYYY-MM').format('YYYY年M月') : '',
          id: comment.id,
          feedbacks: [
            {
              icon: '$leadership',
              title: getMasterData.value.reviews_option.find(
                (review) => review.id === comment.super_power_review
              ).name,
              description: comment.super_power_comment,
            },
            {
              icon: '$indepth',
              title: getMasterData.value.reviews_option.find(
                (review) => review.id === comment.growth_idea_review
              ).name,
              description: comment.growth_idea_comment,
            },
          ],
        }));

        return { title: company.name, feedbacks };
      });

      // Process Kotonaru Power 8 feedback scores
      feedbackTables.value.forEach((feedback, index) => {
        let i = index > 0 ? 5 : 1; // Start from 1 for first table, 5 for second
        feedback.forEach((feed) => {
          // Assign super power and growth idea review scores
          feed.superPowerReview =
            getStudentFeedback.value[`super_power_review_${i}`];
          feed.growthIdeaReview = parseInt(
            getStudentFeedback.value[`growth_idea_review_${i}`]
          );

          // Set dataFound flag if any feedback exists
          if (
            parseInt(feed.superPowerReview) ||
            parseInt(feed.growthIdeaReview)
          ) {
            dataFound.value = true;
          }

          ++i;
        });
      });
    };

    /**
     * Data Fetching
     */
    const getData = async () => {
      await store.dispatch('GET_STUDENT_FEEDBACK', route.params.id);
      isLoading.value = false;
    };

    /**
     * Component Lifecycle
     */
    const isLoading = ref(false);
    onMounted(async () => {
      isLoading.value = true;
      await getData();
      assignData();
    });

    const expandedPanels = ref(0);

    return {
      dataFound,
      feedbackTables,
      panels,
      goTo,
      createFeedback,
      linebreaks,
      getStudentData,
      expandedPanels,
      formatDate,
      isLoading,
    };
  },
};
</script>

<style src="./index.scss" lang="scss"></style>

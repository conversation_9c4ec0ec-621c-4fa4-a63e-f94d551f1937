import axios from '@/services/api';

import {
  CONTRACT_REQUEST,
  CONTRACT_GET_ALL_STUDENT,
  CONTRACT_GET_ALL_STUDENT_SUCCESS,
  CONTRACT_GET,
  CONTRACT_GET_SUCCESS,
} from '@/store/actions/contract';

function initialState() {
  return {
    contracts: [],
    contract: {
      id: null,
      type_format: null,
      contract_template_id: null,
      contract_template: {
        id: null,
        company_id: null,
        contract_template_name: '',
        business_address: null,
        employee_title_name: null,
        type_employment_period: null,
        date_employment_start: null,
        date_employment_end: null,
        type_update_procedure: null,
        job_detail: null,
        flg_insurance_industrial_accident_compensation: null,
        flg_insurance_unemployment: null,
        flg_insurance_public_health: null,
        flg_insurance_employee_pension: null,
        wage: null,
        consultation_employee_title_name: null,
        consultation_employee_mail_address: null,
        flg_update_procedure: null,
        company_user_id: null,
        created_at: null,
        updated_at: null,
        deleted_at: null,
      },
      application_id: null,
      application: {
        id: null,
        internal_application_id: '',
        student_id: null,
        company_id: null,
        internship_post_id: null,
        interested_job_id_1: null,
        text_interested_job_1: '',
        interested_job_id_2: null,
        text_interested_job_2: '',
        interested_job_id_3: null,
        text_interested_job_3: '',
        status: null,
        datetime_applied: null,
        datetime_apply_cancel: null,
        is_admin_read: 0,
        cancel_reason: null,
        created_at: null,
        updated_at: null,
        self_introduction: null,
        type_interested_job: null,
        resume_url: null,
        qualification_url: null,
        required_item_answer: null,
        required_url_answer: null,
        flg_draft: 0,
        flg_delete: 0,
      },
      student_id: null,
      student_name: '',
      company_id: null,
      company: {
        id: null,
        internal_company_id: '',
        name: '',
        furigana_name: null,
        logo_img: '',
        business_industry_id: null,
        office_address: '',
        office_phone: '',
        office_email1: '',
        status: null,
        office_email2: '',
        office_email3: '',
        website_url: '',
        client_liason: '',
        admin_memo: '',
        created_at: null,
        updated_at: null,
      },
      date_application_passed: null,
      type_contract: null,
      original_contract_id: null,
      date_contract_signing: null,
      business_address: null,
      contract_name: '',
      employee_title_name: null,
      type_employment_period: null,
      date_employment_start: null,
      date_employment_end: null,
      type_update_procedure: null,
      job_detail: null,
      wage: null,
      status: null,
      internship_student_status: null,
      flg_draft: 0,
      flg_contract_sent: 0,
      datetime_contract_sent: null,
      flg_employment_check: 0,
      flg_hourlypay_check: 0,
      flg_personal_statement_check: 0,
      flg_contract_approved: 0,
      datetime_contract_approved: null,
      created_at: null,
      updated_at: null,
    },
  };
}

const state = initialState();

const getters = {
  getAllContractsStudent: (state) => state.contracts,
  getSingleContract: (state) => state.contract,
};

const actions = {
  [CONTRACT_GET_ALL_STUDENT]: ({ commit, dispatch }, id) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('CONTRACT_REQUEST');
      axios
        .get(`/admin/student-contracts/${id}`)
        .then((response) => {
          let data = response.data.data;
          commit('CONTRACT_GET_ALL_STUDENT_SUCCESS', { data });

          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },
};

const mutations = {
  [CONTRACT_REQUEST]: (state) => {
    state.status = 'loading';
  },

  [CONTRACT_GET_ALL_STUDENT_SUCCESS]: (state, params) => {
    state.status = 'success';
    state.contracts = params.data;
  },

  [CONTRACT_GET_SUCCESS]: (state, params) => {
    state.status = 'success';
    state.contract = params.data.contract;
  },
};

export default {
  state,
  getters,
  actions,
  mutations,
};

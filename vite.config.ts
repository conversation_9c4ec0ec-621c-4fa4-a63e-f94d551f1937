import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import vuetify from 'vite-plugin-vuetify'; // Add the Vuetify plugin
import { fileURLToPath, URL } from 'url';

export default defineConfig({
  plugins: [
    vue(),
    vuetify({ autoImport: true }), // Enable Vuetify with auto-import
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `
          @use "vuetify/styles" as *;
          @use "@/styles/variables.scss" as *;
        `,
        api: 'modern-compiler',
      },
    },
  },
});

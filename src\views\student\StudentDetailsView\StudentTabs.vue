<template>
  <div>
    <StudentApplicationDetails />
    <!--    <div class="w-100 container-tab-history">-->
    <!--      <v-tabs-->
    <!--        v-model="tab"-->
    <!--        background-color="white"-->
    <!--        color="primary"-->
    <!--        class="my-0 center-tabs"-->
    <!--      >-->
    <!--        <v-tab-->
    <!--          v-for="(item, index) in tabItems"-->
    <!--          :key="index"-->
    <!--          :class="{-->
    <!--            'font-10px tab': $vuetify.breakpoint.smAndDown,-->
    <!--            'font-16px': $vuetify.breakpoint.mdAndUp-->
    <!--          }"-->
    <!--        >-->
    <!--          {{ item.tabTitle }}-->
    <!--        </v-tab>-->
    <!--      </v-tabs>-->
    <!--    </div>-->
    <!--    <v-tabs-items v-model="tab" style="background: transparent">-->
    <!--      <v-tab-item>-->
    <!--        <StudentApplicationDetails />-->
    <!--      </v-tab-item>-->
    <!--      <v-tab-item>-->
    <!--        <StudentInternshipDetailsPage />-->
    <!--      </v-tab-item>-->
    <!--    </v-tabs-items>-->
  </div>
</template>
<script>
import StudentApplicationDetails from '@/views/student/StudentDetailsView/StudentApplicationDetails.vue';
import StudentInternshipDetailsPage from '@/views/student/StudentDetailsView/StudentInternshipDetailsPage.vue';

export default {
  name: 'StudentTabs',
  components: {
    StudentApplicationDetails,
    // StudentInternshipDetailsPage
  },
  async asyncData(context) {},
  data() {
    return {
      tab: 0,
      tabItems: [
        {
          componentName: 'StudentApplicationDetails',
          tabTitle: '応募情報',
        },
        {
          componentName: 'InternshipDetail',
          tabTitle: '求人情報',
        },
      ],
    };
  },
  computed: {},
  async mounted() {},
  methods: {},
};
</script>

<style scoped>
/* Optional: You can also add custom styles to ensure centering, if needed */
.center-tabs {
  display: flex;
  justify-content: center;
  width: 100%;
}

.center-tabs .v-tab {
  flex: 0 0 auto;
}

.container-tab-history {
  width: 100vw;
  background-color: white;
}
</style>

<template>
  <!-- ApplicationPassDialog Dialog -->
  <v-dialog v-model="selfDialog" max-width="790" content-class="no-shadow">
    <v-card class="pa-15">
      <v-card-text class="pb-5">
        <template v-if="Array.isArray(text)">
          <div
            v-for="(tt, index) in text"
            :key="index"
            class="my-1 text-center d-flex justify-center align-center text-333"
            :class="[index == 0 ? 'font-20px mb-7' : 'font-14px']"
            v-html="tt"
          ></div>
        </template>
        <template v-else>
          <div
            class="my-1 d-flex justify-center align-center text-333 font-20px"
            v-html="text"
          ></div>
        </template>

        <div class="pt-13">
          <div name="csv_student">
            <div
              v-show="!previewCSVUrl"
              :class="{ 'px-2': smAndDown }"
              class="file-input-box-container mx-auto"
            >
              <div
                class="file-input-box d-flex align-center justify-center"
                :class="previewCSVUrl ? 'hide-borders' : ''"
              >
                <div
                  class="file-input-inr"
                  :class="previewCSVUrl ? 'hide-icons' : ''"
                >
                  <v-file-input
                    accept="csv/*"
                    :hide-details="true"
                    hide-input
                    truncate-length="1"
                    @change="previewCSV"
                    v-model="fileCSV"
                    :class="previewCSVUrl ? 'hide-icons' : ''"
                    prepend-icon="mdi-plus-circle"
                    class="rounded-circle align-center justify-center pa-0 ma-auto white--text file-circle-icon bg-default"
                  ></v-file-input>
                </div>
              </div>
            </div>
            <div class="text-center">
              <div
                @click="previewCSV"
                class="image-preview"
                v-if="previewCSVUrl"
              >
                {{ previewCSVUrl ? previewCSVUrl : '' }}
              </div>
              <div
                @click="removeImage"
                v-if="previewCSVUrl"
                class="cursor-pointer font-14px fw-500 text-default mb-0 text-primary"
              >
                削除する
              </div>
            </div>
            <div v-if="errorCSVData?.error_count" class="text-center mt-2">
              <div>number of records: {{ errorCSVData?.total_records }}</div>
              <div>
                number of imported records: {{ errorCSVData?.imported_count }}
              </div>
              <div>
                number of issue records: {{ errorCSVData?.error_count }}
              </div>
            </div>
            <div
              @click="downloadError"
              v-if="errorCSVUrl"
              :class="{ 'px-2': smAndDown }"
              class="file-upload-information-container-error mx-auto"
            >
              <div
                class="cursor-pointer file-upload-information-error d-flex align-center justify-center font-14px my-2"
              >
                Download Error CSV
              </div>
            </div>

            <div
              :class="{ 'px-2': smAndDown }"
              class="file-upload-information-container mx-auto"
            >
              <a
                href="/template_student.csv"
                download="template_student.csv"
                style="text-decoration: none"
              >
                <div
                  class="cursor-pointer file-upload-information d-flex align-center justify-center font-14px"
                  :class="{ 'mt-4': !errorCSVData?.error_count }"
                >
                  サンプルCSVをダウンロード
                </div>
              </a>
            </div>
          </div>
        </div>
      </v-card-text>
      <v-card-actions class="justify-center">
        <v-btn
          v-if="!buttonOption.hideCancel"
          variant="outlined"
          color="primary"
          min-width="188"
          class="mr-6"
          @click.prevent="back()"
        >
          戻る
        </v-btn>
        <v-btn
          v-if="!buttonOption.hideSubmit"
          type="submit"
          variant="elevated"
          color="primary"
          class="white--text ml-5"
          min-width="188"
          @click.prevent="uploadToServer()"
          :loading="loading"
        >
          アップロード
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
  <!-- ModalUpload Dialog end -->
</template>
<script>
import { ref, computed } from 'vue';
import { useDisplay } from 'vuetify';

export default {
  name: 'ModalUpload',
  props: {
    errorCSV: {},
    dialog: {
      type: Boolean,
      default: false,
      required: true,
    },
    text: {
      type: [String, Array],
      default: '',
      required: true,
    },

    sentButtonText: {
      type: String,
      default: '戻る',
      required: false,
    },

    notSendButtonText: {
      type: String,
      default: '変更する',
      required: false,
    },

    loading: {
      type: Boolean,
      default: false,
      required: false,
    },

    buttonOption: {
      type: [Object],
      default: () => ({
        hideSubmit: false,
        hideCancel: false,
      }),
      required: false,
    },
  },
  setup(props, { emit }) {
    /**
     * Responsive Display Utilities
     * Used for responsive design adjustments
     */
    const { mdAndUp, smAndDown } = useDisplay();

    /**
     * State Management
     * Manages file upload and preview states
     */
    const reason = ref(null); // Stores reason for action
    const previewCSVUrl = ref(null); // Stores URL for CSV file preview
    const removeimage = ref(null); // Tracks image removal state (0: not removed, 1: removed)
    const fileCSV = ref(null); // Stores the CSV file object

    /**
     * Dialog Control
     * Two-way binding for dialog visibility with parent component
     */
    const selfDialog = computed({
      get: () => props.dialog, // Get dialog state from parent
      set: () => emit('closeModel', true), // Update parent's dialog state
    });

    /**
     * CSV Error Handling
     * Emits event to download error CSV file
     */
    const downloadError = () => {
      emit('downloadError');
    };

    /**
     * File Upload Handler
     * Processes CSV file upload and resets form state
     */
    const uploadToServer = () => {
      emit('submitSuccess', fileCSV.value); // Send file to parent
      // Reset all form states
      previewCSVUrl.value = null;
      removeimage.value = null;
      fileCSV.value = null;
    };

    /**
     * Dialog Navigation
     * Handles dialog closure
     */
    const back = () => {
      emit('closeModel');
    };

    /**
     * CSV Preview Handler
     * Creates preview URL for selected CSV file
     * @param {Event} event - File input change event
     */
    const previewCSV = (event) => {
      fileCSV.value = event.target.files[0]; // Store file object
      previewCSVUrl.value = URL.createObjectURL(fileCSV.value); // Create preview URL
      removeimage.value = 0; // Reset removal state
    };

    /**
     * File Removal Handler
     * Clears file preview and sets removal state
     */
    const removeImage = () => {
      previewCSVUrl.value = ''; // Clear preview URL
      removeimage.value = 1; // Set removal state
    };

    /**
     * Error Data Management
     * Computed properties for CSV error handling
     */
    const errorCSVData = computed(() => props.errorCSV); // Get error data from props
    const errorCSVUrl = computed(() => props.errorCSV?.error_file_content); // Get error file content

    return {
      reason,
      previewCSVUrl,
      removeimage,
      fileCSV,
      selfDialog,
      uploadToServer,
      back,
      previewCSV,
      removeImage,
      mdAndUp,
      smAndDown,
      downloadError,
      errorCSVUrl,
      errorCSVData,
    };
  },
};
</script>

<style lang="scss">
.file-input-box-container {
  max-width: 290px !important;
  .file-input-box {
    height: 134px;
    width: 100% !important;
    border: 1px dashed #13aba3;
    box-sizing: border-box;
    position: relative;
    border-radius: 5px;

    &.hide-borders {
      border: none;
    }
    &:hover {
      .hide-icons {
        position: relative;
        z-index: 100;
      }
    }
    .file-input-inr {
      position: relative;
      margin: auto;
      .file-circle-icon {
        :deep(.v-icon) {
          color: #13aba3 !important;
          font-size: 30px !important;
        }
        &.hide-icons {
          & * {
            width: 100%;
            height: 100%;
            opacity: 0;
            z-index: 2;
          }
        }
        &:hover {
          // Show Input select File icon on hover of the box
          :deep(.v-icon) {
            position: relative;
            z-index: 10;
          }
        }

        .v-input__prepend-outer {
          margin: auto;
          .v-icon {
            color: #fff !important;
          }
        }
      }

      &.hide-icons {
        width: 100%;
        height: 100%;
        background-color: transparent !important;
        h6 {
          visibility: hidden;
        }
        & * {
          background-color: transparent !important;
          width: 100%;
          height: 100%;
        }
      }
      .image-preview {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
  }
}
.file-upload-information-container {
  max-width: 290px !important;
  .file-upload-information {
    width: 100%;
    border-radius: 5px;
    height: 40px;
    background: #f9f9f9;
    color: #7d7e7e;
    text-align: center;
  }
}
.file-upload-information-container-error {
  max-width: 290px !important;
  .file-upload-information-error {
    width: 100%;
    border-radius: 5px;
    height: 40px;
    background: transparent;
    color: red !important;
    text-align: center;
  }
}

:deep(.v-label) {
  font-size: 14px;
}

.cursor-pointer {
  cursor: pointer;
}
</style>

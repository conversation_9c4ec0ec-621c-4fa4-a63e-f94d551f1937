<template>
  <div class="full-width">
    <v-row
      v-if="items"
      class="fw-500 align-center full-width heading-text px-0 mx-0 my-4"
    >
      <v-col cols="auto" class="d-flex align-center flex-wrap px-0 mx-0">
        <h2 class="font-24px mb-1 mr-2">
          <span class="fw-500">{{ items.title }}</span>
          <span class="line" v-if="items.subTitle"></span>
          <span class="font-20px fw-400" v-if="items.subTitle">{{
            items.subTitle
          }}</span>
          <span
            v-if="items.icon"
            cols="auto"
            class="d-flex align-center flex-wrap mx-2"
          >
            <v-icon size="small">${{ items.icon }}</v-icon>
          </span>
          <span class="line" v-if="items.student"></span>
          <span class="ml-2 font-20px" v-if="items.student">{{
            items.student
          }}</span>
          <span class="ml-2 font-18px" v-if="items.count"
            >({{ items.count }})</span
          >
        </h2>
      </v-col>
      <v-col
        class="d-flex justify-start align-center parent tab-items-parent ml-0 pl-0"
      >
        <template v-if="items.tabs && items.tabs.length > 0">
          <v-btn
            v-for="(tab, index) in items.tabs"
            :key="index"
            ref="tabRefs"
            :id="`tab-${index}`"
            variant="text"
            @click="tabAction(tab, index)"
            class="pa-0 mr-4"
          >
            {{ tab.title }} ({{ tab.count }})
            <div class="position-relative" v-if="tab.notification">
              <v-badge
                color="#E14D56"
                inline
                :content="tab.notification"
              ></v-badge>
            </div>
          </v-btn>
        </template>
      </v-col>
      <v-col
        cols="auto"
        class="d-flex font-14px justify-end align-center px-0 mx-0"
        :class="items.tabs && items.tabs.length > 5 ? 'full-width' : ''"
        v-if="items.buttons && items.buttons.length > 0"
      >
        <!-- if possible please check again -->
        <v-btn
          v-for="(button, index) in items.buttons"
          :key="index"
          elevation="4"
          :color="button.color || 'primary'"
          :class="button.class"
          :variant="button.variant"
          :min-width="188"
          :min-height="35"
          @click="button.action"
          v-bind="button.others"
          class="ml-5"
        >
          <div class="d-flex align-center">
            <v-icon v-if="button.icon" :size="15">{{ button.icon }}</v-icon>
            <span class="ml-1" v-if="button.icon">{{ button.title }}</span>
            <span v-else>{{ button.title }}</span>
          </div>
        </v-btn>
      </v-col>
      <v-col
        class="d-flex font-14px align-center justify-end"
        v-if="items.back"
      >
        <v-btn @click="items.back.action" variant="text" color="transparent">
          <v-icon>$ChevronLeft</v-icon>
          <span class="text-7d">{{ items.back.label || '戻る' }}</span></v-btn
        >
      </v-col>
    </v-row>
  </div>
</template>

<script>
import { ref, onMounted, nextTick } from 'vue';

export default {
  name: 'PageTitle',
  props: {
    items: {
      type: Object,
      required: true,
    },
  },
  setup(props) {
    const tabRefs = ref([]);

    const setInitialSelectedTab = async () => {
      await nextTick(); // Ensure the DOM is updated
      const alreadySelectedTab = props.items.tabs?.find((tab) => tab.selected);
      if (alreadySelectedTab) {
        const index = props.items.tabs.findIndex(
          (tab) => tab.title === alreadySelectedTab.title
        );
        if (tabRefs.value[index]?.$el) {
          tabRefs.value[index].$el.classList.add('text-primary');
        }
      } else if (tabRefs.value[0]?.$el) {
        tabRefs.value[0].$el.classList.add('text-primary');
      }
    };

    const tabAction = (tab, index) => {
      tab.action();

      // Safely iterate over tabRefs to avoid undefined elements
      tabRefs.value.forEach((ref, i) => {
        // Check if ref is a DOM element or a Vue component with an `el` property
        const element = ref?.$el || ref;

        if (element && element.classList) {
          if (i === index) {
            element.classList.add('text-primary');
          } else {
            element.classList.remove('text-primary');
          }
        }
      });
    };

    onMounted(setInitialSelectedTab);

    return {
      tabRefs,
      tabAction,
    };
  },
};
</script>

<style lang="scss">
.text-primary {
  color: #1976d2; /* Example color, adjust as needed */
}
</style>

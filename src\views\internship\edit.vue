<template>
  <div v-show="!isLoading && !$store.getters.getApiProcessingStatus">
    <Form @submit="submitInformation()">
      <PageTitle
        :items="{
          title: title,
          subTitle: subTitle,
          back: {
            action: () => {
              $router.push({
                name: 'InternshipPostList',
              });
            },
          },
        }"
      ></PageTitle>
      <v-row v-if="!isLoading">
        <v-col cols="8" class="pa-4">
          <InternshipBasicInformation
            :basicInformation="basicInformation"
            :singleInternship="getSingleInternship"
            :headings="headings"
          />
        </v-col>
        <v-col cols="4" class="pa-4">
          <v-card height="314px" class="text-center pt-14">
            <v-btn
              variant="outlined"
              color="primary"
              type="button"
              @click="openDraftPopup()"
              min-width="150px"
              width="259px"
              >下書き保存</v-btn
            >
            <br />
            <v-btn
              variant="outlined"
              color="primary"
              type="button"
              @click="savePreview()"
              min-width="150px"
              width="259px"
              class="mt-6"
              >プレビュー</v-btn
            >
            <br />
            <v-btn
              type="submit"
              color="#13ABA3"
              class="white--text mt-6"
              min-width="150px"
              width="259px"
              depressed
              >更新＆公開</v-btn
            >
            <br />
            <v-btn
              class="mt-8"
              @click="dialog.deletePost = true"
              variant="text"
              color="#E14D56"
            >
              削除
            </v-btn>
          </v-card>

          <v-card height="450px" class="pt-2 px-10 my-5">
            <!-- Header -->
            <div class="pt-6 text-center font-14px">連絡先</div>

            <!-- User 1 -->
            <v-row class="mt-4">
              <v-col cols="12" class="pt-0 mt-0">
                <label class="font-14px">企業管理ユーザ</label>
              </v-col>
              <v-col cols="1" class="pt-2 py-0 my-0 pr-0 mr-0 overflow-text">
                <label>1.</label>
              </v-col>
              <v-col cols="11" class="py-0 my-0">
                <!-- we need to manipulate in dom for init validation from JS -->
                <Field
                  v-if="!isShowFirstEmployee"
                  v-slot="{ field, errors }"
                  name="企業管理ユーザ1"
                  :rules="companyEditFields.rules"
                  :value="companyEditFields.contactable_user1_id"
                >
                  <v-select
                    v-bind="field"
                    density="compact"
                    variant="outlined"
                    single-line
                    class="w-100 small-label"
                    :model-value="companyEditFields.contactable_user1_id"
                    :items="formattedUsersAdmin"
                    :item-title="(item) => `${item.name} | ${item.email_valid ? item.email_invalid : item.email_invalid ? `${item.email_invalid} (未認証)`  : ''}`"
                    no-data-text="データがありません"
                    :item-value="(item) => item.id"
                    placeholder="選択してください"
                    :error-messages="errors"
                    :error="errors.length !== 0"
                    :hide-details="errors.length <= 0"
                    :class="{
                      'bg-input-disabled': !basicInformation[0]?.value,
                    }"
                    menu-icon="$greyExpansionDropdown"
                  >
                  </v-select>
                </Field>
                <Field
                  v-if="isShowFirstEmployee"
                  v-slot="{ field, errors }"
                  name="企業管理ユーザ1"
                  :rules="companyEditFields.rules"
                  :value="companyEditFields.contactable_user1_id"
                >
                  <v-select
                    v-bind="field"
                    density="compact"
                    variant="outlined"
                    single-line
                    class="w-100 small-label"
                    :model-value="companyEditFields.contactable_user1_id"
                    :items="formattedUsersAdmin"
                    :item-title="(item) => `${item.name} | ${item.email_valid ? item.email_valid : item.email_invalid ? `${item.email_invalid} (未認証)`  : ''}`"
                    no-data-text="データがありません"
                    :item-value="(item) => item.id"
                    placeholder="選択してください"
                    :error-messages="errors"
                    :error="errors.length !== 0"
                    :hide-details="errors.length <= 0"
                    :class="{
                      'bg-input-disabled': !basicInformation[0]?.value,
                    }"
                    menu-icon="$greyExpansionDropdown"
                  >
                  </v-select>
                </Field>
              </v-col>
            </v-row>

            <!-- User 2 -->
            <v-row class="mt-5">
              <v-col cols="12" class="pt-0 mt-0">
                <label class="font-14px">企業一般ユーザ</label>
              </v-col>
              <v-col cols="1" class="pt-2 py-0 my-0 pr-0 mr-0 overflow-text">
                <label>2.</label>
              </v-col>
              <v-col cols="10" class="py-0 my-0">
                <Field
                  v-slot="{ field: { value, ...fieldWithoutValue }, errors }"
                  :value="companyEditFields?.contactable_user2_id"
                  name="企業一般ユーザ2"
                >
                  <v-select
                    v-bind="fieldWithoutValue"
                    density="compact"
                    variant="outlined"
                    single-line
                    class="w-100 small-label"
                    v-model="companyEditFields.contactable_user2_id"
                    :items="formattedUsers"
                    :item-title="(item) => `${item.name} | ${item.email_valid ? item.email_valid : item.email_invalid ? `${item.email_invalid} (未認証)`  : ''}`"
                    no-data-text="データがありません"
                    :item-value="(item) => item.id"
                    placeholder="選択してください"
                    :error-messages="errors"
                    :error="errors.length !== 0"
                    :hide-details="errors.length <= 0"
                    :class="{
                      'bg-input-disabled': !basicInformation[0]?.value,
                    }"
                    menu-icon="$greyExpansionDropdown"
                  >
                  </v-select>
                </Field>
              </v-col>
              <v-col
                cols="1"
                class="d-flex justify-end py-0 my-0 pt-1 pointer ml-0 pl-0"
              >
                <v-icon
                  @click="companyEditFields.contactable_user2_id = null"
                  size="24px"
                  >$TrashSmall</v-icon
                >
              </v-col>
            </v-row>

            <!-- User 3 -->
            <v-row class="mt-5">
              <v-col cols="1" class="pt-2 py-0 my-0 pr-0 mr-0 overflow-text">
                <label>3.</label>
              </v-col>
              <v-col cols="10" class="py-0 my-0">
                <Field
                  v-slot="{ field: { value, ...fieldWithoutValue }, errors }"
                  :value="companyEditFields?.contactable_user3_id"
                  name="企業一般ユーザ3"
                >
                  <v-select
                    v-bind="fieldWithoutValue"
                    density="compact"
                    variant="outlined"
                    single-line
                    class="w-100 small-label"
                    v-model="companyEditFields.contactable_user3_id"
                    :items="formattedUsers"
                    :item-title="(item) => `${item.name} | ${item.email_valid ? item.email_valid : item.email_invalid ? `${item.email_invalid} (未認証)`  : ''}`"
                    no-data-text="データがありません"
                    :item-value="(item) => item.id"
                    placeholder="選択してください"
                    :error-messages="errors"
                    :error="errors.length !== 0"
                    :hide-details="errors.length <= 0"
                    :class="{
                      'bg-input-disabled': !basicInformation[0]?.value,
                    }"
                    menu-icon="$greyExpansionDropdown"
                  >
                  </v-select>
                </Field>
              </v-col>
              <v-col
                cols="1"
                class="d-flex justify-end py-0 my-0 pt-1 pointer ml-0 pl-0"
              >
                <v-icon
                  @click="companyEditFields.contactable_user3_id = null"
                  size="24px"
                  >$TrashSmall</v-icon
                >
              </v-col>
            </v-row>

            <!-- User 4 -->
            <v-row class="mt-5">
              <v-col cols="1" class="pt-2 py-0 my-0 pr-0 mr-0 overflow-text">
                <label>4.</label>
              </v-col>
              <v-col cols="10" class="py-0 my-0">
                <Field
                  v-slot="{ field: { value, ...fieldWithoutValue }, errors }"
                  :value="companyEditFields?.contactable_user4_id"
                  name="企業一般ユーザ4"
                >
                  <v-select
                    v-bind="fieldWithoutValue"
                    density="compact"
                    variant="outlined"
                    single-line
                    class="w-100 small-label"
                    v-model="companyEditFields.contactable_user4_id"
                    :items="formattedUsers"
                    :item-title="(item) => `${item.name} | ${item.email_valid ? item.email_valid : item.email_invalid ? `${item.email_invalid} (未認証)`  : ''}`"
                    no-data-text="データがありません"
                    :item-value="(item) => item.id"
                    placeholder="選択してください"
                    :error-messages="errors"
                    :error="errors.length !== 0"
                    :hide-details="errors.length <= 0"
                    :class="{
                      'bg-input-disabled': !basicInformation[0]?.value,
                    }"
                    menu-icon="$greyExpansionDropdown"
                  >
                  </v-select>
                </Field>
              </v-col>
              <v-col
                cols="1"
                class="d-flex justify-end py-0 my-0 pt-1 pointer ml-0 pl-0"
              >
                <v-icon
                  @click="companyEditFields.contactable_user4_id = null"
                  size="24px"
                  >$TrashSmall</v-icon
                >
              </v-col>
            </v-row>

            <!-- User 5 -->
            <v-row class="mt-5">
              <v-col cols="1" class="pt-2 py-0 my-0 pr-0 mr-0 overflow-text">
                <label>5.</label>
              </v-col>
              <v-col cols="10" class="py-0 my-0">
                <Field
                  v-slot="{ field: { value, ...fieldWithoutValue }, errors }"
                  :value="companyEditFields?.contactable_user5_id"
                  name="企業一般ユーザー5"
                >
                  <v-select
                    v-bind="fieldWithoutValue"
                    density="compact"
                    variant="outlined"
                    single-line
                    class="w-100 small-label"
                    v-model="companyEditFields.contactable_user5_id"
                    :items="formattedUsers"
                    :item-title="(item) => `${item.name} | ${item.email_valid ? item.email_valid : item.email_invalid ? `${item.email_invalid} (未認証)`  : ''}`"
                    no-data-text="データがありません"
                    :item-value="(item) => item.id"
                    placeholder="選択してください"
                    :error-messages="errors"
                    :error="errors.length !== 0"
                    :hide-details="errors.length <= 0"
                    :class="{
                      'bg-input-disabled': !basicInformation[0]?.value,
                    }"
                    menu-icon="$greyExpansionDropdown"
                  >
                  </v-select>
                </Field>
              </v-col>
              <v-col
                cols="1"
                class="d-flex justify-end py-0 my-0 pt-1 pointer ml-0 pl-0"
              >
                <v-icon
                  @click="companyEditFields.contactable_user5_id = null"
                  size="24px"
                  >$TrashSmall</v-icon
                >
              </v-col>
            </v-row>
            <v-row
              class="d-flex align-center justify-end mt-8 cursor-pointer"
              @click="openNewTab()"
            >
              <v-col
                cols="12"
                class="py-0 my-0 d-flex align-center justify-end"
              >
                <span class="font-14px text-primary mr-2">企業ユーザ管理</span>
                <span
                  aria-hidden="true"
                  class="v-icon notranslate theme--light error--text"
                >
                  <v-icon size="24px">$ChevronRightSmall</v-icon>
                </span>
              </v-col>
            </v-row>
          </v-card>

          <v-card min-height="106px" class="my-5">
            <div class="text-center pt-6">ステータス変更</div>
            <div class="d-flex justify-center">
              <div
                class="mt-3 pl-1"
                v-for="item in internshipPostStatusList"
                :key="item.id"
              >
                <div v-if="internshipPostStatus == item.id">
                  <v-btn
                    @click="internshipPostStatus = item.id"
                    rounded
                    width="72.6px"
                    height="20px"
                    depressed
                    color="#E3D129"
                    class="font-12px white--text"
                    >{{ item.title }}
                  </v-btn>
                </div>
                <div v-else>
                  <v-btn
                    @click="internshipPostStatus = item.id"
                    rounded
                    width="72.6px"
                    height="20px"
                    outlined
                    color="#7D7D7D"
                    class="font-12px"
                    >{{ item.title }}
                  </v-btn>
                </div>
              </div>
            </div>
          </v-card>

          <v-card
            class="d-flex align-center justify-center mb-5"
            min-height="106px"
          >
            <div class="text-7d font-18px">
              <v-icon color="#D25CA1" class="mb-1">$HeartIcon</v-icon>
              <span class="pt-2 ml-2">お気に入り</span>
              <span class="ml-2">{{ numberOfFavourites }}</span>
            </div>
            <div class="text-7d font-18px ml-12">
              <v-icon color="#5AB8ED" class="mb-1">$PaperPlaneIcon</v-icon>
              <span class="pt-2 ml-2">応募</span>
              <span class="ml-2">{{ numberOfApplications }}</span>
            </div>
          </v-card>

          <ImageUpload :data="imageDetails" />
        </v-col>
      </v-row>
    </Form>

    <SimpleModel
      text="この求人を下書きとして保存しますか？"
      :dialog="dialog.saveAsDraft"
      @submitSuccess="submitInformation('Y')"
      @closeModel="dialog.saveAsDraft = false"
    ></SimpleModel>

    <SimpleModel
      text="この求人を削除しますか？"
      :dialog="dialog.deletePost"
      :submitButtonText="'削除する'"
      @submitSuccess="deleteInformation()"
      @closeModel="dialog.deletePost = false"
    ></SimpleModel>

    <InterPreviewModel
      :dialog="dialog.preview"
      @submitSuccess="preview()"
      @closeModel="dialog.preview = false"
    ></InterPreviewModel>
    <SuccessModel
      :text="alertText"
      :buttonText="buttonText"
      :routeName="routeName"
      :dialog="successDialog"
      @closeModel="successDialog = false"
    ></SuccessModel>
  </div>
</template>

<script>
import {
  ref,
  computed,
  onMounted,
  watch,
  nextTick,
  defineAsyncComponent,
} from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { useForm, Field, Form, ErrorMessage } from 'vee-validate';
import * as yup from 'yup';
const InternshipBasicInformation = defineAsyncComponent(
  () => import('@/components/pages/PostInputs.vue')
);
const ImageUpload = defineAsyncComponent(
  () => import('@/components/ui/ImageUpload.vue')
);
const SimpleModel = defineAsyncComponent(
  () => import('@/components/models/SimpleModel.vue')
);
const InterPreviewModel = defineAsyncComponent(
  () => import('@/components/models/InterPreviewModel.vue')
);
const SuccessModel = defineAsyncComponent(
  () => import('@/components/models/SuccessModel.vue')
);
import InternshipMixins from './internship.mixin';

export default {
  name: 'InternshipPostEdit',
  components: {
    InternshipBasicInformation,
    ImageUpload,
    SimpleModel,
    InterPreviewModel,
    SuccessModel,
  },
  setup() {
    const store = useStore();
    const {
      basicInformation,
      imageDetails,
      savePageData,
      getSingleInternship,
      getPageFields,
      getApiProcessingStatus,
      saveAsDraft,
      isDraftOrPublic,
      setFormDataFromServer,
      successDialog,
      dialog,
      routeName,
      alertText,
      buttonText,
      companyEditFields,
      setRequired,
    } = InternshipMixins();

    const router = useRouter();
    const title = ref('求人広告');
    const subTitle = ref('編集');
    let numberOfFavourites = ref(0);
    let numberOfApplications = ref(0);
    let internshipPostStatus = ref('1');
    let internshipPostStatusList = ref([
      { title: '募集中', id: '0' },
      { title: '募集終了', id: '1' },
    ]);
    const headings = ref({
      heading1: 3,
      heading2: 4,
    });
    let postStatus = ref('0');
    let isLoading = ref(false);

    /**
     * Component initialization
     * Fetches initial data and sets loading state
     */
    onMounted(async () => {
      isLoading.value = true;
      await getDataFromApi();
    });

    /**
     * Fetches company users based on selected company ID
     * Used to populate contact user dropdowns
     */
    const fetchCompanyUsers = async () => {
      if (companyId.value) {
        await store.dispatch('COMPANY_USERS_GET_ALL', {
          company_id: companyId.value,
        });
      }
    };

    /**
     * Computed properties for accessing and formatting company users
     */
    const getAllCompanyUsers = computed(() => store.getters.getAllCompanyUsers);

    /**
     * Formats and filters admin users (type 1) for the company
     * Used for primary contact selection
     */
    const formattedUsersAdmin = computed(() => {
      return (
        getAllCompanyUsers.value
          ?.map((user) => ({
            name: `${user.surname} ${user.name}`,
            email_valid: user.email_valid,
            email_invalid: user.email_invalid,
            id: user.id,
            isCompany: user.company_id === basicInformation.value[0]?.value,
            type: user.type_user,
          }))
          .filter((user) => user.isCompany && user.type === 1) || []
      );
    });

    /**
     * Formats and filters all company users
     * Used for secondary contact selections
     */
    const formattedUsers = computed(() => {
      return (
        getAllCompanyUsers.value
          ?.map((user) => ({
            name: `${user.surname} ${user.name}`,
            email_valid: user.email_valid,
            email_invalid: user.email_invalid,
            id: user.id,
            isCompany: user.company_id === basicInformation.value[0]?.value,
            type: user.type_user,
          }))
          .filter((user) => user.isCompany) || []
      );
    });

    /**
     * Finds the first admin user (type 1) in the company
     * Used for default primary contact
     */
    const firstUserWithType1 = computed(() =>
      formattedUsers.value.find((user) => user.type === 1)
    );

    /**
     * Gets selected company ID from basic information
     */
    const companyId = computed(() => basicInformation.value[0]?.value);

    /**
     * Watches for company changes to update user lists
     * Sets default primary contact when company changes
     */
    const isShowFirstEmployee = ref(false);
    watch(companyId, async () => {
      isShowFirstEmployee.value = true;
      companyEditFields.value.contactable_user1_id = null;
      await fetchCompanyUsers();
      await nextTick(); // Ensure DOM updates

      if (formattedUsers.value.length > 0 && firstUserWithType1.value) {
        companyEditFields.value.contactable_user1_id =
          firstUserWithType1?.value?.id || '';
        setTimeout(() => {
          companyEditFields.value.contactable_user1_id =
            firstUserWithType1?.value?.id || '';
          isShowFirstEmployee.value = false;
        }, 500);
      }
    });

    /**
     * Fetches internship data from API and initializes form
     * Handles loading states and data population
     */
    const getDataFromApi = async () => {
      await getPageFields();

      await store
        .dispatch('INTERNSHIP_GET', { id: router.currentRoute.value.params.id })
        .then(() => {
          setPageData();
          isLoading.value = true;
          setFormDataFromServer(getSingleInternship);
          setTimeout(() => {
            isLoading.value = false;
          }, 200);
        });
    };

    /**
     * Populates form fields with internship data
     * Handles special field types and formatting
     */
    const setPageData = () => {
      basicInformation.value.forEach((item) => {
        // Handle required item prompt fields
        if (
          item.additional_field !== undefined &&
          item.additional_field.name === 'required_item_prompt'
        ) {
          item.additional_field.value =
            getSingleInternship.value.required_item_prompt;
        }

        // Handle required URL prompt fields
        if (
          item.additional_field !== undefined &&
          item.additional_field.name === 'required_url_prompt'
        ) {
          item.additional_field.value =
            getSingleInternship.value.required_url_prompt;
        }

        // Handle display order selection
        if (item.name == 'display_order_select') {
          if (getSingleInternship.value?.display_order) {
            item.value = 1;
            item.additional_field.value =
              getSingleInternship.value.display_order;
          }
        }
        // Handle internship features
        else if (item.name == 'internship_feature_id') {
          item.value = getSingleInternship.value.internship_feature_posts
            ? getSingleInternship.value.internship_feature_posts.map(
                (i) => i.id
              )
            : [];
        }
        // Handle company selection
        else if (item.name == 'company_id') {
          item.items = [getSingleInternship.value?.company];
          item.value = getSingleInternship.value[item.name];
        }
        // Handle general fields
        else if (getSingleInternship.value?.[item.name]) {
          item.value = getSingleInternship.value[item.name];
          // Format wage with thousands separator
          if (item.name == 'wage') {
            item.value = item.value.toLocaleString('en-US');
          }
        }

        // Handle boolean flags (convert 1/0 to true/false)
        if (item.name === 'flg_required_item') {
          item.value = getSingleInternship.value.flg_required_item === 1;
        }
        if (item.name === 'flg_required_qualification') {
          item.value =
            getSingleInternship.value.flg_required_qualification === 1;
        }
        if (item.name === 'flg_required_resume') {
          item.value = getSingleInternship.value.flg_required_resume === 1;
        }
        if (item.name === 'flg_required_self_introduction') {
          item.value =
            getSingleInternship.value.flg_required_self_introduction === 1;
        }
        if (item.name === 'flg_required_url') {
          item.value = getSingleInternship.value.flg_required_url === 1;
        }
      });

      // Set post status and counts
      postStatus.value = `${getSingleInternship.value?.draft_or_public}`;
      internshipPostStatus.value = `${getSingleInternship.value?.status}`;
      numberOfApplications.value =
        getSingleInternship.value?.applications_count;
      numberOfFavourites.value = getSingleInternship.value?.favorites_count;

      // Update image details
      imageDetails.value.previewImageURL =
        getSingleInternship.value?.seo_featured_image;
      imageDetails.value.seo_ogp.value = getSingleInternship.value?.seo_ogp;
      imageDetails.value.seo_slug.value = getSingleInternship.value?.seo_slug;
      imageDetails.value.seo_meta_description.value =
        getSingleInternship.value?.seo_meta_description;
      imageDetails.value.removeimage = 0;

      // Set contact user IDs
      companyEditFields.value.contactable_user5_id =
        getSingleInternship.value?.contactable_user5_id;
      companyEditFields.value.contactable_user4_id =
        getSingleInternship.value?.contactable_user4_id;
      companyEditFields.value.contactable_user3_id =
        getSingleInternship.value?.contactable_user3_id;
      companyEditFields.value.contactable_user2_id =
        getSingleInternship.value?.contactable_user2_id;
      companyEditFields.value.contactable_user1_id =
        getSingleInternship.value?.contactable_user1_id;
    };

    /**
     * Handles form submission for both draft and publish
     * @param {string} isDraftCheck - 'Y' for draft, 'N' for publish
     */
    const submitInformation = (isDraftCheck = 'N') => {
      isDraftOrPublic.value = '0';
      if (isDraftCheck === 'N') {
        setRequired();
      }

      // Add status to basic information
      basicInformation.value.push({
        name: 'status',
        value: internshipPostStatus.value,
      });

      savePageData('update');
      dialog.value.saveAsDraft = false;
    };

    /**
     * Handles internship deletion
     * Shows success message and redirects to list
     */
    const deleteInformation = () => {
      dialog.value.deletePost = false;
      store
        .dispatch('INTERNSHIP_DELETE', {
          id: router.currentRoute.value.params.id,
        })
        .then(() => {
          alertText.value = '求人を削除しました。';
          routeName.value = `InternshipPostList`;
          successDialog.value = true;
        });
    };

    /**
     * Opens draft confirmation dialog
     */
    const openDraftPopup = () => {
      dialog.value.saveAsDraft = true;
    };

    /**
     * Opens preview dialog
     */
    const savePreview = () => {
      dialog.value.preview = true;
    };

    const openNewTab = () => {
      const routeData = router.resolve({
        name: 'CorporateDetails',
        params: { id: basicInformation.value[0]?.value },
      });
      window.open(routeData.href, '_blank');
    };

    return {
      title,
      subTitle,
      basicInformation,
      imageDetails,
      numberOfFavourites,
      numberOfApplications,
      internshipPostStatus,
      internshipPostStatusList,
      headings,
      dialog,
      alertText,
      buttonText,
      successDialog,
      postStatus,
      isDraftOrPublic,
      submitInformation,
      deleteInformation,
      openDraftPopup,
      savePreview,
      getSingleInternship,
      getApiProcessingStatus,
      isLoading,
      imageDetails,
      saveAsDraft,
      routeName,
      dialog,
      companyEditFields,
      formattedUsers,
      formattedUsersAdmin,
      openNewTab,
      isShowFirstEmployee,
    };
  },
};
</script>

<style lang="scss" src="./style.scss"></style>

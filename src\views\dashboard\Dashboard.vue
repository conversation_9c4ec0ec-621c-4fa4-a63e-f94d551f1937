<template>
  <div class="font-Noto-Sans dashboard-page">
    <v-sheet color="transparent">
      <v-row v-if="!isLoading">
        <v-col
          cols="12"
          class="d-flex flex-wrap align-center justify-space-between w-100 mt-5"
        >
          <div class="fw-500 w-auto d-flex flex-wrap align-center my-auto">
            <h2 class="text-h5 mb-1 mr-4">{{ $t('dashboard.top') }}</h2>
            <div
              class="d-flex text-body-2 mr-6 align-center font-weight-medium"
            >
              <span
                class="d-inline-block mr-2 bg-green rounded-xl py-2 px-4 white--text font-Noto-Sans"
              >
                登録学生数
              </span>
              <span class="d-inline-block font-weight-medium">
                {{ getAllDashboard ? getAllDashboard.allStudents : 0 }}
              </span>
            </div>
            <div
              class="d-flex text-body-2 mr-6 align-center font-weight-medium"
            >
              <span
                class="d-inline-block mr-2 bg-green rounded-xl py-2 px-4 white--text font-Noto-Sans"
              >
                企業数
              </span>
              <span class="d-inline-block font-weight-medium">
                {{ getAllDashboard ? getAllDashboard.allCompanies : 0 }}
              </span>
            </div>
            <div
              class="d-flex text-body-2 mr-6 align-center font-weight-medium"
            >
              <span
                class="d-inline-block mr-2 bg-green rounded-xl py-2 px-4 white--text font-Noto-Sans"
              >
                募集中求人広告数
              </span>
              <span class="d-inline-block font-weight-medium">
                {{
                  getAllDashboard ? getAllDashboard.numberInternshipStudents : 0
                }}
              </span>
            </div>
          </div>
          <div class="dashboard-top-right w-auto my-auto">
            <div
              class="d-flex flex-wrap align-center text-light-dark font-weight-medium text-body-2"
            >
              <span class="mr-5 mb-auto font-Noto-Sans">最終更新</span>
              <span class="text-right">
                <span class="d-block">
                  {{ moment().format('YYYY/MM/DD H:mm') }}
                </span>
              </span>
            </div>
            <div
              style="margin-right: -7px"
              class="font-10px text-b8b8b8 text-right"
            >
              （毎日1:00更新）
            </div>
          </div>
        </v-col>
        <!-- chart row 1 -->
        <v-col cols="12" md="6">
          <v-col cols="12" class="d-flex w-100 my-0 mx-0 px-0 py-0 mb-6">
            <v-card
              style="height: 400px"
              class="py-5 px-5 rounded d-flex flex-column"
            >
              <v-card-title class="pr-7 pl-5 py-0 text-h6">
                <div
                  class="d-flex justify-space-between align-center full-width"
                >
                  <div class="text-light-dark font-18px font-Noto-Sans">
                    応募数 / 合格者数
                  </div>
                  <div class="d-flex align-center">
                    <div class="d-flex align-center mr-6">
                      <div class="application-square mr-2"></div>
                      <div class="font-12px text-555555">応募総数(月)</div>
                    </div>
                    <div class="d-flex align-center">
                      <div class="contract-square mr-2"></div>
                      <div class="font-12px text-555555">合格者総数(月)</div>
                    </div>
                  </div>
                </div>
              </v-card-title>
              <v-card-text>
                <div
                  v-if="
                    (getAllDashboard &&
                      getAllDashboard?.applicationsEachMonth) ||
                    (getAllDashboard && getAllDashboard?.contractEachMonth)
                  "
                  class="d-flex align-center"
                >
                  <LineChart
                    :key="refreshDoughnutComponent"
                    :graph-data="getAllDashboard"
                  />
                </div>
              </v-card-text>
            </v-card>
          </v-col>
          <v-col cols="12 my-0 mx-0 px-0 py-0">
            <div class="d-flex my-0 mx-0 px-0 py-0">
              <!-- chart row 2 -->
              <v-col cols="6" class="d-flex w-100 my-0 ml-0 pl-0 py-0">
                <v-card class="py-5 px-5 rounded d-flex flex-column">
                  <v-card-title class="px-0 py-0 text-h6">
                    <span class="text-light-dark font-14px font-Noto-Sans"
                      >稼働中インターン生数</span
                    >
                  </v-card-title>
                  <v-card-text>
                    <div
                      v-if="
                        (getAllDashboard &&
                          getAllDashboard?.applicationsEachMonth) ||
                        (getAllDashboard &&
                          getAllDashboard?.internshipStudentsEachMonth)
                      "
                      class="d-flex align-center"
                    >
                      <BarChart
                        :key="refreshDoughnutComponent"
                        :graph-data="
                          getAllDashboard.internshipStudentsEachMonth
                        "
                      />
                    </div>
                  </v-card-text>
                </v-card>
              </v-col>
              <v-col cols="6" class="d-flex w-100 my-0 mr-0 pr-0 py-0">
                <v-card class="py-5 px-5 rounded d-flex flex-column">
                  <v-card-title class="px-0 py-0 text-h6">
                    <span class="text-light-dark font-14px font-Noto-Sans"
                      >月間登録企業数</span
                    >
                  </v-card-title>
                  <v-card-text>
                    <div
                      v-if="
                        (getAllDashboard &&
                          getAllDashboard?.applicationsEachMonth) ||
                        (getAllDashboard && getAllDashboard?.activeCompanies)
                      "
                      class="d-flex align-center"
                    >
                      <BarChart2
                        :key="refreshDoughnutComponent"
                        :graph-data="getAllDashboard.activeCompanies"
                      />
                    </div>
                  </v-card-text>
                </v-card>
              </v-col>
            </div>
          </v-col>
        </v-col>

        <!-- Doughnut -->
        <v-col cols="12" md="3" class="d-flex w-100">
          <v-card class="w-100 py-5 px-5 rounded d-flex flex-column">
            <v-card-title class="px-0 py-0 text-h6">
              <span class="text-light-dark font-Noto-Sans font-18px"
                >卒業予定年度</span
              >
            </v-card-title>
            <v-card-text>
              <DoughnutChart
                :key="refreshDoughnutComponent"
                :showLabels="true"
                :labels="doughnutChartLabelsGraduation"
              >
              </DoughnutChart>
            </v-card-text>
          </v-card>
        </v-col>

        <v-col cols="12" md="3" class="d-flex w-100">
          <v-card class="w-100 py-5 px-5 rounded d-flex flex-column">
            <v-card-title class="px-0 py-0 text-h6">
              <span class="text-light-dark font-Noto-Sans font-18px"
                >専攻分野</span
              >
            </v-card-title>
            <v-card-text>
              <DoughnutChart
                :key="refreshDoughnutComponent"
                :showLabels="true"
                :labels="doughnutChartLabelsStudy"
              >
              </DoughnutChart>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-sheet>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, defineAsyncComponent } from 'vue';
import { useStore } from 'vuex';
const DoughnutChart = defineAsyncComponent(
  () => import('@/components/charts/DoughnutChart.vue')
);
const LineChart = defineAsyncComponent(
  () => import('@/components/charts/LineChart.vue')
);
const BarChart = defineAsyncComponent(
  () => import('@/components/charts/BarChart.vue')
);
const BarChart2 = defineAsyncComponent(
  () => import('@/components/charts/BarChart2.vue')
);
import moment from 'moment';

/**
 * Store and reactive state initialization
 */
const store = useStore();
const refreshDoughnutComponent = ref(1); // Trigger chart re-render
const isLoading = ref(false); // Loading state for API calls

/**
 * Computed properties for accessing Vuex store data
 */
const getAllDashboard = computed(() => store.getters.getAllDashboard);
const user = computed(() => store.getters.user);

/**
 * Computed property for graduation year doughnut chart
 * Maps student graduation data to chart format with color coding
 * @returns {Array<Object>} Array of formatted data for doughnut chart
 */
const doughnutChartLabelsGraduation = computed(() => {
  // Color palette for different graduation years
  const colorPalette = [
    '#FF858E',
    '#FECE45',
    '#F8EA57',
    '#AAC863',
    '#4FA072',
    '#11ABA3',
    '#00AEE0',
    '#4784BF',
    '#5D5099',
    '#AA158B',
    '#DC669B',
  ];

  // Map graduation year data to chart format
  return getAllDashboard.value?.studentGraduateEachYear?.map((item, index) => ({
    color: colorPalette[index],
    percentage: item.total, // Total students for this year
    text: item.graduate_year, // Graduation year
    price: item.total, // Used for display
  }));
});

/**
 * Computed property for study field doughnut chart
 * Maps student field of study data to chart format with color coding
 * @returns {Array<Object>} Array of formatted data for doughnut chart
 */
const doughnutChartLabelsStudy = computed(() => {
  // Color palette for different study fields
  const colorPalette = [
    '#FF858E',
    '#FECE45',
    '#F8EA57',
    '#AAC863',
    '#4FA072',
    '#11ABA3',
    '#00AEE0',
    '#4784BF',
    '#5D5099',
    '#AA158B',
    '#DC669B',
  ];

  // Map study field data to chart format
  return getAllDashboard.value?.studentsPerBigField?.map((item, index) => ({
    color: colorPalette[index],
    percentage: item.students_count, // Number of students in field
    text: item.big_field_name, // Field name
    price: item.students_count, // Used for display
  }));
});

/**
 * Fetches dashboard data from API
 * Updates loading state and triggers chart refresh
 */
const getDataFromApi = async () => {
  await store.dispatch('DASHBOARDS_GET_ALL', {
    id: user.value?.id ?? 0, // Current user ID or 0
    sort_by_order: 'desc', // Sort order for data
    sort_by: 'applications_count', // Sort field
  });
  isLoading.value = false; // Hide loading indicator
  refreshDoughnutComponent.value++; // Force chart re-render
};

/**
 * Component initialization
 * Sets loading state and fetches initial data
 */
onMounted(() => {
  isLoading.value = true; // Show loading indicator
  getDataFromApi(); // Fetch dashboard data
});
</script>

<style src="./Dashboard.scss" lang="scss" />

.student-profile {
  .report-table.v-data-table {
    box-shadow: none !important;
    border-radius: 0% !important;

    table tbody {
      tr:hover {
        background-color: transparent !important;
      }
    }
    tr:last-of-type {
      td {
        border-bottom: 1px solid #e5e5e5 !important;
      }
    }
  }
  .application-table .v-data-table {
    box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25);
    table {
      tbody {
        tr {
          td {
            font-weight: 400;
          }
        }
      }
    }
  }

  .elevation {
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  }
  .border-radius {
    border-radius: 5px;
  }
  .sidebar {
    height: 585px;
    width: 423px;
  }
  .h-585 {
    max-height: 585px;
  }
  .mh-585 {
    min-height: 585px !important;
  }
  .h-835 {
    max-height: 875px;
  }
  .mh-835 {
    min-height: 875px !important;
  }
  .form {
    width: 100%;
  }
  .mb-82 {
    margin-bottom: 82px;
  }
  .input-width {
    width: 140px;
  }
  .main {
    width: 709px;
    #details {
      max-width: 291px;
      width: 291px;
    }
    #description {
      width: 303px;
    }
  }
}
.v-expansion-panel {
  border-bottom: 1px solid #e5e5e5;
}
.v-expansion-panel--active {
  padding-bottom: 10px!important;
}

.panel-content {
  .v-expansion-panel-content__wrap {
    padding-left: 0px;
    padding-bottom: 30px;
  }
}
.feedback-description {
  white-space: pre-line;
}

.v-expansion-panels:not(.v-expansion-panels--variant-accordion) > :last-child:not(:first-child) {
  border: none;
}

.empty-item {
  border: 1px dashed #ccc;
  text-align: center;
  padding: 10px;
  color: #666;
  font-size: 14px;
}

.container-report {
  background-color: #f5fefd;
  border-radius: 14px;
  padding: 24px;
  width: 600px;
  font-family: 'Helvetica', sans-serif;
  color: #333!important;
}

.row-report {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e0e0e0;
}

.label-report {
  font-size: 14px;
}

.value-report {
  font-size: 14px;
  font-weight: 500;
}

.v-expansion-panel-title__icon {
  display: none!important;
}

.rotate-icon {
  margin-bottom: 6px;
  transform: rotate(180deg) translateY(-5px);
}

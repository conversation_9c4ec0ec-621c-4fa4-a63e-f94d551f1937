import { ref, reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useStore } from 'vuex';
import { useForm } from 'vee-validate';

export default function useMediaPost() {
  const router = useRouter();
  const route = useRoute();
  const store = useStore();

  const { resetForm, setFieldError, validate } = useForm();

  const imageDetails = ref({
    previewImageURL: null,
    logoImage: null,
    seo_ogp: { value: '', rules: '' },
    seo_slug: { value: '', rules: 'required|slug' },
    seo_meta_description: { value: '', rules: '' },
  });

  const mediaTags = reactive({
    value: [],
  });

  const columnInformation = ref([]);
  const isDraftOrPublic = ref(1); // public post
  const dialog = reactive({
    saveAsDraft: false,
    deletePost: false,
    preview: false,
  });
  const isSaved = ref(null);

  /**
   * Scrolls to the first error element in the form
   * Used for better UX when validation fails
   */
  const scrollToTheFirstErrorInput = () => {
    const firstError = document.querySelector('.error');
    if (firstError) {
      firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  };

  /**
   * Opens draft confirmation dialog after form validation
   * Prevents opening dialog if validation fails
   */
  const openDraftPopup = async () => {
    const isValid = await validate();
    if (!isValid.valid) {
      scrollToTheFirstErrorInput();
      return;
    }
    dialog.saveAsDraft = true;
  };

  /**
   * Initiates save as draft process
   * 'Y' parameter indicates draft status
   */
  const saveAsDraft = () => {
    submitMediaPost('Y');
  };

  /**
   * Saves media post data to the server
   * Handles both create and update operations
   * @param {string} isCreateOrUpdate - 'create' for new post, 'update' for existing
   * @param {Function} action - Callback function after successful save
   */
  const savePageData = async (isCreateOrUpdate, action = null) => {
    // Validate form before proceeding
    const isValid = await validate();
    console.log(isValid);
    if (!isValid.valid) {
      scrollToTheFirstErrorInput();
      return;
    }

    const data = new FormData();

    // Determine API action based on operation type
    let SET_CALL = 'MEDIA_POST_CREATE';
    if (isCreateOrUpdate === 'update' || isSaved.value !== null) {
      SET_CALL = 'MEDIA_POST_UPDATE';
      data.append('id', route.params.id ?? isSaved.value);
      if (imageDetails.value.removeimage) {
        data.append('clear_image', 1);
      }
    }

    // Append SEO metadata
    data.append(
      'seo_meta_description',
      imageDetails.value.seo_meta_description.value ?? ''
    );
    data.append('seo_ogp', imageDetails.value.seo_ogp.value ?? '');
    data.append('seo_slug', imageDetails.value.seo_slug.value ?? null);
    data.append('draft_or_public', isDraftOrPublic.value ?? '');
    if (imageDetails.value.logoImage) {
      data.append('seo_featured_image', imageDetails.value.logoImage);
    }
    data.append('remove_image', imageDetails.value.removeimage);

    // Append media tags if present
    if (mediaTags.value.length > 0) {
      mediaTags.value.forEach((item) => {
        data.append('media_tag_id[]', item);
      });
    }

    // Append status and featured image
    data.append('status', 1);
    if (imageDetails.value.logoImage) {
      data.append('seo_featured_image', imageDetails.value.logoImage);
    }

    // Append form fields and their additional fields
    columnInformation.value.forEach((field) => {
      if (field.name && field.value) {
        if (Object.prototype.hasOwnProperty.call(field, 'additional_field')) {
          data.append(
            field.additional_field.name,
            field.additional_field.value
          );
        }
        data.append(field.name, field.value);
      }
    });

    try {
      // Submit data to API
      const result = await store.dispatch(SET_CALL, data);
      if (result.status === 200) {
        resetForm();
      }
      if (action) {
        action(result);
      }
    } catch (error) {
      // Handle validation errors from API
      if (error?.status === 422) {
        const errors = error?.data?.error?.errors || error?.data?.errors;
        Object.keys(errors).forEach((key) => {
          setFieldError(key, errors[key][0]);
        });
        scrollToTheFirstErrorInput();
      }
    }
  };

  /**
   * Navigates back to media list after successful save
   */
  const saveAction = () => {
    router.push({ name: 'Media' });
  };

  const getPageFields = () => {
    columnInformation.value = [
      {
        label: 'タイトル',
        name: 'title',
        type: 'text',
        placeholder: 'タイトルを入力してください',
        value: '',
        rules: 'required|max:60',
        counter: true,
        counterValue: 60,
      },
      {
        label: '記事のポイント',
        name: 'summery',
        type: 'textarea',
        placeholder: '記事のポイント',
        value: '',
        rules: '',
      },
      {
        label: '表示順',
        name: 'display_order_select',
        type: 'dropdown',
        items: [
          { id: 0, name: '設定しない' },
          { id: 1, name: '設定する' },
        ],
        item_value: 'id',
        item_text: 'name',
        value: 0,
        rules: '',
        additional_field: {
          label: '',
          name: 'display_order',
          type: 'text',
          placeholder: '1からの数字を入力してください',
          value: '',
          rules: 'required|min_value:1',
          requiredChecks: true,
          visibility_check: true,
          visible_value: '0',
          col_class_right: 'pl-3',
        },
      },
      {
        label: '記事内容',
        name: 'description',
        type: 'richbox',
        placeholder: '',
        value: '',
        rules: '',
        height: '400px',
      },
    ];
  };

  onMounted(() => {
    getPageFields();
  });

  return {
    imageDetails,
    mediaTags,
    columnInformation,
    isDraftOrPublic,
    dialog,
    isSaved,
    openDraftPopup,
    saveAsDraft,
    savePageData,
    saveAction,
    getPageFields,
  };
}

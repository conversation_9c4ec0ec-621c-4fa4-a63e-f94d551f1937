<template>
  <div class="chart-parent mb-5 mt-8">
    <canvas ref="chart"></canvas>
  </div>
</template>

<script>
import Chart from 'chart.js/auto';
import 'chartjs-adapter-moment';

export default {
  mounted() {
    this.initBarChart();
  },
  props: {
    graphData: {
      type: Object,
      required: false,
    },
  },
  data() {
    return {
      gradient1: null,
      gradient2: null,
    };
  },
  methods: {
    initBarChart() {
      this.$nextTick(function () {
        const ctx = this.$refs.chart.getContext('2d');

        // Gradient for the first dataset
        this.gradient1 = ctx.createLinearGradient(0, 0, 0, 120);
        this.gradient1.addColorStop(0, 'rgba(251, 203, 241, 1)');
        this.gradient1.addColorStop(0.5, 'rgba(251, 203, 241, 0.5)');
        this.gradient1.addColorStop(1, 'rgba(255, 255, 255, 0.5)');

        // Gradient for the second dataset
        this.gradient2 = ctx.createLinearGradient(0, 0, 0, 120);
        this.gradient2.addColorStop(0, 'rgba(173, 216, 230, 1)'); // Light Blue
        this.gradient2.addColorStop(0.5, 'rgba(173, 216, 230, 0.5)');
        this.gradient2.addColorStop(1, 'rgba(255, 255, 255, 0.5)');

        new Chart(this.$refs.chart, {
          type: 'bar',
          data: {
            labels: this.graphData.applications.map((item) => item.date),
            datasets: [
              {
                label: 'Applications',
                data: this.graphData.applications.map(
                  (item) => item.application_count
                ),
                backgroundColor: this.gradient1,
                borderColor: '#AA158B',
                borderWidth: 1,
              },
              {
                label: 'Dummy Data',
                data: this.graphData.applications.map(
                  (item, index) => item.application_count + index * 2
                ), // Dummy data for the second dataset
                backgroundColor: this.gradient2,
                borderColor: '#0000FF',
                borderWidth: 1,
              },
            ],
          },
          options: {
            interaction: {
              mode: 'index',
              intersect: false,
            },
            plugins: {
              legend: {
                display: true, // Display legend to differentiate the two datasets
              },
              tooltip: {
                enabled: true,
                displayColors: false,
                backgroundColor: '#ffffff',
                bodyColor: '#AA158B',
                titleColor: '#AA158B',
                borderWidth: 1,
                borderColor: 'rgba(0,0,0,0.3)',
              },
            },
            scales: {
              x: {
                grid: {
                  display: false,
                },
                type: 'time',
                time: {
                  displayFormats: {
                    day: 'M/D',
                  },
                  unit: 'day',
                  stepSize: 5,
                  tooltipFormat: 'M/D',
                },
              },
              y: {
                grid: {
                  color: '#e9e9e9',
                },
                ticks: {
                  stepSize: 10,
                },
              },
            },
            responsive: true,
            maintainAspectRatio: false,
          },
        });
      });
    },
  },
};
</script>

<style scoped>
.chart-parent {
  height: 160px;
  width: 100%;
}
canvas {
  position: relative;
  width: 100%;
  height: 160px;
  z-index: 3;
}
</style>

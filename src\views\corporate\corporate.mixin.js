import { ref, computed, onMounted, nextTick } from 'vue';
import { useStore } from 'vuex';
import * as AutoKana from 'vanilla-autokana';

export default function useCorporateMixin() {
  const store = useStore();
  const previewImageURL = ref(null);
  const croppedImagePreview = ref(null);
  const logoImageId = ref(null);
  const loading = ref(false);
  const postalCodeLoading = ref(false);
  const successDialog = ref(false);

  let autokana;
  let nameInputTimeout = null;

  const fields = ref({
    internal_company_id: null,
    name: null,
    furigana_name: null,
    business_industry_id: null,
    office_address: null,
    office_phone: null,
    office_email1: null,
    office_email2: null,
    office_email3: null,
    website_url: null,
    client_liason: null,
    admin_memo: null,
    status: true,
    logo_img: null,
    flg_slack_notification: 0,
    slack_webhook: null,
  });

  const switchLabel = computed(() =>
    fields.value.status ? 'アクティブ' : 'インアクティブ'
  );

  const handleNameInput = () => {
    if (nameInputTimeout) clearTimeout(nameInputTimeout);
    nameInputTimeout = setTimeout(() => {
      fields.value.furigana_name = autokana.getFurigana();
    }, 300);
  };

  const imageCropSuccess = (image) => {
    croppedImagePreview.value = image;
    previewImageURL.value = null;
    fields.value.logo_img = image;
  };

  const imageCropCancel = () => {
    previewImageURL.value = null;
    fields.value.logo_img = null;
  };

  const previewImage = () => {
    if (fields.value.logo_img) {
      previewImageURL.value = URL.createObjectURL(fields.value.logo_img);
    }
  };

  onMounted(async () => {
    await store.dispatch('GET_CORPORATE_CATEGORY_DATA');
    await nextTick();
    if (document.querySelector('#name')) {
      autokana = AutoKana.bind('#name', '#furigana', { katakana: true });
    } else {
      console.warn('Element #name not found!');
    }
  });

  return {
    previewImageURL,
    croppedImagePreview,
    loading,
    postalCodeLoading,
    fields,
    switchLabel,
    handleNameInput,
    imageCropSuccess,
    imageCropCancel,
    previewImage,
    logoImageId,
    successDialog,
  };
}

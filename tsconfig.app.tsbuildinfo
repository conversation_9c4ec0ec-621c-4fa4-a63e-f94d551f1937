{"root": ["./src/app.vue", "./src/components/flashmessage.vue", "./src/components/admin/layout/layout.vue", "./src/components/admin/partials/corporate/imagecrop.vue", "./src/components/admin/partials/footer/footer.vue", "./src/components/admin/partials/header/header.vue", "./src/components/admin/partials/sidebar/sidebar.vue", "./src/components/charts/barchart.vue", "./src/components/charts/barchartold.vue", "./src/components/charts/doughnutchart.vue", "./src/components/charts/doughnutchartold.vue", "./src/components/charts/linechart.vue", "./src/components/charts/linechartold.vue", "./src/components/icons/account.vue", "./src/components/icons/arrowrightdrop.vue", "./src/components/icons/backarrow.vue", "./src/components/icons/bell.vue", "./src/components/icons/calendericon.vue", "./src/components/icons/cameraicon.vue", "./src/components/icons/chat.vue", "./src/components/icons/check.vue", "./src/components/icons/chevronleftfilledicon.vue", "./src/components/icons/closeicon.vue", "./src/components/icons/companies.vue", "./src/components/icons/contracticon.vue", "./src/components/icons/crossicon.vue", "./src/components/icons/deleteicon.vue", "./src/components/icons/dropdownicon.vue", "./src/components/icons/editicon.vue", "./src/components/icons/expansiondropdownarrow.vue", "./src/components/icons/eyeclose.vue", "./src/components/icons/eyefillicon.vue", "./src/components/icons/eyeopen.vue", "./src/components/icons/favouritedicon.vue", "./src/components/icons/feedicon.vue", "./src/components/icons/feedbackicon.vue", "./src/components/icons/fingertouch.vue", "./src/components/icons/greencross.vue", "./src/components/icons/handshakeicon.vue", "./src/components/icons/hearticon.vue", "./src/components/icons/homeerroricon.vue", "./src/components/icons/homeicon.vue", "./src/components/icons/indepthicon.vue", "./src/components/icons/leadershipicon.vue", "./src/components/icons/leftarrow.vue", "./src/components/icons/logo.vue", "./src/components/icons/lovedicon.vue", "./src/components/icons/messageicon.vue", "./src/components/icons/messagesicon.vue", "./src/components/icons/navigatelefticon.vue", "./src/components/icons/navigaterightarrowicon.vue", "./src/components/icons/notificationbubbleicon.vue", "./src/components/icons/notificationicon.vue", "./src/components/icons/opennewtab.vue", "./src/components/icons/openpaperplaneicon.vue", "./src/components/icons/paperplaneicon.vue", "./src/components/icons/pincel.vue", "./src/components/icons/previousanglecircle.vue", "./src/components/icons/questionicon.vue", "./src/components/icons/rightarrow.vue", "./src/components/icons/search.vue", "./src/components/icons/setting.vue", "./src/components/icons/star.vue", "./src/components/icons/starfilled.vue", "./src/components/icons/stipendicon.vue", "./src/components/icons/swapvertical.vue", "./src/components/icons/timeicon.vue", "./src/components/icons/universityicon.vue", "./src/components/icons/verifiedcheck.vue", "./src/components/icons/warning.vue", "./src/components/icons/warningred.vue", "./src/components/models/applicationrejectmodel.vue", "./src/components/models/contractdialog.vue", "./src/components/models/detailfeedback.vue", "./src/components/models/feedbackreasonmodel.vue", "./src/components/models/imagemodel.vue", "./src/components/models/interpreviewmodel.vue", "./src/components/models/interestedjobdialog.vue", "./src/components/models/internshipfeaturedialog.vue", "./src/components/models/mediatagdialog.vue", "./src/components/models/modalimportstudent.vue", "./src/components/models/multipleselectmodel.vue", "./src/components/models/occupationfeaturedialog.vue", "./src/components/models/schooldialog.vue", "./src/components/models/simplemodel.vue", "./src/components/models/successmodel.vue", "./src/components/pages/postinputs.vue", "./src/components/pages/column/columntag.vue", "./src/components/pages/contract/postinputs.vue", "./src/components/pages/internship/internshipsearch.vue", "./src/components/ui/datatable.vue", "./src/components/ui/datepicker.vue", "./src/components/ui/datepickerdialog.vue", "./src/components/ui/imageupload.vue", "./src/components/ui/inputpassword.vue", "./src/components/ui/pagetitle.vue", "./src/components/ui/richtexteditor.vue", "./src/components/ui/richtexteditor2.vue", "./src/components/ui/searcharea.vue", "./src/components/ui/searchbox.vue", "./src/components/ui/searchstudent.vue", "./src/components/ui/timedropdown.vue", "./src/views/privacy/privacy.vue", "./src/views/termsandconditions/terms.vue", "./src/views/test/index.vue", "./src/views/admin/create.vue", "./src/views/admin/edit.vue", "./src/views/admin/listing.vue", "./src/views/application/index.vue", "./src/views/auth/forgotpassword.vue", "./src/views/auth/login.vue", "./src/views/auth/resetpassword.vue", "./src/views/chats/details.vue", "./src/views/chats/index.vue", "./src/views/companyuser/create.vue", "./src/views/companyuser/edit.vue", "./src/views/companyuser/listing.vue", "./src/views/contract/contractdetailpdf.vue", "./src/views/contract/contracts.vue", "./src/views/contract/contractsdetail.vue", "./src/views/contract/histories.vue", "./src/views/contract/historydetail.vue", "./src/views/contract/uncontracteds.vue", "./src/views/contracttemplate/create.vue", "./src/views/contracttemplate/edit.vue", "./src/views/contracttemplate/list.vue", "./src/views/corporate/create.vue", "./src/views/corporate/details.vue", "./src/views/corporate/edit.vue", "./src/views/corporate/index.vue", "./src/views/dashboard/dashboard.vue", "./src/views/dashboard/dashboardnew.vue", "./src/views/educational_facilities/index.vue", "./src/views/error/error.vue", "./src/views/faqs/create.vue", "./src/views/faqs/edit.vue", "./src/views/faqs/index.vue", "./src/views/feedback/createoredit.vue", "./src/views/feedback/createoreditunaddress.vue", "./src/views/feedback/detaifeedback.vue", "./src/views/feedback/detaifeedbackunaddress.vue", "./src/views/feedback/index-unaddressed.vue", "./src/views/feedback/index.vue", "./src/views/interested_job/index.vue", "./src/views/internship/create.vue", "./src/views/internship/draft.vue", "./src/views/internship/edit.vue", "./src/views/internship/list.vue", "./src/views/internship/feature/index.vue", "./src/views/internship/occupation/index.vue", "./src/views/internship_student/feedback.vue", "./src/views/internship_student/list.vue", "./src/views/media-post/create.vue", "./src/views/media-post/edit.vue", "./src/views/media-post/index.vue", "./src/views/media-post/tags.vue", "./src/views/notification_management/create.vue", "./src/views/notifications/create.vue", "./src/views/notifications/edit.vue", "./src/views/notifications/index.vue", "./src/views/student/applicationtable.vue", "./src/views/student/studentapplicationdetails.vue", "./src/views/student/studentform.vue", "./src/views/student/contract.vue", "./src/views/student/contractdetail.vue", "./src/views/student/contractdetailpdf.vue", "./src/views/student/index.vue", "./src/views/student/profile.vue", "./src/views/student/report.vue", "./src/views/student/studentdetailsview/studentapplicationdetails.vue", "./src/views/student/studentdetailsview/studentinternshipdetailspage.vue", "./src/views/student/studentdetailsview/studenttabs.vue", "./src/views/student/applicationtablecomponent/applicationwithallotherstatus.vue", "./src/views/student/applicationtablecomponent/applicationwithalreadyappliedandpassedstatus.vue", "./src/views/student/applicationtablecomponent/filesvg.vue", "./src/views/users/applicants.vue", "./src/views/users/createoredit.vue", "./src/views/users/detail.vue", "./src/views/users/index.vue"], "version": "5.6.2"}
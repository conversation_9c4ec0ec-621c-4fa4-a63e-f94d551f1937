<template>
  <v-dialog v-model="getLaunch" max-width="775px">
    <v-card elevation="0" color="white" width="775px">
      <v-card-title class="d-flex justify-end pr-9 pb-2 pt-8">
        <v-btn variant="text" icon @click="closeModal">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-card-title
        v-if="edit"
        class="d-flex justify-center pt-0 pb-8 font-20px"
      >
        編集
      </v-card-title>
      <v-card-title v-else class="d-flex justify-center pt-0 pb-8 font-20px">
        新規登録
      </v-card-title>
      <div class="d-flex flex-column align-center justify-space-between">
        <Form @submit="submitForm" :initial-values="fields">
          <div class="d-flex flex-column align-center justify-center">
            <div class="input-width">
              <label class="d-block font-14px mb-1"> プルダウン表示項目 </label>
              <Field name="name" rules="required" v-slot="{ field, errors }">
                <v-text-field
                  v-bind="field"
                  :error-messages="errors"
                  :error="errors.length > 0"
                  :hide-details="errors.length <= 0"
                  placeholder="入力してください"
                  density="compact"
                  variant="outlined"
                />
              </Field>
            </div>
            <div class="input-width pt-2">
              <label class="d-block font-14px mb-1"> 表示順位 </label>
              <Field name="display_order" v-slot="{ field, errors }">
                <v-text-field
                  v-bind="field"
                  :error-messages="errors"
                  :error="errors.length > 0"
                  :hide-details="errors.length <= 0"
                  placeholder="半角数字を入力してください"
                  type="number"
                  density="compact"
                  variant="outlined"
                />
              </Field>
            </div>
          </div>
          <v-card-actions class="d-flex justify-center pt-7 pb-16">
            <v-btn
              v-if="edit"
              variant="outlined"
              color="primary"
              width="188px"
              height="35px"
              @click="closeModal"
            >
              戻る
            </v-btn>
            <v-btn
              type="submit"
              variant="elevated"
              color="primary"
              class="white--text ml-5"
              width="188px"
              height="35px"
            >
              {{ edit ? '保存' : '登録' }}
            </v-btn>
          </v-card-actions>
        </Form>
      </div>
    </v-card>
  </v-dialog>
</template>

<script>
import { ref, computed, watch } from 'vue';
import { useForm, Field, Form } from 'vee-validate';
import { useStore } from 'vuex';

export default {
  name: 'OccupationDialog',
  props: {
    edit: {
      type: Boolean,
      default: true,
    },
    launch: {
      type: Boolean,
      required: true,
    },
    item: {
      type: Object,
      default: null,
    },
  },
  setup(props, { emit }) {
    /**
     * Component Setup
     * Handles occupation feature dialog for creation and editing
     * Manages form state and interactions for internship occupation features
     */
    const store = useStore();

    /**
     * Dialog Control
     * Two-way binding for dialog visibility with parent component
     * Used for showing/hiding the occupation feature dialog
     */
    const getLaunch = computed({
      get() {
        return props.launch; // Get dialog state from parent
      },
      set(value) {
        emit('update:launch', value); // Update parent's dialog state
      },
    });

    /**
     * Form State Management
     * Manages occupation feature form data
     */
    const fields = ref({
      name: '', // Feature name (プルダウン表示項目)
      display_order: '', // Display order in list (表示順位)
    });

    /**
     * Form Data Watcher
     * Handles form initialization and reset based on dialog state
     * Populates form when editing existing feature
     */
    watch(
      () => props.launch,
      (val) => {
        if (val && props.item) {
          // Initialize form with existing feature data
          fields.value.name = props.item.name || '';
          fields.value.display_order = props.item.display_order || '';
        } else {
          // Reset form for new feature entry
          fields.value.name = '';
          fields.value.display_order = '';
        }
      }
    );

    /**
     * Form Utilities
     * Provides form reset functionality from vee-validate
     */
    const { resetForm } = useForm();

    /**
     * Dialog Action Handler
     * Manages dialog closure and form reset
     */
    const closeModal = () => {
      getLaunch.value = false; // Close dialog
      resetForm(); // Clear form state
    };

    /**
     * Form Submission Handler
     * Processes form submission for both create and update operations
     * @param {Object} values - Form field values
     */
    const submitForm = async (values) => {
      try {
        if (props.edit) {
          await updateOccupation(values); // Update existing feature
        } else {
          // Create new occupation feature
          await store.dispatch('INTERNSHIP_OCCUPATIONS_CREATE', values);
        }
        resetForm(); // Clear form
        emit('refresh'); // Notify parent to refresh list
        store.dispatch('GET_MASTER_DATA'); // Update master data
        closeModal(); // Close dialog
      } catch (error) {
        console.error(error);
      }
    };

    /**
     * Occupation Update Handler
     * Updates existing occupation feature with new values
     * @param {Object} values - Updated form values
     */
    const updateOccupation = async (values) => {
      try {
        values.id = props.item.id; // Add ID for update operation
        await store.dispatch('INTERNSHIP_OCCUPATIONS_EDIT', values);
      } catch (error) {
        console.error(error);
      }
    };

    return {
      getLaunch,
      fields,
      submitForm,
      closeModal,
    };
  },
};
</script>

<style lang="scss">
.input-width {
  width: 543px;
}
.v-field {
  &.v-field--variant-outlined {
    .v-field__outline {
      .v-field__outline__start,
      .v-field__outline__end {
        border-color: $light-grey !important;
      }
    }
  }

  &.v-field--error {
    .v-field__outline {
      .v-field__outline__start,
      .v-field__outline__end {
        border-color: $color-red !important;
      }
    }

    // For progress linear error color
    .v-progress-linear__background,
    .v-progress-linear__buffer,
    .v-progress-linear__indeterminate .long,
    .v-progress-linear__indeterminate .short {
      background-color: $color-red !important;
    }
  }
}
</style>

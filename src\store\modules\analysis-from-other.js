import axios from '@/services/api';

function initialState() {
  return {
    status: '',
    analysisList: [],
    singleAnalysis: {},
    analysisCounts: {},
    analysisPagination: 0,
  };
}

const state = initialState();

const getters = {
  getAllAnalysis: (state) => state.analysisList,
  getSingleAnalysis: (state) => state.singleAnalysis,
  getAnalysisCounts: (state) => state.analysisCounts,
  getAnalysisPagination: (state) => state.analysisPagination,
};

const actions = {
  ['ANALYSIS_GET_ALL']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('ANALYSIS_REQUEST');
      axios
        .get(`/admin/analysis`, { params })
        .then((response) => {
          let data = response.data;
          commit('ANALYSIS_GET_ALL_SUCCESS', { data });

          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['ANALYSIS_GET']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('ANALYSIS_REQUEST');
      axios
        .get(`/admin/analysis/${params.id}`)
        .then((response) => {
          let data = response.data;
          commit('ANALYSIS_GET_SUCCESS', { data });

          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['ANALYSIS_UPDATE']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('ANALYSIS_REQUEST');
      axios
        .put(`/admin/analysis/${params.id}`, params)
        .then((response) => {
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['ANALYSIS_DELETE']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('ANALYSIS_REQUEST');
      axios
        .delete(`/admin/analysis/${params.id}`, params)
        .then((response) => {
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },
};

const mutations = {
  ['ANALYSIS_REQUEST']: (state) => {
    state.status = 'loading';
  },

  ['ANALYSIS_GET_ALL_SUCCESS']: (state, { data }) => {
    state.status = 'success';
    state.analysisList = data.data;
    state.analysisPagination = data.pagination;
    state.analysisCounts = data.total_count;
  },

  ['ANALYSIS_GET_SUCCESS']: (state, { data }) => {
    state.status = 'success';
    state.singleAnalysis = data.data;
  },
};

export default {
  state,
  getters,
  actions,
  mutations,
};

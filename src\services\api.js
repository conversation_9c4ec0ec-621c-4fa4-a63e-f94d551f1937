/**
 * API Service Configuration
 * Configures axios instance with interceptors for request/response handling
 */
import axios from 'axios';
import store from '@/store/index.js';
import i18n from '@/plugins/i18n';

/**
 * Request Interceptor
 * Handles request configuration before sending to the server
 * - Clears any existing form errors
 * - Adds authentication token if available
 * - Sets default headers for Japanese locale and JSON content
 * - Configures base URL from environment variables
 */
axios.interceptors.request.use(
  (config) => {
    // Clear any existing form errors before new request
    store.commit('clearFormErrors');

    // Add authentication token if available
    const token = store.getters.token;
    if (token) {
      config.headers['Authorization'] = 'Bearer ' + token;
    }

    // Set default headers
    config.headers['Accept-Language'] = 'ja'; // Set Japanese as default language
    config.headers['Content-Type'] = 'application/json';
    config.baseURL = import.meta.env.VITE_APP_API_URL; // Set API base URL from env

    return config;
  },
  (error) => {
    Promise.reject(error); // Reject promise if request error occurs
  }
);

/**
 * Response Interceptor
 * Handles API response processing
 * - Manages authentication errors (401, 403)
 * - Handles validation errors (422)
 * - Processes successful responses
 */
axios.interceptors.response.use(
  (response) => {
    return response; // Return response directly if successful
  },
  function (error) {
    // Handle authentication errors
    if (error?.response?.status === 401 || error?.response?.status === 403) {
      store.commit('AUTH_LOGOUT'); // Logout user if unauthorized/forbidden
    }
    // Handle validation errors
    if (error?.response?.status === 422) {
      // Validation error handling can be implemented here
    }
    return Promise.reject(error.response); // Reject promise with error response
  }
);

// Export configured axios instance
export default axios;

<template>
  <div>
    <PageTitle
      v-show="!loading && !$store.getters.getApiProcessingStatus"
      :items="{
        title: '契約管理',
        subTitle: '一覧',
        tabs: [
          {
            title: 'アクティブ',
            count: getContractActiveCount,
            notification: 0,
            action: () => resultOnTab(2),
          },
          {
            title: '契約満了',
            count: getContractInactiveCount,
            notification: 0,
            action: () => resultOnTab(4),
          },
        ],
        buttons: [
          {
            title: 'CSVエクスポート',
            class: 'bg-white text-ff862f',
            color: 'text-ff862f',
            variant: 'outlined',
            action: () => downloadCsv(),
          },
          {
            title: '詳細条件検索',
            class: 'bg-white',
            variant: 'outlined',
            action: () => {
              toggleSearch = !toggleSearch;
            },
          },
        ],
      }"
    />
    <v-fade-transition>
      <SearchArea
        v-if="toggleSearch"
        @toggleSearch="updateSearchResults"
        @searchSubmit="searchSubmit"
        @changedInputType="setChangedInputType"
        v-bind="{ toggleSearch, selectTypeOptions, searchFields }"
        class="mb-4"
      ></SearchArea>
    </v-fade-transition>

    <contractDetailPDF
      v-if="isDownloadPDF"
      :id="idContract"
      :studentId="studentId"
      ref="pdfDocumentContractAdmin"
    />

    <DataTable
      v-show="!loading && !$store.getters.getApiProcessingStatus"
      :items="items"
      :headers="getHeaders"
      :total-records="
        getContractPagination ? getContractPagination.records_total : 0
      "
      :number-of-pages="
        getContractPagination ? getContractPagination.total_pages : 0
      "
      :customHeight="'height: 69px'"
      @update:options="updateTable"
      :page="configuration.page"
      ref="pagination"
      class="contract-table"
    >
      <template v-slot:item.id="{ item }">
        <div style="min-width: 100px" class="position-relative">
          {{ item && item.internal_contract_id ? item.internal_contract_id.split('-')[0] : '' }}
        </div>
      </template>

      <!-- type_format -->
      <template v-slot:item.type_format="{ item }">
        <div class="flex align-center" style="min-width: 120px">
          <v-radio-group
            v-model="item.type_format"
            density="compact"
            @change="handleRadioChange(item, item.type_format)"
            inline
            :hide-details="true"
            :disabled="isTypeFormatDisabled(item)"
          >
            <v-radio
              :ripple="false"
              v-for="(option, index) in typeContracts"
              :key="index"
              color="primary"
              :value="option.id"
              class="mr-2"
            >
              <template #label>
                <span class="font-12px">{{ option.name }}</span>
              </template>
            </v-radio>
          </v-radio-group>
        </div>
      </template>

      <!-- company -->
      <template v-slot:item.company_id="{ item }">
        <div class="font-12px fw-400">
          {{ item?.company?.internal_company_id }}
        </div>
        <div class="mouse-pointer">
          <router-link
            v-if="item.company.id"
            target="_blank"
            class="text-3f74c2 text-decoration-none truncate-lines lines-1"
            :to="{ name: 'CorporateDetails', params: { id: item.company.id } }"
          >
            <v-tooltip :text="item.company.name" location="top" color="white">
              <template #activator="{ props }">
                <span v-bind="props">{{ item.company.name }}</span>
              </template>
            </v-tooltip>
          </router-link>
        </div>
      </template>

      <!-- student -->
      <template v-slot:item.student_id="{ item }">
        <div class="font-12px fw-400">
          {{ item?.student?.student_internal_id }}
        </div>
        <div class="mouse-pointer">
          <router-link
            v-if="item?.student?.id"
            target="_blank"
            class="text-3f74c2 text-decoration-none truncate-lines lines-1"
            :to="{ name: 'StudentProfile', params: { id: item?.student?.id } }"
          >
            <v-tooltip
              :text="`${item.student.family_name} ${item.student.first_name}`"
              location="top"
              color="white"
            >
              <template #activator="{ props }">
                <span v-bind="props">
                  {{ item.student.family_name }} {{ item.student.first_name }}
                </span>
              </template>
            </v-tooltip>
          </router-link>
          <div v-else>存在しない</div>
        </div>
      </template>

      <!-- university -->
      <template v-slot:item.university="{ item }">
        <div v-if="item?.student?.id">
          <div class="font-12px fw-400">{{ item?.education_facility }}</div>
          <div
            :class="item?.student?.reason_for_withdrawal ? 'text-b8b8b8' : ''"
          >
            {{ item?.student?.email_valid }}
          </div>
        </div>
        <div v-else>存在しない</div>
      </template>

      <!-- detail -->
      <template v-slot:[`item.detail`]="{ item }">
        <div
          v-if="item.student"
          @click="showDetailApp(item)"
          class="position-relative pl-5 cursor-pointer"
        >
          <v-icon color="#black">$FeedIcon</v-icon>
        </div>
      </template>

      <!-- qulifying date -->
      <template v-slot:item.date_application_passed="{ item }">
        <div class="font-12px fw-400">
          {{
            item.date_application_passed
              ? dateFormat(item.date_application_passed)
              : '-'
          }}
        </div>
      </template>

      <!-- start date -->
      <template v-slot:item.date_entire_employment_start="{ item }">
        <div class="font-12px fw-400">
          {{
            item.date_entire_employment_start
              ? dateFormat(item.date_entire_employment_start)
              : ''
          }}
        </div>
      </template>

      <!-- end date -->
      <template v-slot:item.date_entire_employment_end="{ item }">
        <div
          class="font-12px fw-400"
          :class="isDateWithinOneMonth(item) ? 'text-change' : ''"
        >
          {{
            item.date_entire_employment_end ? dateFormat(item.date_entire_employment_end) : ''
          }}
        </div>
      </template>

      <!-- status -->
      <template v-slot:item.status="{ item }">
        <div class="font-12px fw-400">
          <v-chip
            text-color="white"
            :color="chipColor(item.status, item.type_format)"
            variant="flat"
            dense
            size="small"
            class="status-chip"
          >
            <div class="font-12px text-white">
              {{ getStatus(item.status, item.type_format) }}
            </div>
          </v-chip>
        </div>
      </template>

      <!-- renew -->
      <template v-slot:item.renew="{ item }">
        <div class="font-12px fw-400">{{ item?.renew ? item.renew : '-' }}</div>
      </template>

      <!-- action -->
      <template v-slot:item.action="{ item }">
        <div class="font-12px fw-400 cursor-pointer" @click="openDetail(item)">
          <v-icon class="text-ff862f" color="#11aba3">$edit</v-icon>
        </div>
      </template>
    </DataTable>

    <ContractDialog
      v-model:launch="launchEdit"
      :item="editItem"
      @refresh="generateItems"
      @cancelUpdate="cancelUpdate"
    />

    <ApplicationDetailDialog
      text="応募内容"
      :dialog="dialog.isShowDetailApp"
      :application="application"
      @submitSuccess="dialog.isShowDetailApp = false"
      @closeModel="dialog.isShowDetailApp = false"
    ></ApplicationDetailDialog>

    <SimpleModel
      :text="errorMessages"
      :dialog="dialog.errorDialog"
      :showCloseIcon="true"
      @closeModel="dialog.errorDialog = false"
      :buttonOption="{
        hideCancel: true,
        hideSubmit: true,
      }"
    ></SimpleModel>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, defineAsyncComponent } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
const PageTitle = defineAsyncComponent(
  () => import('@/components/ui/PageTitle.vue')
);
const DataTable = defineAsyncComponent(
  () => import('@/components/ui/DataTable.vue')
);
import contractDetailPDF from '@/views/contract/contractDetailPdf.vue';
const ContractDialog = defineAsyncComponent(
  () => import('@/components/models/ContractDialog.vue')
);
const ApplicationDetailDialog = defineAsyncComponent(
  () => import('@/components/models/ApplicationDetailDialog.vue')
);
const SimpleModel = defineAsyncComponent(
  () => import('@/components/models/SimpleModel.vue')
);
const SearchArea = defineAsyncComponent(
  () => import('@/components/ui/SearchArea.vue')
);
import Encoding from 'encoding-japanese';

import moment from 'moment';
const dateFormat = (date) => {
  return date ? moment(date).format('YYYY/MM/DD') : '---- / - / -';
};

const store = useStore();
const router = useRouter();

const application = ref({});
const errorMessages = ref(null);
const dialog = ref({ isShowDetailApp: false, errorDialog: false });
const toggleSearch = ref(false);
const items = ref([]);
const loading = ref(false);
const isDownloadPDF = ref(false);
const idContract = ref(null);
const studentId = ref(null);
const launchEdit = ref(false);
const isFromPencilButton = ref(false);
const editItem = ref(null);
const searchFields = ref([]);
const selectTypeOptions = ref([
  {
    id: 'keyword_search',
    name: 'キーワード検索',
  },
  {
    id: 'created_at',
    name: '合格日',
  },
]);
const configuration = ref({
  page: 1,
  sort_by: 'updated_at',
  sort_by_order: 'desc',
  paginate: 25,
  status: '2,3',
});
const typeContracts = ref([
  { id: 1, name: 'KN' },
  { id: 2, name: '独自' },
]);

const getHeaders = computed(() => [
  {
    title: '契約ID',
    value: 'id',
    align: 'left',
    class: ['py-3', 'px-0'],
    sortable: false,
    flex: '1 1 5%', // Flex grow, flex shrink, and basis for proportional width
  },
  {
    title: 'フォーマット',
    value: 'type_format',
    align: 'left',
    class: ['py-3', 'px-0'],
    sortable: false,
    flex: '1 1 15%', // Flex grow, flex shrink, and basis for proportional width
  },
  {
    title: '内部ID',
    subTitle: '企業名',
    align: 'left',
    class: ['py-3', 'px-0'],
    value: 'company_id',
    sortable: false,
    flex: '1 1 20%',
  },
  {
    title: '学生ID',
    subTitle: '学生名',
    class: ['py-3', 'px-0'],
    value: 'student_id',
    align: 'left',
    sortable: false,
    flex: '1 1 15%',
  },
  {
    title: '大学名',
    subTitle: '学生メールアドレス',
    value: 'university',
    class: ['py-3', 'px-0'],
    align: 'left',
    sortable: false,
    flex: '1 1 15%',
  },
  {
    title: '応募内容',
    value: 'detail',
    class: ['py-3', 'px-0'],
    align: 'left',
    sortable: false,
    flex: '1 1 15%',
  },
  {
    title: '合格日',
    sortable: false,
    align: 'left',
    class: ['py-3', 'px-0'],
    value: 'date_application_passed',
    flex: '1 1 15%',
  },
  {
    title: '当初雇用開始日',
    sortable: false,
    align: 'left',
    class: ['py-3', 'px-0'],
    value: 'date_entire_employment_start',
    flex: '1 1 15%',
  },
  {
    title: '最新雇用終了日',
    sortable: false,
    align: 'left',
    class: ['py-3', 'px-0'],
    value: 'date_entire_employment_end',
    flex: '1 1 15%',
  },
  {
    title: '学生確認',
    sortable: false,
    align: 'left',
    class: ['py-3', 'px-0'],
    value: 'status',
    flex: '1 1 15%',
  },
  {
    title: '',
    value: 'action',
    class: ['py-3', 'px-0'],
    sortable: false,
    align: 'center',
    flex: '1 1 15%',
  },
]);

// Computed properties for pagination and contract counts
const getContractPagination = computed(
  () => store.getters.getContractPagination
);
const getContractActiveCount = computed(
  () => store.getters.getContractActiveCount || 0
);
const getContractInactiveCount = computed(
  () => store.getters.getContractInactiveCount || 0
);

/**
 * Handle contract format type radio button change
 * Opens edit dialog for confirmation
 * @param {Object} item - Contract item being edited
 */
const handleRadioChange = async (item, type_format) => {
  launchEdit.value = true;
  editItem.value = item;
  editItem.value.type_format = type_format;
};

/**
 * Cancels the update of the contract type format.
 * Reverts the change in the UI.
 * @param {Object} item The contract item being edited.
 */
const cancelUpdate = (item) => {
  if (!isFromPencilButton.value) {
    // Find the index of the item with the specified ID
    const index = items.value.findIndex((i) => i.id === item.id);
    if (index !== -1) {
      // Update the object at that index with the new values
      items.value[index] = {
        ...items.value[index],
        type_format: item.type_format === 1 ? 2 : 1,
      };
    } else {
      console.error(`Item with ID ${item.id} not found.`);
    }
  } else {
    isFromPencilButton.value = false;
  }
};

/**
 * Export contracts data to CSV file
 * Filename format: 契約管理_YYYY-MM-DD.csv (Contract Management_Date.csv)
 */
const downloadCsv = async () => {
  await store.dispatch('CONTRACT_EXPORT_CSV');

  // Show error if no data found
  if (store.getters.getContractCsvData?.message) {
    dialog.value.errorDialog = true;
    errorMessages.value = '<div class="pt-10">データが見つかりません。</div>'; // "No data found"
    return;
  }

  // Get CSV data from Vuex store
  const csvData = store.getters.getContractCsvData.data.csv;

  // Convert UTF-8 CSV to Shift-JIS
  const sjisArray = Encoding.convert(Encoding.stringToCode(csvData), {
    to: 'SJIS',
    from: 'UNICODE',
  });

  const uint8Array = new Uint8Array(sjisArray);
  const fileUrl = window.URL.createObjectURL(
    new Blob([uint8Array], { type: 'text/csv;charset=Shift_JIS' })
  );

  const fileLink = document.createElement('a');
  fileLink.href = fileUrl;
  fileLink.setAttribute(
    'download',
    `契約管理_${new Date().toISOString().slice(0, 10)}.csv`
  );
  document.body.appendChild(fileLink);
  fileLink.click();
  document.body.removeChild(fileLink);
};

/**
 * Update table configuration based on sorting and pagination changes
 * @param {Object} e - Event object containing sort and page information
 */
const updateTable = async (e) => {
  // Update sort settings
  configuration.value.sort_by =
    e && e.sortBy && e.sortBy.length && e?.sortBy[0]?.key
      ? e?.sortBy[0]?.key
      : 'updated_at';
  configuration.value.sort_by_order =
    e && e.sortBy && e.sortBy.length && e?.sortBy[0]?.order
      ? e?.sortBy[0]?.order
      : 'desc';
  configuration.value.page = typeof e === 'number' ? e : (e?.page ?? 1);
  await generateItems();
};

/**
 * Handle tab change between Active and Expired contracts
 * @param {number} tab - Tab identifier (2: Active, 4: Expired)
 */
const resultOnTab = async (tab) => {
  configuration.value.page = 1;
  configuration.value.status = tab;
  await generateItems();
  resetPagination();
};

/**
 * Reset search parameters and refresh contract list
 */
const updateSearchResults = async () => {
  toggleSearch.value = false;
  configuration.value.page = 1;
  configuration.value.search = null;
  configuration.value.date_from = null;
  configuration.value.date_to = null;
  await generateItems();
  resetPagination();
};

/**
 * Fetch contract items based on current configuration
 * Status 2,3 represents active contracts (2: Pending, 3: Confirmed)
 */
const generateItems = async () => {
  loading.value = true;
  items.value = [];
  if (configuration.value.status === 2) configuration.value.status = '2,3';

  try {
    const response = await store.dispatch(
      'CONTRACT_GET_ALL',
      configuration.value
    );
    items.value = response.data.data;
  } catch (error) {
    console.error('Error fetching contract data:', error);
    // Optionally show an error message or handle the error
  } finally {
    loading.value = false;
  }
};

/**
 * Update search fields based on search type
 * @param {string} inputSearchType - Type of search (keyword_search or created_at)
 */
const setChangedInputType = (inputSearchType) => {
  if (inputSearchType === 'keyword_search') {
    // Text search for company/student information
    searchFields.value = [
      {
        label: 'Search text',
        name: 'search',
        type: 'text',
        value: null,
        placeholder: '企業内部ID、企業名、学生ID、学生名', // Company ID, Company name, Student ID, Student name
      },
    ];
  } else if (inputSearchType === 'created_at') {
    // Date range search for application date
    searchFields.value = [
      {
        label: 'Label',
        name: 'date_from',
        type: 'date',
        rules: 'required',
        show_after_approx: true,
        value: moment().format('YYYY-MM-DD'),
        menu: false,
        locale: 'ja',
        date_format: 'YYYY-MM-DD',
      },
      {
        label: 'Label',
        name: 'date_to',
        type: 'date',
        rules: 'required',
        show_after_approx: false,
        value: moment().format('YYYY-MM-DD'),
        menu: false,
        locale: 'ja',
        range: true,
        range_input: 'date_from',
        date_format: 'YYYY-MM-DD',
      },
    ];
  }
};

/**
 * Determines the color for the status chip based on contract status and format type
 * @param {number} status - Contract status (1: In Preparation, 2: Pending, 3: Confirmed, 4: Expired)
 * @param {number} type_format - Contract format type (1: KN, 2: Independent)
 * @returns {string} Hex color code for the status chip
 */
const chipColor = (status, type_format) => {
  // Special case: Confirmed status with Independent format
  if (status === 3 && type_format === 2) {
    return '#E5E5E5'; // Gray - indicates no confirmation needed for independent format
  }
  switch (status) {
    case 1:
      return '#8B8000'; // Yellow - Contract in preparation
    case 2:
      return '#EE6C9B'; // Pink - Waiting for confirmation
    case 3:
      return '#60D1CB'; // Teal - Confirmed
    case 4:
      return '#A7A7A7'; // Blue - Contract expired
  }
};

/**
 * Gets the status text based on contract status and format type
 * @param {number|string} value - Contract status
 * @param {number} type_format - Contract format type (1: KN, 2: Independent)
 * @returns {string} Localized status text in Japanese
 */
const getStatus = (value, type_format) => {
  const status = parseInt(value);
  // Independent format contracts don't need confirmation when confirmed
  if (status === 3 && type_format === 2) {
    return '-';
  }
  switch (status) {
    case 1:
      return '契約準備中  '; // Contract in preparation
    case 2:
      return '確認待ち'; // Waiting for confirmation
    case 3:
      return '確認済み'; // Confirmed
    case 4:
      return '契約満了'; // Contract expired
  }
};

/**
 * Opens the contract detail view.
 * @param {Object} item The contract item.
 */
const openDetail = (item) => {
  isFromPencilButton.value = true;
  if (item.type_format === 2) {
    editItem.value = item;
    launchEdit.value = true;
  } else if (item.status > 1) {
    router.push(`contract-detail/${item.id}?createNew=true`);
    return;
  } else {
    router.push(`contract-detail/${item.id}`);
  }
};

/**
 * Handle search form submission
 * @param {Object} $event - Event object containing search fields
 */
const searchSubmit = async ($event) => {
  let obj = {};
  // Process search fields if they exist
  if ($event.fields.length > 0) {
    $event.fields.forEach((field) => {
      obj[field.name] = field.value;
    });
  }
  // Update configuration with new search parameters while preserving existing ones
  configuration.value = {
    ...configuration.value,
    ...obj,
  };
  await generateItems();
  resetPagination();
};

/**
 * Reset pagination to first page
 */
const resetPagination = () => {
  configuration.value.page = 1;
};

/**
 * Show application detail dialog
 * @param {Object} item - Contract item containing application details
 */
const showDetailApp = (item) => {
  application.value = item.application;
  dialog.value.isShowDetailApp = true;
};

/**
 * Check if contract end date is within one month
 * Used to highlight contracts nearing expiration
 * @param {Object} item - Contract item containing end date
 * @returns {boolean} True if contract ends within a month
 */
const isDateWithinOneMonth = (item) => {
  // Don't highlight expired contracts
  if (
    configuration.value &&
    configuration.value.status &&
    configuration.value.status === 4
  ) {
    return false;
  }
  const date = item.date_employment_end;
  if (!date) return false;

  const today = new Date();
  const oneMonthLater = new Date();
  oneMonthLater.setMonth(today.getMonth() + 1);
  const employmentEndDate = new Date(date);
  return employmentEndDate <= oneMonthLater && employmentEndDate >= today;
};

/**
 * Check if contract type format can be modified
 * Format cannot be changed for confirmed or expired contracts
 * @param {Object} item - Contract item
 * @returns {boolean} True if format selection should be disabled
 */
const isTypeFormatDisabled = (item) => {
  return item && [4].includes(item.status); // Status 3: Confirmed, 4: Expired
};

onMounted(() => {});
</script>

<style lang="scss">
.pill {
  width: 77px;
  height: 30px;
  border-radius: 30px;
}

.contract-table {
  :deep(thead th) {
    padding-left: 16px !important;
    padding-right: 16px !important;
  }

  :deep(tbody tr td) {
    padding-top: 15px !important;
    padding-bottom: 15px !important;

    &:nth-child(2) {
      padding-left: 0 !important;
      padding-right: 0 !important;
    }
  }

  :deep(tbody tr:not(.row-pink):hover) {
    background-color: transparent !important;
  }
}

.text-change {
  color: #e14d56 !important;
}
.status-chip {
  min-width: 80px;
  justify-content: center;
}
</style>

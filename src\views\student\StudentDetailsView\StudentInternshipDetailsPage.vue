<template>
  <v-container>
    <v-row justify="center">
      <v-col cols="12" sm="10" md="8">
        <v-card elevation="0" class="d-flex justify-left pl-10">
          <div
            class="mt-10"
            :class="{
              'container-history': $vuetify.breakpoint.lgAndUp,
              'container-history-mobile': $vuetify.breakpoint.mdAndDown,
            }"
          >
            <div
              v-for="(item, index) in detailApplicant"
              :key="index"
              class="no-truncate-history"
            >
              <div>
                <div class="no-truncate-history mb-6">
                  <div
                    class="text-97 pr-10"
                    :class="{
                      'font-18px': $vuetify.breakpoint.lgAndUp,
                      'font-14px': $vuetify.breakpoint.mdAndDown,
                    }"
                  >
                    {{ item.label }}
                  </div>
                  <div
                    class="d-flex justify-space-between align-center mt-1 pr-4"
                  >
                    <div
                      v-if="item.content && !item.isURL"
                      class="sub-label"
                      :class="{
                        'font-16px': $vuetify.breakpoint.lgAndUp,
                        'font-12px': $vuetify.breakpoint.mdAndDown,
                      }"
                    >
                      {{ item.content }}
                      <div v-if="item.content2">{{ item.content2 }}</div>
                      <div v-if="item.content3">{{ item.content3 }}</div>
                    </div>
                    <div
                      v-if="item.content && item.isURL"
                      class="sub-label text-3979d9"
                      :class="{
                        'font-16px': $vuetify.breakpoint.lgAndUp,
                        'font-12px': $vuetify.breakpoint.mdAndDown,
                      }"
                    >
                      <a
                        v-if="isUrl(item.content)"
                        class="text-blue"
                        :href="extractUrl(item.content)"
                        target="_blank"
                      >
                        {{ item.content }}
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>
<script>
import InternshipMixins from '../../internship/internship.mixin';
import { mapGetters } from 'vuex';

export default {
  name: 'StudentInternshipDetailsPage',
  mixins: [InternshipMixins],
  data() {
    return {
      detailApplicant: [
        { label: '企業名', content: '' },
        { label: '求人タイトル', content: '' },
        { label: '職種', content: '' },
        { label: 'インターン期間', content: '' },
        { label: '週稼働時間', content: '' },
        { label: '時給', content: '' },
        { label: '採用フロー', content: '' },
        { label: '会社概要', content: '' },
        { label: '募集内容', content: '' },
      ],
      showToggleCopy: false,
    };
  },
  created() {
    this.$store.dispatch('INTERNSHIP_GET', {
      id: this.$route.params.applicationID,
    });
  },
  mounted() {
    this.fetchApplicantDetails();
  },
  methods: {
    fetchApplicantDetails() {
      this.detailApplicant = [
        {
          label: '企業名',
          content: this.getSingleInternship.company.name || '回答なし',
        },
        {
          label: '求人タイトル',
          content: this.getSingleInternship.title || '回答なし',
        },
        {
          label: '職種',
          content: this.getSingleInternship.work_category_id || '回答なし',
        },
        {
          label: 'インターン期間',
          content: this.getSingleInternship.period || '回答なし',
        },
        {
          label: '週稼働時間',
          content: this.getSingleInternship.workload || '回答なし',
        },
        { label: '時給', content: this.getSingleInternship.wage || '回答なし' },
        {
          label: '採用フロー',
          content: this.getSingleInternship.application_step_1 || '回答なし',
        },
        {
          label: '会社概要',
          content:
            this.getSingleInternship.description_corporate_profile ||
            '回答なし',
        },
        {
          label: '募集内容',
          content:
            this.getSingleInternship.description_internship_content ||
            '回答なし',
        },
      ];
    },
    isUrl(content) {
      const urlPattern = new RegExp(
        '^(https?:\\/\\/)?' + // validate protocol
          '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.?)+[a-z]{2,}|' + // domain name
          '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
          '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*' + // port and path
          '(\\?[;&a-z\\d%_.~+=-]*)?' + // query string
          '(\\#[-a-z\\d_]*)?$',
        'i'
      ); // fragment locator
      return !!urlPattern.test(content);
    },
    extractUrl(content) {
      if (this.isUrl(content)) {
        return content.startsWith('http') ? content : `http://${content}`;
      }
      return '#';
    },
    copyToClipboard(text) {
      const el = document.createElement('textarea');
      el.value = text;
      document.body.appendChild(el);
      el.select();
      document.execCommand('copy');
      document.body.removeChild(el);
      this.showToggleCopy = true;
      this.ToggleFavouriteAlertMessage = 'インターンシップを辞退しました。';
      setInterval(() => {
        this.showToggleCopy = false;
      }, 2000);
    },
  },
  computed: {
    ...mapGetters(['getSingleInternship']),
  },
};
</script>

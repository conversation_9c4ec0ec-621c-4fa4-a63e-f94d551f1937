<template>
  <div class="">
    <PageTitle
      v-show="!loading && !$store.getters.getApiProcessingStatus"
      :items="{
        title: '契約管理',
        subTitle: '送付履歴',
        buttons: [
          {
            title: 'CSVエクスポート',
            class: 'bg-white text-ff862f',
            color: 'text-ff862f',
            variant: 'outlined',
            action: () => downloadCsv(),
          },
          {
            title: '詳細条件検索',
            class: 'bg-white',
            variant: 'outlined',
            action: () => {
              toggleSearch = !toggleSearch;
            },
          },
        ],
      }"
    ></PageTitle>
    <v-fade-transition>
      <SearchArea
        v-if="toggleSearch"
        @toggleSearch="updateSearchResults"
        @searchSubmit="searchSubmit"
        @changedInputType="setChangedInputType"
        @resetForm="getDataFromApi"
        v-bind="{ toggleSearch, selectTypeOptions, searchFields }"
        class="mb-4"
      ></SearchArea>
    </v-fade-transition>

    <contractDetailPDF
      v-if="isDownloadPDF"
      :id="idContract"
      :studentId="studentId"
      ref="pdfDocumentContractAdmin"
    />

    <DataTable
      v-show="!loading && !$store.getters.getApiProcessingStatus"
      :items="items"
      :headers="getHeaders"
      :total-records="
        getContractPagination ? getContractPagination.records_total : 0
      "
      :number-of-pages="
        getContractPagination ? getContractPagination.total_pages : 0
      "
      @update:options="updateTable"
      :page="configuration.page"
      ref="pagination"
      :customHeight="'height: 69px'"
      class="contract-table"
    >
      <template v-slot:item.id="{ item }">
        <div style="min-width: 100px" class="position-relative">
          {{ item.internal_contract_id }}
        </div>
      </template>

      <!-- type_format -->
      <template v-slot:item.type_format="{ item }">
        <div class="flex align-center" style="min-width: 120px">
          <v-radio-group
            v-model="item.type_format"
            density="compact"
            @change="handleRadioChange(item)"
            inline
            :hide-details="true"
          >
            <v-radio
              :ripple="false"
              v-for="(option, index) in typeContracts"
              :key="index"
              color="primary"
              :value="option.id"
              class="mr-2"
            >
              <template #label>
                <span class="font-12px">{{ option.name }}</span>
              </template>
            </v-radio>
          </v-radio-group>
        </div>
      </template>

      <!-- company -->
      <template v-slot:item.company_id="{ item }">
        <div class="font-12px fw-400">
          {{ item.company.internal_company_id }}
        </div>
        <div class="mouse-pointer">
          <router-link
            v-if="item?.company?.id"
            target="_blank"
            class="text-3f74c2 text-decoration-none truncate-lines lines-1"
            :to="{ name: 'CorporateDetails', params: { id: item.company.id } }"
          >
            <v-tooltip :text="item.company.name" location="top" color="white">
              <template #activator="{ props }">
                <span v-bind="props">{{ item.company.name }}</span>
              </template>
            </v-tooltip>
          </router-link>
        </div>
      </template>

      <!-- student -->
      <template v-slot:item.student_id="{ item }">
        <div class="font-12px fw-400">
          {{ item?.student?.student_internal_id }}
        </div>
        <div class="mouse-pointer">
          <router-link
            v-if="item?.student?.id"
            target="_blank"
            class="text-3f74c2 text-decoration-none truncate-lines lines-1"
            :to="{ name: 'StudentProfile', params: { id: item?.student?.id } }"
          >
            <v-tooltip
              :text="`${item.student.family_name} ${item.student.first_name}`"
              location="top"
              color="white"
            >
              <template #activator="{ props }">
                <span v-bind="props">
                  {{ item.student.family_name }} {{ item.student.first_name }}
                </span>
              </template>
            </v-tooltip>
          </router-link>
          <div v-else>存在しない</div>
        </div>
      </template>

      <!-- university -->
      <template v-slot:item.university="{ item }">
        <div v-if="item?.student?.id">
          <div class="font-12px fw-400">{{ item?.education_facility }}</div>
          <div
            :class="item?.student?.reason_for_withdrawal ? 'text-b8b8b8' : ''"
          >
            {{ item?.student?.email_valid }}
          </div>
        </div>
        <div v-else>存在しない</div>
      </template>

      <!-- qulifying date -->
      <template v-slot:item.date_application_passed="{ item }">
        <div class="font-12px fw-400">
          {{
            item.date_application_passed
              ? dateFormat(item.date_application_passed)
              : '-'
          }}
        </div>
      </template>

      <!-- start date -->
      <template v-slot:item.date_employment_start="{ item }">
        <div class="font-12px fw-400">
          {{
            item.date_employment_start
              ? dateFormat(item.date_employment_start)
              : ''
          }}
        </div>
      </template>

      <!-- end date -->
      <template v-slot:item.date_employment_end="{ item }">
        <div class="font-12px fw-400">
          {{
            item.date_employment_end ? dateFormat(item.date_employment_end) : ''
          }}
        </div>
      </template>

      <!-- status -->
      <template v-slot:item.status="{ item }">
        <div class="font-12px fw-400">
          <v-chip
            text-color="white"
            :color="chipColor(item.status, item.type_format)"
            variant="flat"
            dense
            size="small"
            class="status-chip"
          >
            <div class="font-12px text-white">
              {{ getStatus(item.status, item.type_format) }}
            </div>
          </v-chip>
        </div>
      </template>

      <!-- approve date -->
      <template v-slot:item.datetime_contract_sent="{ item }">
        <div class="font-12px fw-400">
          {{
            item.datetime_contract_sent
              ? dateFormat(item.datetime_contract_sent)
              : ''
          }}
        </div>
      </template>

      <!-- renew -->
      <template v-slot:item.renew="{ item }">
        <div class="font-12px fw-400">{{ item?.renew ? item.renew : '-' }}</div>
      </template>

      <!-- action -->
      <template v-slot:item.action="{ item }">
        <div class="font-12px fw-400 cursor-pointer" @click="openDetail(item)">
          <v-icon class="text-ff862f" color="#11aba3">$edit</v-icon>
        </div>
      </template>

      <template v-slot:item.wage="{ item }">
        <div class="font-12px fw-400">
          {{ item.wage?.toLocaleString('en-US') }}
        </div>
      </template>

      <!-- pdf -->
      <template v-slot:[`item.application_detail`]="{ item }">
        <div
          v-if="item.student"
          @click="generatePdf(item)"
          class="position-relative pl-5 cursor-pointer"
        >
          <v-icon color="#black">$FeedIcon</v-icon>
        </div>
      </template>
    </DataTable>

    <ContractDialog
      v-model:launch="launchEdit"
      :item="editItem"
      @refresh="generateItems"
      @cancelUpdate="cancelUpdate"
    />

    <SimpleModel
      :text="errorMessages"
      :dialog="dialog.errorDialog"
      :showCloseIcon="true"
      @closeModel="dialog.errorDialog = false"
      :buttonOption="{
        hideCancel: true,
        hideSubmit: true,
      }"
    ></SimpleModel>
  </div>
</template>
<script setup>
import { ref, computed, onMounted, defineAsyncComponent } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
const PageTitle = defineAsyncComponent(
  () => import('@/components/ui/PageTitle.vue')
);
const DataTable = defineAsyncComponent(
  () => import('@/components/ui/DataTable.vue')
);
const SearchArea = defineAsyncComponent(
  () => import('@/components/ui/SearchArea.vue')
);
const ContractDialog = defineAsyncComponent(
  () => import('@/components/models/ContractDialog.vue')
);
const SimpleModel = defineAsyncComponent(
  () => import('@/components/models/SimpleModel.vue')
);
import contractDetailPDF from '@/views/contract/contractDetailPdf.vue';
import Encoding from 'encoding-japanese';

import moment from 'moment';
const dateFormat = (date) => {
  return date ? moment(date).format('YYYY/MM/DD') : '---- / - / -';
};

const store = useStore();
const router = useRouter();

const toggleSearch = ref(false);
const items = ref([]);
const isDownloadPDF = ref(false);
const idContract = ref(null);
const studentId = ref(null);
const launchEdit = ref(false);
const loading = ref(false);
const editItem = ref(null);
const searchFields = ref([]);
const dialog = ref({ errorDialog: false });
const errorMessages = ref(null);
const selectTypeOptions = ref([
  {
    id: 'keyword_search',
    name: 'キーワード検索',
  },
  {
    id: 'created_at',
    name: '合格日',
  },
]);
const configuration = ref({
  page: 1,
  sort_by: 'updated_at',
  sort_by_order: 'desc',
  paginate: 25,
  is_history_page: '1',
});
const typeContracts = ref([
  { id: 1, name: 'KN' },
  { id: 2, name: '独自' },
]);

const getHeaders = computed(() => [
  {
    title: '契約ID',
    value: 'id',
    align: 'left',
    class: ['py-3', 'px-0'],
    sortable: false,
    flex: '1 1 5%', // Flex grow, flex shrink, and basis for proportional width
  },
  {
    title: '内部ID',
    subTitle: '企業名',
    align: 'left',
    class: ['py-3', 'px-0'],
    value: 'company_id',
    sortable: false,
    flex: '1 1 20%',
  },
  {
    title: '学生ID',
    subTitle: '学生名',
    class: ['py-3', 'px-0', 'text-no-wrap'],
    value: 'student_id',
    align: 'left',
    isNoWrap: true,
    sortable: false,
    flex: '1 1 15%',
  },
  {
    title: '大学名',
    subTitle: '学生メールアドレス',
    value: 'university',
    class: ['py-3', 'px-0'],
    align: 'left',
    sortable: false,
    flex: '1 1 15%',
  },
  {
    title: '契約内容',
    sortable: false,
    align: 'left',
    class: ['px-0'],
    value: 'application_detail',
    width: '9.62%',
  },
  {
    title: '合格日',
    sortable: false,
    align: 'left',
    class: ['py-3', 'px-0'],
    value: 'date_application_passed',
    flex: '1 1 15%',
  },
  {
    title: '雇用開始日',
    sortable: false,
    align: 'left',
    class: ['py-3', 'px-0'],
    value: 'date_employment_start',
    flex: '1 1 15%',
  },
  {
    title: '雇用終了日',
    sortable: false,
    align: 'left',
    class: ['py-3', 'px-0'],
    value: 'date_employment_end',
    flex: '1 1 15%',
  },
  {
    title: '時給',
    sortable: false,
    align: 'left',
    class: ['py-3', 'px-0'],
    value: 'wage',
    flex: '1 1 15%',
  },
  {
    title: '学生確認',
    sortable: false,
    align: 'left',
    class: ['py-3', 'px-0'],
    value: 'status',
    flex: '1 1 15%',
  },
  {
    title: '送付日',
    sortable: false,
    align: 'center',
    class: ['py-3', 'px-0'],
    value: 'datetime_contract_sent',
    flex: '1 1 15%',
  },
]);

// Computed properties for contract data
/**
 * Get pagination information for contract list
 * @returns {Object} Pagination data including total pages and records
 */
const getContractPagination = computed(
  () => store.getters.getContractPagination
);

/**
 * Handle contract format type change via radio button
 * Opens edit dialog for confirmation
 * @param {Object} item - Contract item being edited
 */
const handleRadioChange = async (item) => {
  launchEdit.value = true;
  editItem.value = item;
};

/**
 * Revert contract format type change if update is cancelled
 * Toggles between KN (1) and Independent (2) format types
 * @param {Object} item - Contract item to revert
 */
const cancelUpdate = (item) => {
  const index = items.value.findIndex((i) => i.id === item.id);
  if (index !== -1) {
    items.value[index] = {
      ...items.value[index],
      type_format: item.type_format === 1 ? 2 : 1,
    };
  } else {
    console.error(`Item with ID ${item.id} not found.`);
  }
};

/**
 * Export contracts data to CSV file
 * Filename format: 契約管理_YYYY-MM-DD.csv (Contract Management_Date.csv)
 */
const downloadCsv = async () => {
  await store.dispatch('CONTRACT_EXPORT_CSV');

  // Show error if no data available
  if (store.getters.getContractCsvData?.message) {
    dialog.value.errorDialog = true;
    errorMessages.value = '<div class="pt-10">データが見つかりません。</div>'; // No data found
    return;
  }

  // Get CSV data
  const csvData = store.getters.getContractCsvData.data.csv;

  // Convert UTF-8 CSV to Shift-JIS
  const sjisArray = Encoding.convert(Encoding.stringToCode(csvData), {
    to: 'SJIS',
    from: 'UNICODE',
  });

  const uint8Array = new Uint8Array(sjisArray);
  const blob = new Blob([uint8Array], { type: 'text/csv;charset=Shift_JIS' });

  const fileUrl = window.URL.createObjectURL(blob);
  const fileLink = document.createElement('a');
  fileLink.href = fileUrl;
  fileLink.setAttribute(
    'download',
    `契約管理_送付履歴_${new Date().toISOString().slice(0, 10)}.csv`
  );
  document.body.appendChild(fileLink);
  fileLink.click();
  document.body.removeChild(fileLink);
};

/**
 * Update table configuration based on sorting and pagination changes
 * @param {Object} e - Event object containing sort and page information
 */
const updateTable = async (e) => {
  // Update sort settings with fallback to default
  configuration.value.sort_by =
    e && e.sortBy && e.sortBy.length && e?.sortBy[0]?.key
      ? e?.sortBy[0]?.key
      : 'updated_at';
  configuration.value.sort_by_order =
    e && e.sortBy && e.sortBy.length && e?.sortBy[0]?.order
      ? e?.sortBy[0]?.order
      : 'desc';
  configuration.value.page = typeof e === 'number' ? e : (e?.page ?? 1);
  await generateItems();
};

/**
 * Handle tab change in contract list
 * @param {number} tab - Tab identifier (2: Draft, others: Normal status)
 */
const resultOnTab = async (tab) => {
  configuration.value.page = 1;
  configuration.value.status = tab;
  await generateItems();
  resetPagination();
};

/**
 * Reset search parameters and refresh contract list
 * Clears all search filters and returns to first page
 */
const updateSearchResults = async () => {
  toggleSearch.value = false;
  configuration.value.page = 1;
  configuration.value.search = null;
  configuration.value.date_from = null;
  configuration.value.date_to = null;
  await generateItems();
  resetPagination();
};

/**
 * Fetch contract items based on current configuration
 * Handles special case for draft contracts (status 2)
 */
const generateItems = async () => {
  loading.value = true;
  items.value = [];
  delete configuration.value.draft;
  // Special handling for draft contracts
  if (configuration.value.status === 2) {
    delete configuration.value.status;
    configuration.value.draft = 1;
  }

  try {
    const response = await store.dispatch(
      'CONTRACT_GET_ALL',
      configuration.value
    );
    items.value = response.data.data;
  } catch (error) {
    console.error('Error fetching contract data:', error);
  } finally {
    loading.value = false; // Set loading to false after the API call completes (success or failure)
  }
};

/**
 * Update search fields based on search type
 * @param {string} inputSearchType - Type of search (keyword_search or created_at)
 */
const setChangedInputType = (inputSearchType) => {
  if (inputSearchType === 'keyword_search') {
    // Text search for company/student information
    searchFields.value = [
      {
        label: 'Search text',
        name: 'search',
        type: 'text',
        value: null,
        placeholder: '企業ID、企業名、学生ID、学生名', // Company ID, Company name, Student ID, Student name
      },
    ];
  } else if (inputSearchType === 'created_at') {
    // Date range search with Japanese locale
    searchFields.value = [
      {
        label: 'Label',
        name: 'date_from',
        type: 'date',
        rules: 'required',
        show_after_approx: true,
        value: moment().format('YYYY-MM-DD'),
        menu: false,
        locale: 'ja',
        date_format: 'YYYY-MM-DD',
      },
      {
        label: 'Label',
        name: 'date_to',
        type: 'date',
        rules: 'required',
        show_after_approx: false,
        value: moment().format('YYYY-MM-DD'),
        menu: false,
        locale: 'ja',
        range: true,
        range_input: 'date_from',
        date_format: 'YYYY-MM-DD',
      },
    ];
  }
};

/**
 * Get color for status chip based on contract status and format type
 * @param {number} status - Contract status (1: In Preparation, 2: Pending, 3: Confirmed, 4: Expired)
 * @param {number} type_format - Contract format type (1: KN, 2: Independent)
 * @returns {string} Hex color code for the status chip
 */
const chipColor = (status, type_format) => {
  if (status === 3 && type_format === 2) {
    return '#E5E5E5'; // Gray - Independent format doesn't need confirmation
  }
  switch (status) {
    case 1:
      return '#8B8000'; // Yellow - Contract in preparation
    case 2:
      return '#EE6C9B'; // Pink - Waiting for confirmation
    case 3:
      return '#60D1CB'; // Teal - Confirmed
    case 4:
      return '#A7A7A7'; // Blue - Contract expired
  }
};

/**
 * Get status text based on contract status and format type
 * @param {number|string} value - Contract status
 * @param {number} type_format - Contract format type (1: KN, 2: Independent)
 * @returns {string} Localized status text in Japanese
 */
const getStatus = (value, type_format) => {
  const status = parseInt(value);
  if (status === 3 && type_format === 2) {
    return '-'; // No confirmation needed for independent format
  }
  switch (status) {
    case 1:
      return '契約準備中  '; // Contract in preparation
    case 2:
      return '確認待ち'; // Waiting for confirmation
    case 3:
      return '確認済み'; // Confirmed
    case 4:
      return '契約満了'; // Contract expired
  }
};

/**
 * Navigate to contract detail page
 * @param {Object} item - Contract item containing ID
 */
const openDetail = (item) => {
  router.push(`uncontract-detail/${item.id}`);
};

/**
 * Handle search form submission
 * Updates configuration with search parameters and refreshes list
 * @param {Object} $event - Event object containing search fields
 */
const searchSubmit = async ($event) => {
  let obj = {};
  if ($event.fields.length > 0) {
    $event.fields.forEach((field) => {
      obj[field.name] = field.value;
    });
  }
  configuration.value = {
    ...configuration.value,
    ...obj,
  };
  await generateItems();
  resetPagination();
};

/**
 * Reset pagination to first page
 */
const resetPagination = () => {
  configuration.value.page = 1;
};
const pdfDocumentContractAdmin = ref(null);
const generatePdf = async (item) => {
  await store.dispatch('CONTRACT_GET', { id: item?.id, isPDF: true });
  store.dispatch('API_PROCESSING', true); // Start loading
  studentId.value = item.student_id;
  idContract.value = item.id;
  isDownloadPDF.value = true;
  setTimeout(() => {
    // Use .value to access the ref's value
    pdfDocumentContractAdmin.value.generatePdf();
    isDownloadPDF.value = false;
  }, 1000);
};

onMounted(() => {});
</script>
<style lang="scss">

</style>

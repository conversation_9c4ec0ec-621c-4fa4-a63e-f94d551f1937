<template>
  <div class="admin-edit-page">
    <PageTitle
      :items="{
        title: '管理者',
        subTitle: '管理者編集',
        back: {
          action: () => {
            $router.push({ name: 'AdminListing' });
          },
        },
      }"
    ></PageTitle>
    <v-card class="pa-5" v-if="isDataLoaded">
      <Form
        @submit="handleFormSubmit"
        :validation-schema="schema"
        :initial-values="fields"
      >
        <div class="my-6">
          <v-row>
            <v-col cols="12" md="8" class="mx-auto">
              <div class="d-flex text-7d mb-2">
                <span class="min-width-75px">登録日</span>
                <span class="min-width-75px">{{ createdAt }}</span>
              </div>

              <v-col cols="12" md="12" class="mb-4 mt-6">
                <label class="d-block font-14px">
                  <span>管理者氏名</span>
                  <span class="error--text ml-2 font-12px">必須</span>
                </label>
                <Field name="name" v-slot="{ field, errors }">
                  <v-text-field
                    v-bind="field"
                    :error-messages="errors"
                    :error="errors.length !== 0"
                    :hide-details="errors.length <= 0"
                    density="compact"
                    variant="outlined"
                  ></v-text-field>
                </Field>
              </v-col>

              <v-col cols="12" md="12" class="mb-4">
                <label class="d-block font-14px">
                  <span>メールアドレス</span>
                  <span class="error--text ml-2 font-12px">必須</span>
                </label>
                <Field name="email" v-slot="{ field, errors }">
                  <v-text-field
                    v-bind="field"
                    :error-messages="errors"
                    :error="errors.length !== 0"
                    :hide-details="errors.length <= 0"
                    density="compact"
                    variant="outlined"
                    placeholder=""
                  ></v-text-field>
                </Field>
              </v-col>

              <v-col cols="12" md="12" class="mb-4 d-flex justify-center">
                <Field name="status" v-slot="{ field, errors }">
                  <v-switch
                    :model-value="switchValue"
                    @update:modelValue="onSwitchChange"
                    :error-messages="errors"
                    :error="errors.length !== 0"
                    :hide-details="errors.length <= 0"
                    outlined
                    :label="switchValue ? 'アクティブ' : '非アクティブ'"
                    color="#13ABA3"
                    class="mt-0"
                  ></v-switch>
                </Field>
              </v-col>

              <v-col>
                <div class="admin-footer-action text-center mt-6">
                  <v-btn
                    text
                    height="0"
                    class="delete-btn font-14px pa-0 ml-2 text-red"
                    @click.prevent="dialog.delete = true"
                  >
                    <v-icon size="18" class="mr-2">$WarningRed</v-icon>
                    削除
                  </v-btn>
                  <v-btn
                    type="submit"
                    :loading="loading"
                    max-width="188px"
                    width="100%"
                    height="35"
                    class="btn-primary white--text"
                    >更新</v-btn
                  >
                </div>
              </v-col>
            </v-col>
          </v-row>
        </div>
      </Form>
    </v-card>

    <SuccessModel
      :text="`企業情報を登録しました。`"
      :buttonText="`企業一覧へ戻る`"
      :routeName="`AdminListing`"
      :dialog="dialog.success"
      @closeModel="dialog.success = false"
    ></SuccessModel>

    <SimpleModel
      :text="`この管理者情報を削除しますか？`"
      :dialog="dialog.delete"
      :loading="loading"
      :submitButtonText="`削除`"
      @submitSuccess="deleteAdmin"
      @closeModel="dialog.delete = false"
    ></SimpleModel>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useStore } from 'vuex';
import { Form, Field } from 'vee-validate';
import SuccessModel from '@/components/models/SuccessModel.vue';
import SimpleModel from '@/components/models/SimpleModel.vue';
import * as yup from 'yup';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

const store = useStore();
const { t } = useI18n();
const route = useRoute();
const router = useRouter();

const switchValue = ref(false);
const onSwitchChange = (value) => {
  switchValue.value = value; // Update the local switch state
  fields.value.status = value; // Update the Vee-Validate form value manually
};

// Validation schema
const schema = yup.object().shape({
  name: yup
    .string()
    .required(t('field_required_message', { field: '管理者氏名' })),
  email: yup
    .string()
    .required(t('field_required_message', { field: 'メールアドレス' }))
    .email(t('field_email_message')),
  status: yup.boolean().required(),
});

// State and variables
const loading = ref(false);
const dialog = ref({
  delete: false,
  success: false,
  submit: false,
});
const fields = ref({
  name: '',
  email: '',
  status: false,
});
const createdAt = ref('----/--/--');

// Fetch data on mounted
onMounted(async () => {
  await getDataFromApi();
});

// Method to fetch data
const getDataFromApi = async () => {
  try {
    const adminId = route.params.id;
    loading.value = true;
    await store.dispatch('ADMINS_GET', { id: adminId });
    loading.value = true;
    setPageData();
  } finally {
    loading.value = false;
  }
};

// Watch the fields to detect when they are initialized
const isDataLoaded = computed(() => {
  return fields.value.name !== '' || fields.value.email !== ''; // Update the condition based on what constitutes "loaded"
});

// Set the page data
const setPageData = () => {
  const adminData = store.getters.getAdmin;
  fields.value.name = adminData.name || '';
  fields.value.email = adminData.email || '';
  fields.value.status = adminData.status === 1 ? true : false;
  createdAt.value = adminData.created_at
    ? formatDate(adminData.created_at)
    : '----/--/--';
  switchValue.value = fields.value.status; // Set the initial value for the switch
};

// Format date function
const formatDate = (date) => {
  const options = { year: 'numeric', month: '2-digit', day: '2-digit' };
  return new Date(date).toLocaleDateString('ja-JP', options);
};

// Submit form handler
const handleFormSubmit = async (values) => {
  loading.value = true;
  try {
    values.status = switchValue.value; // Update the status value from the local switchValue
    const data = {
      id: route.params.id,
      ...values,
    };
    await store.dispatch('ADMINS_UPDATE', data);
    dialog.value.success = true;
  } catch (error) {
    if (error.response && error.response.status === 422) {
      console.error(error);
    }
  } finally {
    loading.value = false;
  }
};

// Delete admin function
const deleteAdmin = async () => {
  loading.value = true;
  try {
    const adminId = route.params.id;
    await store.dispatch('ADMINS_DELETE', { id: adminId });
    router.push({ name: 'AdminListing' });
  } finally {
    loading.value = false;
  }
};
</script>

<style lang="scss" scoped src="./styles.scss"></style>

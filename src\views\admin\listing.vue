<template>
  <div
    v-show="!loading && !$store.getters.getApiProcessingStatus"
    class="font-Noto-Sans job-list-page"
  >
    <PageTitle
      :items="{
        title: '管理者',
        subTitle: '管理者一覧',
        buttons: [
          {
            title: '新規作成',
            icon: 'mdi-plus-circle',
            action: () => {
              $router.push({
                name: 'AdminsCreate',
              });
            },
          },
        ],
      }"
    ></PageTitle>
    <v-sheet color="transparent">
      <v-row>
        <v-col cols="12" md="12" class="d-flex w-100">
          <DataTable
            :items="dataWithPartialEmail(admins)"
            :headers="headers"
            @row-clicked="
              $router.push({ name: 'AdminsEdit', params: { id: $event.id } })
            "
          >
            <template v-slot:[`item.action`]="{ item }">
              <v-btn
                height="23px"
                width="113px"
                class="rounded-xl font-12px white--text"
                depressed
                :color="item.status == 1 ? '#4BCFA0' : '#BCBCBC'"
              >
                {{ item.status == 1 ? 'アクティブ' : 'インアクティブ' }}
              </v-btn>
            </template>

            <template v-slot:[`item.created_at`]="{ item }">
              <div class="py-4">{{ formatDate(item.created_at) }}</div>
            </template>
          </DataTable>
        </v-col>
      </v-row>
    </v-sheet>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, defineAsyncComponent } from 'vue';
import { useStore } from 'vuex';
const DataTable = defineAsyncComponent(
  () => import('@/components/ui/DataTable.vue')
);
import moment from 'moment';

// Define headers as a constant
const headers = [
  {
    title: '管理者名',
    align: 'left',
    sortable: false,
    value: 'name',
    width: '15%',
  },
  {
    title: 'メールアドレス',
    align: 'left',
    sortable: false,
    value: 'email',
    width: '60%',
  },
  {
    title: '登録日',
    align: 'left',
    sortable: false,
    value: 'created_at',
    width: '5.76%',
  },
  {
    title: '',
    align: 'center',
    sortable: false,
    value: 'action',
    width: '16.7%',
  },
];

// Vuex store
const store = useStore();

// Fetch data from the API
const getDataFromApi = async () => {
  await store.dispatch('ADMINS_GET_ALL');
};

// Get all admins from the Vuex store
const admins = computed(() => store.getters.getAllAdmins);

// Format email addresses to partially hide them
const dataWithPartialEmail = (data) => {
  return data.map((item) => {
    let emailSplit = item.email.split('@');
    let emailSplit1 = emailSplit[0];
    let emailSplit2 = emailSplit[1];
    let hiddenEmail1 = emailSplit1.substring(0, 3);
    let hiddenEmail2 = emailSplit2.substring(0, 3);
    item.email = `${hiddenEmail1}*****@${hiddenEmail2}*****`;
    return item;
  });
};

// Format date
const formatDate = (date) => {
  if (!date) {
    return '----/--/--';
  }
  return moment(date).format('YYYY/MM/DD');
};

// Fetch data when the component is mounted
const loading = ref(false);
onMounted(async () => {
  loading.value = true;
  await getDataFromApi();
  loading.value = false;
});
</script>

<style lang="scss" scoped src="./styles.scss"></style>

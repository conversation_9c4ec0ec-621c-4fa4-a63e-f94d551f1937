import axios from '@/services/api';

import {
  INTERNSHIP_STUDENT_REQUEST,
  INTERNSHIP_STUDENT_GET_ALL,
  INTERNSHIP_STUDENT_GET_ALL_SUCCESS,
  INTERNSHIP_STUDENT_UPDATE,
  INTERNSHIP_STUDENT_GET,
  INTERNSHIP_STUDENT_GET_SUCCESS,
} from '@/store/actions/internship_student';

function initialState() {
  return {
    status: '',
    internshipStudent: [],
    singleInternshipStudent: {},
    internshipStudentPagination: null,
    csvData: null,
    internshipStudentCounts: {},
  };
}

const state = initialState();

const getters = {
  getAllInternshipStudent: (state) => state.internshipStudent,
  getSingleInternshipStudent: (state) => state.singleInternshipStudent,
  getInternshipStudentPagination: (state) => state.internshipStudentPagination,
  getInternshipStudentCategories: (state) => state.internshipStudentCategories,
  getInternshipStudentCsvData: (state) => state.csvData,
  getInternshipStudentCounts: (state) => state.internshipStudentCounts,
};

const actions = {
  ['INTERNSHIP_STUDENT_EXPORT_CSV']: ({ commit, dispatch }) => {
    dispatch('API_PROCESSING', true, { root: true });
    return new Promise((resolve, reject) => {
      axios
        .get('/admin/export/internship-student')
        .then((response) => {
          commit(
            'INTERNSHIP_STUDENT_EXPORT_CSV_SUCCESS',
            response.data
          );
          resolve(response);
        })
        .catch((error) => {
          reject(error);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  [INTERNSHIP_STUDENT_GET_ALL]: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('INTERNSHIP_STUDENT_REQUEST');
      axios
        .get(`/admin/internship-students`, { params })
        .then((response) => {
          commit('INTERNSHIP_STUDENT_GET_ALL_SUCCESS', response.data);
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  [INTERNSHIP_STUDENT_GET]: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('INTERNSHIP_STUDENT_REQUEST');
      axios
        .get(`/admin/internship-students/${params.id}`, { params })
        .then((response) => {
          commit('INTERNSHIP_STUDENT_GET_SUCCESS', response.data);
          resolve(response);
        })
        .catch((err) => {
          commit('INTERNSHIP_STUDENT_GET_ERROR');
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  [INTERNSHIP_STUDENT_UPDATE]: ({ dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      axios
        .post(`/admin/update-temporary-stop/${params.id}`, params)
        .then((response) => {
          resolve(response);
        })
        .catch((error) => {
          reject(error);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },
};

const mutations = {
  ['INTERNSHIP_STUDENT_EXPORT_CSV_SUCCESS']: (state, payload) => {
    state.csvData = payload;
  },
  [INTERNSHIP_STUDENT_REQUEST]: (state) => {
    state.status = 'loading';
  },

  [INTERNSHIP_STUDENT_GET_ALL_SUCCESS]: (state, payload) => {
    state.status = 'success';
    state.internshipStudent = payload.data;
    state.internshipStudentPagination = payload.paginate;
    state.internshipStudentCounts = payload.temporary_stop_counts;
  },

  [INTERNSHIP_STUDENT_GET_SUCCESS]: (state, payload) => {
    state.status = 'success';
    state.singleInternshipStudent = payload.data;
  },
};

export default {
  state,
  getters,
  actions,
  mutations,
};

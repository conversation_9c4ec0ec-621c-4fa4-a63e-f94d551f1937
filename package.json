{"name": "kt-web-admin-vue3", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@date-io/date-fns": "^3.0.0", "@mdi/font": "^7.4.47", "@vee-validate/i18n": "^4.13.2", "@vee-validate/rules": "^4.13.2", "@vueup/vue-quill": "^1.2.0", "axios": "^0.21.4", "chart.js": "^3.7.1", "chartjs-adapter-moment": "^1.0.0", "core-js": "^3.18.1", "date-fns": "^3.6.0", "debounce": "^1.2.1", "encoding-japanese": "^2.2.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "moment": "^2.29.2", "quill-resize-image": "^1.0.5", "v-mask": "^2.3.0", "vanilla-autokana": "^1.3.0", "vee-validate": "^4.13.2", "vite-plugin-vuetify": "^2.0.4", "vue": "^3.5.11", "vue-cropperjs": "^5.0.0", "vue-i18n": "^9.2.2", "vue-meta": "^3.0.0-alpha.2", "vue-observe-visibility": "^1.0.0", "vue-router": "^4.1.6", "vuejs-paginate-next": "^1.0.2", "vuetify": "^3.1.0", "vuex": "^4.1.0", "vuex-persistedstate": "^4.1.0", "yup": "^1.4.0"}, "devDependencies": {"@types/node": "^22.7.4", "@vitejs/plugin-vue": "^5.1.2", "path": "^0.12.7", "prettier": "^3.3.3", "sass": "^1.85.1", "sass-embedded": "^1.79.4", "sass-loader": "^16.0.5", "typescript": "^5.5.3", "vite": "^5.4.1", "vue-tsc": "^2.0.29"}}
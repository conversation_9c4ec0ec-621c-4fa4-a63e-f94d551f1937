import axios from '@/services/api';

import {
  INTERESTED_JOB_REQUEST,
  INTERESTED_JOB_GET_ALL,
  INTERESTED_JOB_GET_ALL_SUCCESS,
  INTERESTED_JOB_GET_ALL_ERROR,
  INTERESTED_JOB_GET_SUCCESS,
  INTERESTED_JOB_DELETE,
  INTERESTED_JOB_EDIT,
  INTERESTED_JOB_CREATE,
} from '@/store/actions/interested_job.js';

function initialState() {
  return {
    status: '',
    interestedJobs: [],
    singleInterestedJob: {},
    interestedJobsPagination: null,
  };
}

const state = initialState();

const getters = {
  getAllInterestedJob: (state) => state.interestedJobs,
  getSingleInterestedJob: (state) => state.singleInterestedJob,
  getInterestedJobPagination: (state) => state.interestedJobsPagination,
};

const actions = {
  [INTERESTED_JOB_GET_ALL]: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('INTERESTED_JOB_REQUEST');
      axios
        .get(`/admin/interested-jobs`, { params })
        .then((response) => {
          commit('INTERESTED_JOB_GET_ALL_SUCCESS', response.data);
          resolve(response);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },
  [INTERESTED_JOB_EDIT]: ({ dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      axios
        .put(`/admin/interested-jobs/${params.id}`, params)
        .then((response) => {
          resolve(response);
        })
        .catch((error) => {
          reject(error);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },
  [INTERESTED_JOB_CREATE]: ({ dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      axios
        .post(`/admin/interested-jobs`, params)
        .then((response) => {
          resolve(response);
        })
        .catch((error) => {
          reject(error);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },
  [INTERESTED_JOB_DELETE]: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('INTERESTED_JOB_REQUEST');
      axios
        .delete(`/admin/interested-jobs/${params}`)
        .then((response) => {
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },
};

const mutations = {
  [INTERESTED_JOB_REQUEST]: (state) => {
    state.status = 'loading';
  },

  [INTERESTED_JOB_GET_ALL_SUCCESS]: (state, payload) => {
    state.status = 'success';
    state.interestedJobs = payload.data.interested_jobs || [];
    state.interestedJobsPagination = payload.data.paginate;
  },

  [INTERESTED_JOB_GET_SUCCESS]: (state, payload) => {
    state.status = 'success';
    state.singleInterestedJob = payload.data.interested_jobs;
  },
};

export default {
  state,
  getters,
  actions,
  mutations,
};

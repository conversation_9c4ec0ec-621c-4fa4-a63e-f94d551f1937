<template>
  <div
    class="bg-f9f9f9"
    :style="cardHeight"
    :class="{
      'px-15 py-15': smAndUp,
      'px-5 py-10': xs,
    }"
  >
    <div v-if="mdAndUp" class="position-absolute desktop-cover">
      <img :src="logoUrl" alt="404_vector" />
    </div>
    <div
      class="d-flex align-center"
      :class="{
        'full-height': smAndUp,
        'flex-wrap': smAndDown,
      }"
    >
      <div class="flex-grow-1">
        <div
          class="text-1f2020"
          :class="{
            'font-96px fw-600': smAndUp,
            'font-48px text-center fw-500': xs,
          }"
        >
          404
        </div>

        <div
          class="text-1f2020 mt-10-reverse"
          :class="{
            'font-36px ': smAndUp,
            'font-16px text-center': xs,
          }"
        >
          Not Found
        </div>

        <div
          v-if="smAndUp"
          class="text-000 Helvetica-Medium mt-1"
          :class="{
            'font-20px': smAndUp,
            'font-16px text-center': xs,
          }"
        >
          お探しのページは見つかりませんでした
        </div>

        <div
          class="text-8e"
          :class="{
            'font-16px mt-7': smAndUp,
            'font-14px text-center mt-5 px-10': xs,
          }"
        >
          申し訳ございませんが、 お探しのページが見つかりませんでした。<br />
          お探しのページは削除されたか、 URLが変更された可能性がございます。
        </div>

        <div
          v-if="smAndUp"
          class="text-primary"
          :class="{
            'font-16px mt-7': smAndUp,
            'font-14px d-flex justify-center  mt-5': xs,
          }"
        >
          <v-btn
            width="230px"
            height="46px"
            variant="outlined"
            color="primary"
            @click="$router.push({ name: 'Dashboard' })"
          >
            <div class="d-flex align-center">
              <v-icon>$HomeErrorIcon</v-icon>
              <div class="pl-1 pt-1">TOPへもどる</div>
            </div></v-btn
          >
        </div>
      </div>
      <div class="flex-grow-1 d-flex justify-center phone-cover">
        <div v-if="mdAndUp" class="mt-10">
          <img :src="logoUrl1" class="desktop_404" alt="404_cover_image" />
        </div>
        <div v-else class="d-flex justify-center mt-5">
          <img :src="logoUrl2" class="mobile_404" alt="404_cover_image" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useDisplay } from 'vuetify';
import { ref, computed, onMounted } from 'vue';
const { mdAndUp, smAndDown, smAndUp, xs } = useDisplay();

const logoUrl = ref(
  new URL('@/assets/images/404_vector.png', import.meta.url).href
);
const logoUrl1 = ref(
  new URL('@/assets/images/404_cover.png', import.meta.url).href
);
const logoUrl2 = ref(
  new URL('@/assets/images/404_cover_mobile.png', import.meta.url).href
);

const cardHeight = computed(() => 'height: 100vh');

const pageTitle = '404 Not Found';
const pageUrl = import.meta.url + window.location.pathname;

const setHead = () => {
  document.title = pageTitle;

  // canonical link タグの操作
  const canonicalLink = document.querySelector('link[rel="canonical"]');
  if (canonicalLink) {
    canonicalLink.setAttribute('href', pageUrl);
  } else {
    const link = document.createElement('link');
    link.rel = 'canonical';
    link.href = pageUrl;
    document.head.appendChild(link);
  }
};

onMounted(() => {
  setHead();
});
</script>

<style src="./Error.scss" lang="scss"></style>

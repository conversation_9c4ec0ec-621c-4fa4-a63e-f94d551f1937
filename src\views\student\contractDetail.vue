<template>
  <div ref="pdfContract" class="d-flex justify-center">
    <div
      class="mb-15"
      :class="{
        'px-4 container-details': mdAndUp,
        'px-4': smAndDown,
      }"
    >
      <div
        class="relative mt-12 mb-6"
        :class="{ 'd-flex justify-center': mdAndUp }"
      >
        <div class="text-center text-97 font-16px">
          契約内容を確認して、画面下部の承認ボタンを押してください。
        </div>
        <div
          :class="{
            'd-flex justify-end mt-4 mb-4': smAndDown,
          }"
        >
          <div
            :class="{ 'pos-download-pdf': mdAndUp }"
            class="text-529AEE font-16px mb-2px cursor-pointer"
            @click="generatePdf"
          >
            PDF表示
            <span>
              <v-icon size="20px" color="#529AEE" style="margin-bottom: 1px">
                mdi mdi-open-in-new
              </v-icon>
            </span>
          </div>
        </div>
      </div>
      <div
        flat
        color="white"
        class="borderradius-10"
        :class="{
          'px-14 py-16': mdAndUp,
          'px-5 py-8': smAndDown,
        }"
      >
        <contractDetailPDF
          :id="id"
          v-if="isDownloadPDF"
          ref="pdfDocumentContractAdmin"
        />
        <!-- header 1 -->
        <div
          :class="{
            'd-flex justify-space-between': mdAndUp,
            '': smAndDown,
          }"
        >
          <div
            class="name-uni"
            :class="{
              'font-16px': mdAndUp,
              'font-14px d-inline': smAndDown,
            }"
          >
            <span>{{ getSingleStudent?.education_facility.name }}</span>
            <span class="ml-2"
              >{{ getSingleStudent.family_name }}
              {{ getSingleStudent.first_name }} <span class="ml-2"></span>
              <span class="ml-2"></span> 殿
            </span>
          </div>
          <div
            class="w-full font-16px"
            :class="{
              'mt-1': smAndDown,
            }"
          >
            {{
              contract.datetime_contract_sent
                ? formattedDate(contract.datetime_contract_sent)
                : ''
            }}
          </div>
        </div>
        <!-- header 2 -->
        <div
          class="d-flex justify-center"
          :class="{
            'font-24px': mdAndUp,
            'font-16x mt-6': smAndDown,
          }"
        >
          労働条件通知書
        </div>
        <!-- header 3 -->
        <div
          class="mt-4"
          :class="{
            'font-16px d-flex justify-center ': mdAndUp,
            'font-14px': smAndDown,
          }"
        >
          <div>
            <!-- type contract -->
            <v-checkbox
              v-model="contract.type_contract_value"
              :value="1"
              :ripple="false"
              dense
              :hide-details="true"
              color="#8E8E8E"
              class="pa-0 ma-0 login-modal cancel-mouse-event"
            >
              <template #label>
                <div class="fw-400 pa-0 text-black">新規</div>
              </template>
            </v-checkbox>
          </div>
          <div>
            <!-- type contract -->
            <v-checkbox
              v-model="contract.type_contract_value"
              :value="2"
              :ripple="false"
              dense
              :hide-details="true"
              color="#8E8E8E"
              class="pa-0 ma-0 login-modal cancel-mouse-event"
              :class="{
                'ml-8': mdAndUp,
                'font-14px': smAndDown,
              }"
            >
              <template #label>
                <div class="fw-400 pa-0 text-black">
                  更新
                  <span class="ml-6" v-if="contract.date_contract_signing"
                    >({{ formattedDate(contract.date_contract_signing) }})</span
                  >
                  <span class="ml-6" v-else
                    >(<span class="mr-4">西暦</span>
                    <span class="mr-4">年</span>
                    <span class="mr-4">月</span> 日)
                  </span>
                </div>
              </template>
            </v-checkbox>
          </div>
        </div>

        <!-- header 4 -->
        <v-row
          class="mt-6"
          justify="end"
          :style="{ padding: '0', margin: '0' }"
        >
          <v-col cols="12" md="6" :style="{ padding: '0', margin: '0' }">
            <v-row
              :style="{
                fontSize: mdAndUp ? '16px' : '14px',
                padding: '0',
                margin: '0',
              }"
            >
              <v-col cols="12" md="4" :style="{ padding: '0', margin: '0' }">
                <p :style="{ padding: '0', margin: '0' }">事業所所在地</p>
              </v-col>
              <v-col cols="12" md="8" :style="{ padding: '0', margin: '0' }">
                <p
                  :style="{
                    padding: '0',
                    margin: '0',
                    borderBottom: '1px solid black',
                  }"
                  :class="{
                    'text-center': mdAndUp,
                    'tex-left d-inline px-2': smAndDown,
                  }"
                >
                  {{ contract?.business_address }}
                </p>
              </v-col>
              <v-col cols="12" md="4" :style="{ padding: '0', margin: '0' }">
                <p :style="{ padding: '0', margin: '0' }">名称</p>
              </v-col>
              <v-col cols="12" md="8" :style="{ padding: '0', margin: '0' }">
                <p
                  :style="{
                    padding: '0',
                    margin: '0',
                    borderBottom: '1px solid black',
                  }"
                  :class="{
                    'text-center': mdAndUp,
                    'tex-left d-inline px-2': smAndDown,
                  }"
                >
                  {{ contract?.company?.name }}
                </p>
              </v-col>
              <v-col cols="12" md="4" :style="{ padding: '0', margin: '0' }">
                <p :style="{ padding: '0', margin: '0' }">使用者職氏名</p>
              </v-col>
              <v-col cols="12" md="8" :style="{ padding: '0', margin: '0' }">
                <p
                  :style="{
                    padding: '0',
                    margin: '0',
                    borderBottom: '1px solid black',
                  }"
                  :class="{
                    'text-center': mdAndUp,
                    'tex-left d-inline px-2': smAndDown,
                  }"
                >
                  {{ contract?.employee_title_name || '-' }}
                </p>
              </v-col>
            </v-row>
          </v-col>
        </v-row>

        <!-- main table -->
        <v-row justify="center">
          <v-col cols="12" class="text-black">
            <!-- row 1 -->
            <v-row
              :class="{
                'font-16px': mdAndUp,
                'font-14x': smAndDown,
              }"
            >
              <v-col
                cols="12"
                md="3"
                :class="{
                  'pl-6': mdAndUp,
                  'font-14px': smAndDown,
                }"
                class="table-header-left d-flex align-center"
              >
                雇用期間
              </v-col>
              <v-col cols="12" md="9" class="table-header-right">
                <v-row>
                  <v-col cols="12">
                    <div class="d-flex align-center">
                      <v-checkbox
                        v-model="contract.type_employment_period"
                        :ripple="false"
                        :value="2"
                        dense
                        :hide-details="true"
                        color="#8E8E8E"
                        class="pa-0 ma-0 login-modal cancel-mouse-event"
                      >
                        <template #label>
                          <div
                            class="fw-400 pa-0 text-black"
                            :class="{
                              'font-16px': mdAndUp,
                              'font-14px': smAndDown,
                            }"
                          >
                            期間の定めあり
                            <span v-if="contract.type_employment_period === 2"
                              >（{{
                                formattedDate(contract.date_employment_start)
                              }}～{{
                                formattedDate(contract.date_employment_end)
                              }}）</span
                            >
                          </div>
                        </template>
                      </v-checkbox>
                    </div>
                    <div class="d-flex align-center w-full">
                      <v-checkbox
                        v-model="contract.type_employment_period"
                        :value="1"
                        :ripple="false"
                        dense
                        :hide-details="true"
                        color="#8E8E8E"
                        class="pa-0 ma-0 login-modal cancel-mouse-event"
                      >
                        <template #label>
                          <div
                            class="fw-400 pa-0 text-black"
                            :class="{
                              'font-16px': mdAndUp,
                              'font-14px': smAndDown,
                            }"
                          >
                            期間の定めなし<span v-show="false"
                              >（雇入れ日：令和&nbsp;&nbsp;&nbsp;年&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;日））</span
                            >
                            <!--  need confirm reiwa year -->
                          </div>
                        </template>
                      </v-checkbox>
                    </div>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
            <!-- row 2 -->
            <v-row
              :class="{
                'font-16px': mdAndUp,
                'font-14x': smAndDown,
              }"
            >
              <v-col
                cols="12"
                md="3"
                :class="{
                  'pl-6': mdAndUp,
                  'font-14px': smAndDown,
                }"
                class="table-header-left-2 d-flex align-center"
              >
                <div>
                  <div>更新の有無</div>
                  <div>※雇用期間について</div>
                  <div>「期間の定めあり」と</div>
                  <div>した場合</div>
                </div>
              </v-col>
              <v-col cols="12" md="9" class="table-header-right-2">
                <v-row>
                  <v-col
                    cols="12"
                    :class="{
                      'font-16px': mdAndUp,
                      'font-14px': smAndDown,
                    }"
                  >
                    1. 契約の更新の有無
                    <div class="d-flex align-center ml-8">
                      [
                      <div class="d-flex align-center w-full">
                        <v-checkbox
                          v-model="contract.type_update_procedure"
                          :value="1"
                          :ripple="false"
                          dense
                          :hide-details="true"
                          color="#8E8E8E"
                          class="pa-0 ma-0 login-modal pt-1"
                        >
                          <template #label>
                            <div
                              class="fw-400 pa-0 text-black"
                              :class="{
                                'font-16px': mdAndUp,
                                'font-14px': smAndDown,
                              }"
                            >
                              更新する場合があり得る
                            </div>
                          </template>
                        </v-checkbox>
                      </div>
                      <div class="d-flex align-center">
                        <v-checkbox
                          v-model="contract.type_update_procedure"
                          :value="2"
                          :ripple="false"
                          dense
                          :hide-details="true"
                          color="#8E8E8E"
                          class="pa-0 ma-0 login-modal pt-1"
                        >
                          <template #label>
                            <div
                              class="fw-400 pa-0 text-black"
                              :class="{
                                'font-16px': mdAndUp,
                                'font-14px': smAndDown,
                              }"
                            >
                              契約の更新はしない
                            </div>
                          </template>
                        </v-checkbox>
                      </div>
                      ]
                    </div>
                    2. 契約の更新は、次のいずれかにより判断する
                    <!--  need confirm contract renewal -->
                    <ul class="ml-4">
                      <li>契約期間満了時の業務量</li>
                      <li>従事している業務の進捗状況</li>
                      <li>本人の能力、業務成績、勤務態度</li>
                      <li>会社の経営状況</li>
                    </ul>
                    3. 更新上限 有（更新４回まで／通算契約期間５年まで）
                    <!--  need confirm contract renewal limit -->
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
            <!-- row 3 -->
            <v-row
              :class="{
                'font-16px': mdAndUp,
                'font-14x': smAndDown,
              }"
            >
              <v-col
                cols="12"
                md="3"
                :class="{
                  'pl-6': mdAndUp,
                  'font-14px': smAndDown,
                }"
                class="table-header-left-2 d-flex align-center"
              >
                <div>就業の場所</div>
              </v-col>
              <v-col cols="12" md="9" class="table-header-right-2">
                <v-row>
                  <v-col
                    cols="12"
                    :class="{
                      'font-16px': mdAndUp,
                      'font-14px': smAndDown,
                    }"
                  >
                    {{ contract.work_place }}
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
            <!-- row 4 -->
            <v-row
              :class="{
                'font-16px': mdAndUp,
                'font-14x': smAndDown,
              }"
            >
              <v-col
                cols="12"
                md="3"
                :class="{
                  'pl-6': mdAndUp,
                  'font-14px': smAndDown,
                }"
                class="table-header-left-2 d-flex align-center"
              >
                <div>従事すべき業務の内容</div>
              </v-col>
              <v-col cols="12" md="9" class="table-header-right-2">
                <v-row>
                  <v-col
                    cols="12"
                    :class="{
                      'font-16px': mdAndUp,
                      'font-14px': smAndDown,
                    }"
                  >
                    {{ contract.job_detail }}
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
            <!-- row 5 -->
            <v-row
              :class="{
                'font-16px': mdAndUp,
                'font-14x': smAndDown,
              }"
            >
              <v-col
                cols="12"
                md="3"
                :class="{
                  'pl-6': mdAndUp,
                  'font-14px': smAndDown,
                }"
                class="table-header-left-2 d-flex align-center"
              >
                <div>
                  始業・終業の時刻、勤務時間、休憩時間、所定時間外労働、休日労働の有無に関する事項
                </div>
              </v-col>
              <v-col cols="12" md="9" class="table-header-right-2">
                <v-row>
                  <v-col
                    cols="12"
                    :class="{
                      'font-16px': mdAndUp,
                      'font-14px': smAndDown,
                    }"
                  >
                    1. 始業・終業の時刻
                    <div class="ml-4">
                      <div>始業および終業の時間は本人の決定に委ねる。</div>
                      <div>ただし、５：００～２２：００の時間内とする。</div>
                    </div>
                    2. 勤務時間
                    <div class="ml-4">
                      <div>勤務時間は本人の決定に委ねる。</div>
                      <div>
                        ただし、５：００～２２：００の時間内で以下を上限とする。
                      </div>
                      <ul>
                        <li>１日について８時間</li>
                        <li>
                          １週間について４０時間（毎週日曜日を起算日とする）
                        </li>
                        <li>
                          １ヶ月について１１９時間（賃金締切日の翌日を起算日とする）
                        </li>
                      </ul>
                      副業・兼業を行う場合には当社と副業・兼業先で合算した勤務時間は１日について８時間、１週
                      間について４０時間を上限とする。
                    </div>
                    3. 休憩時間
                    <div class="ml-4">
                      <div>
                        １日について勤務時間が６時間を超える場合 ４５分以上
                      </div>
                      <div>
                        １日について勤務時間が８時間を超える場合 ６０分以上
                      </div>
                    </div>
                    4. 法定時間外労働・法定休日労働 無
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
            <!-- row 6 -->
            <v-row
              :class="{
                'font-16px': mdAndUp,
                'font-14x': smAndDown,
              }"
            >
              <v-col
                cols="12"
                md="3"
                :class="{
                  'pl-6': mdAndUp,
                  'font-14px': smAndDown,
                }"
                class="table-header-left-2 d-flex align-center"
              >
                <div>労働時間の記録</div>
              </v-col>
              <v-col cols="12" md="9" class="table-header-right-2">
                <v-row>
                  <v-col
                    cols="12"
                    :class="{
                      'font-16px': mdAndUp,
                      'font-14px': smAndDown,
                    }"
                  >
                    <div>
                      <div>
                        労働時間の管理は自己申告制とし、以下の点に留意する
                      </div>
                      <ul>
                        <li>
                          勤務を行った日ごとに当社指定の勤怠管理表に勤務時間・業務内容を入力すること。
                        </li>
                        <li>
                          勤務時間は実際に勤務した適正な時間を申告すること。
                        </li>
                        <li>
                          始業終業時刻、勤務時間についての上限を守ること。ただし、特別な理由があり上限時間を超えて勤務する場合は管理者に報告の上、承認を得ること。承認を得ずに行った時間外労働、休日労働、深夜労働時間については業務上必要ないものとみなし、賃金は支給しない。
                        </li>
                        <li>
                          会社は申告された勤務時間と実際の労働時間との間に乖離がないか、定期的に確認することがある。
                        </li>
                        <li>
                          労働時間の自己申告制は業務の効率化やワークライフバランスの実現の観点から導入しているものである。
                        </li>
                      </ul>
                    </div>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
            <!-- row 7 -->
            <v-row
              :class="{
                'font-16px': mdAndUp,
                'font-14x': smAndDown,
              }"
            >
              <v-col
                cols="12"
                md="3"
                :class="{
                  'pl-6': mdAndUp,
                  'font-14px': smAndDown,
                }"
                class="table-header-left-2 d-flex align-center"
              >
                <div>休日</div>
              </v-col>
              <v-col cols="12" md="9" class="table-header-right-2">
                <v-row>
                  <v-col
                    cols="12"
                    :class="{
                      'font-16px': mdAndUp,
                      'font-14px': smAndDown,
                    }"
                  >
                    週休２日以上とし、法定休日は４週を通じて４日とする。
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
            <!-- row 8 -->
            <v-row
              :class="{
                'font-16px': mdAndUp,
                'font-14x': smAndDown,
              }"
            >
              <v-col
                cols="12"
                md="3"
                :class="{
                  'pl-6': mdAndUp,
                  'font-14px': smAndDown,
                }"
                class="table-header-left-2 d-flex align-center"
              >
                <div>賃金</div>
              </v-col>
              <v-col cols="12" md="9" class="table-header-right-2">
                <v-row>
                  <v-col cols="12">
                    <div
                      cols="12"
                      :class="{
                        'font-16px': mdAndUp,
                        'font-14px': smAndDown,
                      }"
                    >
                      <p style="margin: 0">
                        1. 時給制（{{ contract?.wage }}円）
                      </p>

                      <p style="margin: 0">
                        2. 時間外に対して支払われる割増賃金率
                      </p>
                      <ul
                        style="
                          list-style-type: none;
                          padding-left: 15px;
                          margin: 0;
                        "
                      >
                        <li style="margin: 0">
                          法定時間外/月６０時間以内（{{
                            contract?.overtime_pay_rate_under_60_hours
                          }}％）
                        </li>
                        <li style="margin: 0">
                          法定時間外/月６０時間超（{{
                            contract?.overtime_pay_rate_over_60_hours
                          }}％）
                        </li>
                        <li style="margin: 0">
                          法定休日（{{ contract?.holiday_pay_rate }}％）
                        </li>
                        <li style="margin: 0">
                          深夜（{{ contract?.night_shift_pay_rate }}％）
                        </li>
                      </ul>

                      <p style="margin: 0">
                        3. 賃金締切日 毎月
                        {{ contract?.wage_closing_date }}日
                      </p>
                      <p style="margin: 0">
                        4. 賃金支払日 毎月 翌月{{
                          contract?.wage_payment_date
                        }}日
                      </p>
                      <p style="margin: 0">
                        5. 支払方法
                        本人の同意を得て直接銀行口座に振込にて全額支払う。ただし、法令等で定められているものは、支払の際に控除する。
                      </p>
                      <p style="margin: 0">
                        6. 昇給
                        {{ contract?.flg_salary_increase ? '有' : '無' }}
                      </p>
                      <p style="margin: 0">
                        7. 賞与 {{ contract?.flg_bonus ? '有' : '無' }}
                      </p>
                      <p style="margin: 0">
                        8. 退職金
                        {{ contract?.flg_retirement_allowance ? '有' : '無' }}
                      </p>
                    </div>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
            <!-- row 9 -->
            <v-row
              :class="{
                'font-16px': mdAndUp,
                'font-14x': smAndDown,
              }"
            >
              <v-col
                cols="12"
                md="3"
                :class="{
                  'pl-6': mdAndUp,
                  'font-14px': smAndDown,
                }"
                class="table-header-left-2 d-flex align-center"
              >
                <div>退職に関する事項</div>
              </v-col>
              <v-col cols="12" md="9" class="table-header-right-2">
                <v-row>
                  <v-col
                    cols="12"
                    :class="{
                      'font-16px': mdAndUp,
                      'font-14px': smAndDown,
                    }"
                  >
                    <div>
                      1.
                      自己都合退職の場合の手続については、原則として３０日前までに申し出なければならない
                    </div>
                    <div>
                      2. 解雇の事由及び手続
                      <div class="ml-4">
                        <div>
                          ①&nbsp;&nbsp;
                          身体、精神の障害により、業務に耐えられないとき
                        </div>
                        <div>
                          ②&nbsp;&nbsp;
                          勤務成績が不良で、就業に適さないと認められたとき
                        </div>
                        <div>
                          ③&nbsp;&nbsp;
                          協調性がなく、注意および指導しても改善の見込みがないと認められるとき
                        </div>
                        <div>
                          ④&nbsp;&nbsp;
                          事業の縮小等、やむを得ない業務の都合により必要のあるとき
                        </div>
                        <div>
                          ⑤&nbsp;&nbsp;
                          事業の運営上、やむを得ない事情、または天災事変その他これに準ずるやむを得ない事情により事業の継続が困難になったとき
                        </div>
                      </div>
                    </div>
                    <div class="ml-4">
                      上記に該当する事由があった場合は、３０日前に予告するか解雇予告手当を支払って解雇する。
                    </div>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
            <!-- row 10 -->
            <v-row
              :class="{
                'font-16px': mdAndUp,
                'font-14x': smAndDown,
              }"
            >
              <v-col
                cols="12"
                md="3"
                :class="{
                  'pl-6': mdAndUp,
                  'font-14px': smAndDown,
                }"
                class="table-header-left-2 d-flex align-center"
              >
                社会保険の加入状況
              </v-col>
              <v-col cols="12" md="9" class="table-header-right-2">
                <v-row>
                  <v-col cols="12">
                    <div class="d-flex align-center w-full">
                      <v-checkbox
                        v-model="
                          contract.flg_insurance_industrial_accident_compensation
                        "
                        :value="1"
                        :ripple="false"
                        dense
                        :hide-details="true"
                        color="#8E8E8E"
                        class="pa-0 ma-0 login-modal cancel-mouse-event"
                      >
                        <template #label>
                          <div
                            class="fw-400 pa-0 text-black"
                            :class="{
                              'font-16px': mdAndUp,
                              'font-14px': smAndDown,
                            }"
                          >
                            労災保険
                          </div>
                        </template>
                      </v-checkbox>
                    </div>
                    <div class="d-flex align-center">
                      <v-checkbox
                        v-model="contract.flg_insurance_unemployment"
                        :value="1"
                        :ripple="false"
                        dense
                        :hide-details="true"
                        color="#8E8E8E"
                        class="pa-0 ma-0 login-modal cancel-mouse-event"
                      >
                        <template #label>
                          <div
                            class="fw-400 pa-0 text-black"
                            :class="{
                              'font-16px': mdAndUp,
                              'font-14px': smAndDown,
                            }"
                          >
                            雇用保険
                          </div>
                        </template>
                      </v-checkbox>
                    </div>
                    <div class="d-flex align-center">
                      <v-checkbox
                        v-model="contract.flg_insurance_public_health"
                        :value="1"
                        :ripple="false"
                        dense
                        :hide-details="true"
                        color="#8E8E8E"
                        class="pa-0 ma-0 login-modal cancel-mouse-event"
                      >
                        <template #label>
                          <div
                            class="fw-400 pa-0 text-black"
                            :class="{
                              'font-16px': mdAndUp,
                              'font-14px': smAndDown,
                            }"
                          >
                            健康保険
                          </div>
                        </template>
                      </v-checkbox>
                    </div>
                    <div class="d-flex align-center">
                      <v-checkbox
                        v-model="contract.flg_insurance_employee_pension"
                        :value="1"
                        :ripple="false"
                        dense
                        :hide-details="true"
                        color="#8E8E8E"
                        class="pa-0 ma-0 login-modal cancel-mouse-event"
                      >
                        <template #label>
                          <div
                            class="fw-400 pa-0 text-black"
                            :class="{
                              'font-16px': mdAndUp,
                              'font-14px': smAndDown,
                            }"
                          >
                            厚生年金保険
                          </div>
                        </template>
                      </v-checkbox>
                    </div>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
            <!-- row 12 -->
            <v-row
              :class="{
                'font-16px': mdAndUp,
                'font-14x': smAndDown,
              }"
            >
              <v-col
                cols="12"
                md="3"
                :class="{
                  'pl-6': mdAndUp,
                  'font-14px': smAndDown,
                }"
                class="table-header-left-2 d-flex align-center"
              >
                <div>雇用管理の改善等に関する事項に係る相談窓口</div>
              </v-col>
              <v-col cols="12" md="9" class="table-header-right-2">
                <v-row>
                  <v-col
                    cols="12"
                    :class="{
                      'font-16px': mdAndUp,
                      'font-14px': smAndDown,
                    }"
                  >
                    <div>
                      担当者職氏名：{{ contract.consultation_user_title_name }}
                    </div>
                    <div>
                      email:
                      {{ contract.consultation_user_mail_address }}
                    </div>
                    <div>電話: {{ contract.consultation_user_tel }}</div>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed, nextTick } from 'vue';
import { useDisplay } from 'vuetify';
import { useStore } from 'vuex';
import contractDetailPDF from '@/views/student/contractDetailPdf.vue';
import moment from 'moment';

export default {
  name: 'ContractDetail',
  components: {
    contractDetailPDF,
  },
  props: {
    id: {
      type: [String, Number],
      required: true,
    },
  },
  setup(props) {
    // Reactive state
    const mdAndUp = ref(false);
    const smAndDown = ref(false);
    const pdfDocumentContractAdmin = ref(null);
    const isDownloadPDF = ref(false);
    // Access Vuex store
    const store = useStore();

    // Fetch data when the component is mounted
    onMounted(() => {
      const { mdAndUp: mdUp, smAndDown: smDown } = useDisplay();
      mdAndUp.value = mdUp.value; // Assign the reactive value to the mdAndUp ref
      smAndDown.value = smDown.value;
      store.dispatch('CONTRACT_GET', props.id); // Dispatch Vuex action to fetch contract data
    });

    // Computed properties
    const contract = computed(() => store.getters.getSingleContract);
    const getSingleStudent = computed(() => store.getters.getSingleStudent);

    // Methods
    const formattedDate = (item) => moment(item).format('YYYY年M月D日');

    const approve = () => {
      // Trigger alert
      store.dispatch('show-alert', 'approveContract');
      // Hide modal (via Vuex mutation)
      store.commit('hideModal');
    };

    const generatePdf = async () => {
      store.dispatch('API_PROCESSING', true); // Start loading
      isDownloadPDF.value = true;

      // Wait for the component to render and get the ref
      await nextTick();

      // Verify if pdfDocumentContractAdmin is accessible
      if (
        pdfDocumentContractAdmin.value &&
        typeof pdfDocumentContractAdmin.value.generatePdf === 'function'
      ) {
        pdfDocumentContractAdmin.value.generatePdf();
      } else {
        console.error(
          'generatePdf is not a function or pdfDocumentContractAdmin is not set'
        );
      }
    };

    // Expose all necessary values and methods to the template
    return {
      mdAndUp,
      smAndDown,
      contract,
      formattedDate,
      approve,
      generatePdf,
      isDownloadPDF,
      pdfDocumentContractAdmin,
      getSingleStudent,
    };
  },
};
</script>

<style scoped>
#pdf-content {
  width: 210mm; /* A4 width */
  padding: 40px; /* Add some padding to avoid content cutting off */
  padding-top: 60px;
}

.pdf-container {
  z-index: -9999999;
  position: absolute;
  left: 0;
}

.pdf-content {
  background: white;
  font-family: 'Arial', sans-serif;
  font-size: 12px;
  line-height: 1.5;
}

.name-uni {
  border-radius: 0px !important;
  border-bottom: 1px solid black;
}
.page {
  position: relative;
  page-break-after: always;
}

.page:last-child {
  page-break-after: auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-number {
  position: absolute;
  top: 10mm;
  right: 10mm;
  font-size: 12px;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

td {
  padding: 5px 10px;
  vertical-align: top;
}

ul,
ol {
  padding-left: 20px;
}

h2,
h3,
h4 {
  margin-top: 0;
}

:deep(.v-input--selection-controls__input + .v-label) {
  color: #000000de !important;
}

.border-b-header-4 {
  border-bottom: 1px solid black;
}

.table-header {
  border-collapse: collapse;
}
.table-header-left {
  border: 1px solid #000;
}

.table-header-right {
  border: 1px solid #000;
  border-left: 0;
}

.table-header-left-2 {
  border: 1px solid #000;
  border-top: 0px;
}

.table-header-right-2 {
  border-right: 1px solid #000;
  border-bottom: 1px solid #000;
  border-top: 0px;
}

@media (max-width: 960px) {
}
</style>

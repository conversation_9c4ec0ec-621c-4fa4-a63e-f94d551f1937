<template>
  <v-navigation-drawer
    app
    clipped
    v-model="drawerState"
    :expand-on-hover="!drawerState"
    :width="sidebarWidth"
    :permanent="mdAndUp"
    :temporary="smAndDown"
    :mini-variant-width="sidebarMinWidth"
    :class="{ 'drawer-mini': !drawerState }"
    v-if="drawerState"
  >
    <v-treeview
      :items="items"
      activatable
      :multiple-active="false"
      selected-color="indigo"
      open-on-click
      expand-icon="mdi-chevron-right"
      collapse-icon="mdi-chevron-down"
      item-text="title"
      item-key="title"
      class="treeview-custom-sidebar"
      transition
    >
      <template #prepend="{ item }">
        <v-icon
          v-if="!item.hideIcon"
          class="icon-arrow-right fill-primary"
          :class="{
            'active-svg': isActive(item),
            'child-icon': item.iconStroke,
          }"
          size="x-large"
          style="padding: 0"
        >
          {{ item.iconText || item.icon }}
        </v-icon>
      </template>

      <template #title="{ item }">
        <div
          @click="checkCount(item)"
          class="d-flex align-center"
          style="width: 100%"
          :class="item.class ? item.class : ''"
        >
          <template v-if="item.link == '/logout'">
            <span class="page-link" @click="logOut()">
              {{ item.title }}
            </span>
          </template>
          <template v-else>
            <router-link
              class="page-link"
              v-if="item.link"
              :to="item.link"
              :class="{
                'text-primary':
                  item.link.name === $route.name ||
                  String($route.name).includes(item.link.highlightOn),
              }"
            >
              {{ item.title }}
            </router-link>
            <span
              class="page-link"
              v-else
              :class="checkChildrens(item.children)"
            >
              {{ item.title }}
            </span>
          </template>

          <template v-if="item.badgeCount">
            <div style="position: absolute; right: 40px; color: white">
              <v-badge color="#E14D56" inline :content="item.badgeCount">
              </v-badge>
            </div>
          </template>
        </div>
      </template>
    </v-treeview>
  </v-navigation-drawer>
</template>

<script>
import { ref, computed, watch } from 'vue';
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import { useDisplay } from 'vuetify';
import { useI18n } from 'vue-i18n';
import Encoding from 'encoding-japanese';
export default {
  name: 'Sidebar',
  setup() {
    const openItems = ref([]);
    // Toggle the open state of a specific item
    const toggleOpen = (item) => {
      const index = openItems.value.indexOf(item.title);
      if (index > -1) {
        // If the item is already open, remove it (close it)
        openItems.value.splice(index, 1);
      } else {
        // If the item is not open, add it (open it)
        openItems.value.push(item.title);
      }
    };
    const isItemOpen = (item) => openItems.value.includes(item.title);
    const { t } = useI18n();
    const store = useStore();
    const route = useRoute();
    const { mdAndUp, smAndDown } = useDisplay();
    const user = computed(() => store.getters.user);
    const getCountsData = computed(() => store.getters.getCountsData);
    const getApprovedCompanies = computed(
      () => store.getters.getApprovedCompanies
    );
    const getTotalStudents = computed(() => store.getters.getTotalStudents);
    const getTotalUnreadApplications = computed(
      () => store.getters.getTotalUnreadApplications
    );
    const getNewStudentCount = computed(() => store.getters.getNewStudentCount);

    // Reactive data for the drawer state
    const drawerState = ref(store.state.drawer ?? true);
    const sidebarWidth = 240;
    const sidebarMinWidth = 76;

    watch(
      () => store.state.drawer,
      (newDrawerState) => {
        drawerState.value = newDrawerState;
      }
    );

    const items = computed(() => [
      {
        title: t('sidebar.link_titles.top'),
        icon: '$HomeIcon',
        link: { name: 'Dashboard' },
      },
      {
        title: t('sidebar.link_titles.job_info.title'),
        icon: '$Search',
        model: false,
        class: 'hide-count-on-open',
        badgeCount: 0,
        iconStroke: true,
        children: [
          {
            title: t('sidebar.link_titles.job_info.list_all', {
              count: getCountsData.value?.total_internships ?? 0,
            }),
            icon: 'mdi-circle-small',
            iconText: '$ArrowRightDrop',
            link: {
              name: 'InternshipPostList',
              highlightOn: 'InternshipPostEdit',
            },
            iconStroke: true,
            isHasCount: true,
          },
          {
            title: t('sidebar.link_titles.job_info.create_new'),
            icon: 'mdi-circle-small',
            iconText: '$ArrowRightDrop',
            badgeCount: 0,
            iconStroke: true,
            link: { name: 'InternshipPostCreate' },
          },
          {
            title: t('sidebar.link_titles.job_info.draft', {
              count: getCountsData.value?.total_draft_internships ?? 0,
            }),
            icon: 'mdi-circle-small',
            iconText: '$ArrowRightDrop',
            link: { name: 'InternshipPostDraftList' },
            iconStroke: true,
            isHasCount: true,
          },
          {
            title: '職種管理',
            icon: 'mdi-circle-small',
            iconText: '$ArrowRightDrop',
            link: { name: 'WorkCategories' },
            iconStroke: true,
          },
          {
            title: t('sidebar.link_titles.job_info.feature_management'),
            icon: 'mdi-circle-small',
            iconText: '$ArrowRightDrop',
            link: { name: 'InternshipFeatures' },
            iconStroke: true,
          },
        ],
      },

      {
        title: t('sidebar.link_titles.application.title'),
        icon: '$PaperPlaneIcon',
        model: false,
        badgeCount: getTotalUnreadApplications.value,
        class: 'hide-count-on-open',
        link: { name: 'Applications' },
      },

      {
        title: t('sidebar.link_titles.contract.title'),
        icon: '$contractIcon',
        model: false,
        children: [
          {
            title: t('sidebar.link_titles.contract.list_all', {
              count: getCountsData.value?.total_contracts_status_2_3_4 ?? 0,
            }),
            icon: 'mdi-circle-small',
            iconText: '$ArrowRightDrop',
            link: { name: 'Contract', highlightOn: 'ContractDetail' },
            iconStroke: true,
            isHasCount: true,
          },
          {
            title: t('sidebar.link_titles.contract.uncontracted_list', {
              count: getCountsData.value?.total_contracts_status_1 ?? 0,
            }),
            icon: 'mdi-circle-small',
            iconText: '$ArrowRightDrop',
            link: { name: 'Uncontracted' },
            iconStroke: true,
            isHasCount: true,
          },
          {
            title: t('sidebar.link_titles.contract.contract_history', {
              count:
                getCountsData.value
                  ?.total_contracts_status_2_3_4and_type_format_1 ?? 0,
            }),
            icon: 'mdi-circle-small',
            iconText: '$ArrowRightDrop',
            link: { name: 'ContractHistory' },
            iconStroke: true,
            isHasCount: true,
          },
          {
            title: t('sidebar.link_titles.contract.template_management'),
            icon: 'mdi-circle-small',
            iconText: '$ArrowRightDrop',
            link: { name: 'TemplateManagement' },
            iconStroke: true,
          },
        ],
      },

      {
        title: t('sidebar.link_titles.internship_student.title', {
          count: getCountsData.value?.counts_internship_student?.Total,
        }),
        icon: '$HandShakeIcon',
        model: false,
        class: 'hide-count-on-open',
        link: { name: 'InternshipStudent' },
        isHasCount: true,
      },

      {
        title: t('sidebar.link_titles.feedback.title'),
        icon: '$messages',
        model: false,
        children: [
          {
            title: t('sidebar.link_titles.feedback.list_all', {
              count: getCountsData.value?.feedback_status_3_and_4,
            }),
            icon: 'mdi-circle-small',
            iconText: '$ArrowRightDrop',
            link: {
              name: 'Feedbacks',
              highlightOn: 'AddressedFeedbackCreate',
            },
            iconStroke: true,
            isHasCount: true,
          },
          {
            title: t('sidebar.link_titles.feedback.list_unaddress', {
              count: getCountsData.value?.feedback_status_1_and_2,
            }),
            icon: 'mdi-circle-small',
            iconText: '$ArrowRightDrop',
            link: {
              name: 'UnaddressFeedbacks',
              highlightOn: 'UnaddressFeedbackEdit',
            },
            iconStroke: true,
            isHasCount: true,
          },
        ],
      },
      {
        title: t('sidebar.link_titles.student.title', {
          count: getTotalStudents.value,
        }),
        badgeCount: getNewStudentCount.value,
        icon: '$Account',
        model: false,
        children: [
          {
            title: t('sidebar.link_titles.student.list_all'),
            icon: 'mdi-circle-small',
            iconText: '$ArrowRightDrop',
            link: { name: 'Students', highlightOn: 'StudentProfile' },
            iconStroke: true,
            isHasCount: true,
          },
          {
            title: t('sidebar.link_titles.student.create_new_student'),
            icon: 'mdi-circle-small',
            iconText: '$ArrowRightDrop',
            link: { name: 'StudentCreate' },
            iconStroke: true,
            isHasCount: true,
          },
        ],
      },
      {
        title: t('sidebar.link_titles.corporate.title', {
          count: getCountsData.value?.total_companies ?? 0,
        }),
        icon: '$Companies',
        model: false,
        children: [
          {
            title: t('sidebar.link_titles.corporate.list_all'),
            icon: 'mdi-circle-small',
            iconText: '$ArrowRightDrop',
            link: { name: 'Corporate', highlightOn: 'CorporateEdit' },
            iconStroke: true,
            isHasCount: true,
          },
          {
            title: t('sidebar.link_titles.corporate.create_new_corporation'),
            icon: 'mdi-circle-small',
            iconText: '$ArrowRightDrop',
            link: { name: 'CorporateCreate' },
            iconStroke: true,
            isHasCount: true,
          },
          {
            title: '企業ユーザー CSV',
            icon: 'mdi-circle-small',
            iconText: '$ArrowRightDrop',
            action: 'exportCsv',
            iconStroke: true,
          },
        ],
      },
      // {
      //   title: t('sidebar.link_titles.company_user.title', {
      //     count: getApprovedCompanies.value,
      //   }),
      //   icon: '$Companies',
      //   model: false,
      //   link: {
      //     name: 'CompanyUserListing',
      //     highlightOn: 'CompanyUserEdit',
      //   },
      //   isHasCount: true,
      // },
      {
        title: t('sidebar.link_titles.notification_management.title'),
        icon: '$NotificationIcon',
        link: {
          name: 'NotificationManagement',
          highlightOn: 'NotificationManagement',
        },
      },
      {
        title: t('sidebar.link_titles.column.title'),
        icon: '$Bell',
        model: false,
        children: [
          {
            title: t('sidebar.link_titles.column.list_all'),
            icon: 'mdi-circle-small',
            iconText: '$ArrowRightDrop',
            link: { name: 'Media', highlightOn: 'EditMediaPost' },
            iconStroke: true,
          },
          {
            title: t('sidebar.link_titles.column.tag_management'),
            icon: 'mdi-circle-small',
            iconText: '$ArrowRightDrop',
            link: { name: 'MediaTags' },
            iconStroke: true,
          },
        ],
      },
      {
        title: t('sidebar.link_titles.analysis_from_others.title', {
          count: getCountsData.value?.analysis_total_active_data ?? 0,
        }),
        icon: '',
        link: {
          name: 'AnalysisFromOthers',
          highlightOn: 'AnalysisFromOthers',
        },
      },
      {
        title: t('sidebar.link_titles.settings.title'),
        icon: '$Setting',
        model: false,
        children: [
          {
            title: '教育機関',
            icon: 'mdi-circle-small',
            iconText: '$ArrowRightDrop',
            link: { name: 'educational_facilities' },
            iconStroke: true,
          },
          {
            title: '興味のある仕事',
            icon: 'mdi-circle-small',
            iconText: '$ArrowRightDrop',
            link: { name: 'InterestedJob' },
            iconStroke: true,
          },
          {
            title: '管理者',
            icon: 'mdi-circle-small',
            iconText: '$ArrowRightDrop',
            link: { name: 'AdminListing', highlightOn: 'AdminsEdit' },
            iconStroke: true,
          },
          {
            title: 'ログアウト',
            icon: 'mdi-circle-small',
            iconText: '$ArrowRightDrop',
            link: '/logout',
            iconStroke: true,
          },
        ],
      },
      {
        title: 'Test',
        link: {
          name: 'TestPage',
          highlightOn: 'TestPage',
        },
      },
    ]);

    // Method to toggle drawer
    const toggleDrawer = () => store.dispatch('TOGGLE_DRAWER');

    // Log out method
    const logOut = () => store.dispatch('AUTH_LOGOUT');

    // Check if any children are active for highlighting purposes
    const checkChildrens = (children) => {
      return children?.some((child) => isActive(child))
        ? 'text-primary active-svg'
        : '';
    };
    async function companyExportUser() {
      await store.dispatch('COMPANY_USER_EXPORT_CSV');
      const csvData = store.getters.getCompanyUserCsvData.csv;
      const sjisArray = Encoding.convert(Encoding.stringToCode(csvData), {
        to: 'SJIS',
        from: 'UNICODE',
      });
      const uint8Array = new Uint8Array(sjisArray);
      const fileUrl = window.URL.createObjectURL(
        new Blob([uint8Array], { type: 'text/csv;charset=Shift_JIS' })
      );

      const fileLink = document.createElement('a');
      fileLink.href = fileUrl;
      fileLink.setAttribute(
        'download',
        `企業ユーザー管理_${new Date().toISOString().slice(0, 10)}.csv`
      );
      document.body.appendChild(fileLink);
      fileLink.click();
      document.body.removeChild(fileLink);
    }
    const checkCount = async (item) => {
      if (item.action === 'exportCsv') {
        await companyExportUser();
        return;
      }
      if (item.isHasCount) {
        await store.dispatch('GET_COUNTS_DATA');
      }
    };

    // Determines if a given item is active based on the route
    const isActive = (item) =>
      (item.link && item.link.name === route.name) ||
      (item.link && route.name.includes(item.link.highlightOn)) ||
      (item.children && item.children.some(isActive));

    return {
      drawerState,
      sidebarWidth,
      sidebarMinWidth,
      items,
      logOut,
      toggleDrawer,
      checkChildrens,
      isActive,
      route,
      mdAndUp,
      smAndDown,
      user,
      getApprovedCompanies,
      getTotalStudents,
      getTotalUnreadApplications,
      getNewStudentCount,
      getCountsData,
      openItems,
      toggleOpen,
      isItemOpen,
      checkCount,
    };
  },
};
</script>

<style src="./Sidebar.scss" lang="scss" />

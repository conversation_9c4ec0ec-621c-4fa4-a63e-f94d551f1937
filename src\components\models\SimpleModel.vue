<template>
  <v-dialog v-model="selfDialog" max-width="775" content-class="no-shadow">
    <template v-slot:default>
      <v-card class="pa-15">
        <v-col
          v-if="showCloseIcon"
          style="position: absolute; top: 0; right: 0"
          cols="1"
          class="p-8 py-6"
        >
          <v-btn variant="text" icon @click.prevent="closeDialog">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-col>
        <v-card-text class="pb-5">
          <template v-if="Array.isArray(text)">
            <div
              v-for="(tt, index) in text"
              :key="index"
              class="my-1 text-center d-flex justify-center align-center"
              :class="[index == 0 ? 'font-20px mb-7' : 'font-14px']"
              v-html="tt"
            ></div>
          </template>
          <template v-else>
            <div
              style="font-size: 20px"
              class="my-1 d-flex justify-center align-center font-20px"
              v-html="text"
            ></div>
          </template>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn
            v-if="!buttonOption.hideCancel"
            variant="outlined"
            color="primary"
            min-width="236"
            class="mr-6"
            @click.prevent="cancelAction"
          >
            {{ cancelButtonText }}
          </v-btn>
          <v-btn
            v-if="!buttonOption.hideSubmit"
            class="bg-primary white--text"
            min-width="236"
            variant="elevated"
            @click.prevent="submitAction"
            :loading="loading"
          >
            {{ submitButtonText }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </template>
  </v-dialog>
</template>

<script>
import { computed, toRefs, defineComponent } from 'vue';

export default defineComponent({
  name: 'SimpleModel',
  props: {
    dialog: {
      type: Boolean,
      default: false,
      required: true,
    },
    text: {
      type: [String, Array],
      default: 'この企業情報を削除しますか？',
      required: false,
    },
    submitButtonText: {
      type: String,
      default: '保存する',
      required: false,
    },
    cancelButtonText: {
      type: String,
      default: '戻る',
      required: false,
    },
    showCloseIcon: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
      required: false,
    },
    buttonOption: {
      type: Object,
      default: () => ({
        hideSubmit: false,
        hideCancel: false,
      }),
      required: false,
    },
  },
  setup(props, { emit }) {
    /**
     * Destructure and make props reactive
     * Extract commonly used props and make them reactive using toRefs
     */
    const {
      dialog,
      text,
      submitButtonText,
      cancelButtonText,
      loading,
      buttonOption,
    } = toRefs(props);

    /**
     * Dialog Control
     * Two-way binding for dialog visibility with parent component
     * Manages the showing/hiding of the modal dialog
     */
    const selfDialog = computed({
      get: () => dialog.value, // Get dialog state from parent
      set: (value) => {
        emit('closeModel', value); // Notify parent of dialog state change
      },
    });

    /**
     * Close Dialog Handler
     * Handles dialog closure via close icon
     */
    const closeDialog = () => {
      selfDialog.value = false; // Close the dialog
    };

    /**
     * Cancel Action Handler
     * Handles dialog cancellation and notifies parent
     */
    const cancelAction = () => {
      selfDialog.value = false; // Close the dialog
      emit('cancelAction'); // Notify parent of cancellation
    };

    /**
     * Submit Action Handler
     * Handles form submission and notifies parent
     */
    const submitAction = () => {
      emit('submitSuccess', true); // Notify parent of successful submission
      selfDialog.value = false; // Close the dialog
    };

    return {
      selfDialog,
      text,
      submitButtonText,
      cancelButtonText,
      loading,
      buttonOption,
      closeDialog,
      cancelAction,
      submitAction,
    };
  },
});
</script>

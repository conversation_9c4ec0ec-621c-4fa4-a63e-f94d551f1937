<template>
  <PageTitle :items="titleItems" />
  <div class="">
    <Form @submit="$route.params.id ? updateAnalysis() : ''">
      <v-row>
        <v-col cols="8">
          <!-- Main card container -->
          <v-card class="w-100 pb-12">
            <div
              class="mx-auto"
              style="max-width: 642px"
              :class="{ 'px-4': mdAndDown }"
            >
              <div>
                <div class="mt-20">
                  <!-- Loop through fields and create form inputs dynamically -->
                  <v-sheet
                    v-for="(field, index) in fields"
                    :key="field.name || index"
                    width="100%"
                    :class="field.class"
                    color="transparent"
                  >
                    <Field
                      v-slot="{
                        field: { value, ...fieldWithoutValue },
                        errors,
                      }"
                      :name="field.name"
                      :rules="field.rules"
                      :value="
                        $route.params.id
                          ? getSingleAnalysis[field.name]
                          : field.value
                      "
                    >
                      <template
                        v-if="
                          field.type != 'dropdownMonth' &&
                          field.type != 'dropdownWorkMonth'
                        "
                      >
                        <label class="d-block font-14px mb-1">
                          <span>{{ field.label }}</span>
                          <span
                            v-if="field.required"
                            class="error--text ml-2 font-12px"
                            >必須</span
                          >
                        </label>
                        <v-select
                          v-bind="fieldWithoutValue"
                          v-if="field.type === 'select'"
                          :items="field.items"
                          v-model="field.value"
                          density="compact"
                          variant="outlined"
                          :item-title="field.item_text"
                          :item-value="field.item_value"
                          :placeholder="field.placeholder"
                          :error-messages="errors"
                          :error="errors.length !== 0"
                          :hide-details="errors.length <= 0"
                          menu-icon="$greyExpansionDropdown"
                          :class="{ 'bg-input-disabled': field.disabled }"
                        ></v-select>
                        <v-autocomplete
                          v-bind="fieldWithoutValue"
                          v-if="field.type === 'autocomplete'"
                          :items="field.items"
                          v-model="field.value"
                          density="compact"
                          variant="outlined"
                          v-model:search="field.searched_text"
                          @keyup="field.search_api(field)"
                          :loading="field.is_loading"
                          :hide-no-data="field.is_hide_no_data"
                          :item-title="field.item_text"
                          :item-value="field.item_value"
                          hide-selected
                          :placeholder="field.placeholder"
                          :error-messages="errors"
                          :error="errors.length !== 0"
                          :hide-details="errors.length <= 0"
                          color="#13ABA3"
                          append-icon=""
                          :class="{ 'bg-input-disabled': field.disabled }"
                          autocomplete="new-password"
                        ></v-autocomplete>
                        <v-text-field
                          v-bind="fieldWithoutValue"
                          v-if="field.type === 'text'"
                          v-model="field.value"
                          :placeholder="field.placeholder"
                          :error-messages="errors"
                          :error="errors.length !== 0"
                          :hide-details="errors.length <= 0"
                          variant="outlined"
                          :width="field.width"
                          density="compact"
                          :class="{ 'bg-input-disabled': field.disabled }"
                        ></v-text-field>
                        <v-textarea
                          v-bind="fieldWithoutValue"
                          v-if="field.type === 'text-area'"
                          height="210px"
                          style="max-height: 210px"
                          variant="outlined"
                          density="compact"
                          :placeholder="field.placeholder"
                          v-model="field.value"
                          :error-messages="errors"
                          :error="errors.length !== 0"
                          :hide-details="errors.length <= 0"
                          :class="{ 'bg-input-disabled': field.disabled }"
                        >
                        </v-textarea>
                        <DatePicker
                          v-bind="fieldWithoutValue"
                          v-if="field.type === 'datepicker'"
                          :field="field.date"
                          :errors="errors"
                          density="compact"
                          v-model="field.value"
                          :error-messages="errors"
                          :error="errors.length !== 0"
                          :hide-details="errors.length <= 0"
                          @input="error = null"
                          :min="minStartDate"
                          :separator="'/'"
                          :pickerType="field.pickerType"
                        >
                        </DatePicker>
                      </template>
                      <template v-else>
                        <div
                          class="d-flex"
                          style="max-width: 642px"
                          v-if="field.type != 'dropdownWorkMonth'"
                        >
                          <div
                            class="full-width width-full w-100 mr-2"
                            v-if="!$route.params.id"
                          >
                            <label class="d-block font-14px mb-1">
                              <span>{{ field.label }}</span>
                              <span
                                v-if="field.required"
                                class="error--text ml-2 font-12px"
                                >必須</span
                              >
                            </label>
                            <v-select
                              v-bind="fieldWithoutValue"
                              density="compact"
                              variant="outlined"
                              single-line
                              class="mt-1"
                              v-model="field.value"
                              :placeholder="field.placeholder"
                              :items="getAnalysiMonths"
                              :item-title="field.item_text"
                              :item-value="field.item_value"
                              :error-messages="errors"
                              :error="errors.length !== 0"
                              :hide-details="errors.length <= 0"
                              autocomplete="chrome-off"
                              menu-icon="$greyExpansionDropdown"
                              no-data-text="データがありません"
                              :disabled="
                                $route.params.id
                                  ? true
                                  : false || field.disabled
                              "
                              :class="{
                                'bg-input-disabled':
                                  $route.params.id || field.disabled,
                              }"
                            >
                            </v-select>
                          </div>
                          <!-- if edit -->
                          <div
                            class="full-width width-full w-100 mr-2"
                            v-if="$route.params.id"
                          >
                            <label class="d-block font-14px mb-1">
                              <span>{{ field.label }}</span>
                              <span
                                v-if="field.required"
                                class="error--text ml-2 font-12px"
                                >必須</span
                              >
                            </label>
                            <v-text-field
                              v-bind="fieldWithoutValue"
                              v-model="field.value"
                              :placeholder="field.placeholder"
                              :error-messages="errors"
                              :error="errors.length !== 0"
                              :hide-details="errors.length <= 0"
                              density="compact"
                              variant="outlined"
                              class="bg-input-disabled"
                              disabled
                            ></v-text-field>
                          </div>
                          <div class="full-width width-full w-100">
                            <label class="d-block font-14px mb-1">
                              <span>{{ fields[3].label }}</span>
                              <span
                                v-if="fields[3].required"
                                class="error--text ml-2 font-12px"
                                >必須</span
                              >
                            </label>
                            <v-text-field
                              v-bind="fieldWithoutValue"
                              v-model="fields[3].value"
                              :placeholder="fields[3].placeholder"
                              :error-messages="errors"
                              :error="errors.length !== 0"
                              :hide-details="errors.length <= 0"
                              density="compact"
                              variant="outlined"
                              class="bg-input-disabled"
                            ></v-text-field>
                          </div>
                        </div>
                      </template>
                    </Field>
                  </v-sheet>
                </div>
              </div>
            </div>
            <!-- Centered section title -->
            <div class="font-18px text-center">
              Kotonaru Power 8について
              <!-- Centered power cards container -->
              <div
                class="mx-auto mt-6"
                :class="{ 'px-4': mdAndDown }"
                style="max-width: 642px"
              >
                <v-row justify="center">
                  <v-col
                    v-for="(power, index) in kotonaru_power_8"
                    :key="index"
                    cols="12"
                    sm="4"
                    md="3"
                  >
                    <div class="kotonaru-card">
                      <div class="kotonaru-card-title">{{ power.title }}</div>
                      <div class="kotonaru-card-text">
                        {{ power.description }}
                      </div>
                    </div>
                  </v-col>
                </v-row>
              </div>
            </div>
          </v-card>
        </v-col>
        <v-col cols="4" class="pa-4">
          <v-card class="text-center" width="100%">
            <div class="button-width mx-auto px-4">
              <div v-if="$route.params.id" class="btn-container">
                <!--Apply-->
                <div class="my-10">
                  <!-- <v-btn
                  height="35px"
                  width="250px"
                  class="mb-2 text-capitalize white--text font-14px fw-500 btn-primary"
                  variant="outlined"
                  type="submit"
                  color="#13ABA3"
                >
                更新する
                </v-btn> -->
                  <!-- <br /> -->
                  <div
                    @click="dialog.delete = true"
                    class="error--text my-4 cursor-pointer"
                  >
                    削除
                  </div>
                </div>
              </div>
            </div>
          </v-card>
        </v-col>
      </v-row>
    </Form>

    <SuccessModel
      :text="`Analysis has been removed.`"
      :buttonText="`Back to Analysis List`"
      :routeName="`AnalysisFromOthers`"
      :dialog="dialog.deleteSuccess"
      @closeModel="
        $router.push({
          name: 'AnalysisFromOthers',
        })
      "
    ></SuccessModel>
    <SuccessModel
      :text="`下書きとして保存しました。`"
      :buttonText="`下書き一覧へ戻る`"
      :routeName="`Analysis`"
      :dialog="dialog.draftSuccess"
      @closeModel="dialog.draftSuccess = false"
    ></SuccessModel>
    <SimpleModel
      text="Do you want to delete this Analysis"
      :dialog="dialog.delete"
      @submitSuccess="deleteAnalysis()"
      @closeModel="dialog.delete = false"
      submitButtonText="削除する"
    ></SimpleModel>
  </div>
</template>
<script setup>
import { computed, ref, watch, onMounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter, useRoute } from 'vue-router';
import DatePicker from '@/components/ui/DatePicker.vue';
import SimpleModel from '@/components/models/SimpleModel.vue';
import SuccessModel from '@/components/models/SuccessModel.vue';
import { debounce } from 'debounce';
import { Field, Form } from 'vee-validate';
import moment from 'moment';
import { useDisplay } from 'vuetify';

const { mdAndUp, smAndDown, mdAndDown } = useDisplay();
const router = useRouter();
const route = useRoute();
const store = useStore();

const analysisID = ref(null);
const pageType = ref('edit');

const getSingleAnalysis = computed(() => store.getters.getSingleAnalysis);
const getMasterData = computed(() => store.getters.getMasterData);

const titleItems = computed(() => ({
  title: '他己分析管理', // analysis-from-others
  subTitle: '詳細', // detail
}));

const dialog = ref({
  delete: false,
  deleteSuccess: false,
  draftSuccess: false,
});

const kotonaru_power_8 = ref([
  {
    title: 'リーダーシップ',
    description:
      '主体性を発揮し、目標の達成に向かってチームを牽引することができます。',
  },
  {
    title: '大胆さ',
    description: '失敗や否定的な意見を恐れず、発言や行動をすることができます。',
  },
  {
    title: '外向的',
    description:
      '興味関心が広く、行動的で社交的です。外の世界に対し、自らアプローチできます。',
  },
  {
    title: '創造性',
    description:
      '自分なりの考えや新しい視点でアイデアを出したり、切り口を変えた提案ができます。',
  },
  {
    title: '協調性',
    description:
      'チームメンバーの意見に注意深く耳を傾け、共感することで、誰もが同じ目標に向かって取り組む環境を作ります。',
  },
  {
    title: '綿密さ',
    description:
      '詳細な点も把握し、決定や選択を行う際には、潜在的なリスクに注意を払い判断できます。',
  },
  {
    title: '内省的',
    description:
      '自分の発言や行動、その結果起きたことを振り返り、そこから学びを得ることができます。',
  },
  {
    title: '論理性',
    description:
      '物事の構造を理解し、考えの妥当性を検証したり、筋道を立てた発言ができます。',
  },
]);

const type_relationships = ref([
  {
    id: 1,
    name: '親族 (family)',
  },
  {
    id: 2,
    name: '親友 (friend)',
  },
  {
    id: 3,
    name: '先輩 (senior)',
  },
  {
    id: 4,
    name: '後輩 (Junior)',
  },
  {
    id: 5,
    name: '知人 (Acquaintance)',
  },
  {
    id: 6,
    name: 'その他 (others)',
  },
]);

const fields = ref([
  {
    label: '企業名',
    name: 'Student_id',
    type: 'text',
    value: '',
    placeholder: '入力してください',
    required: true,
    rules: 'required:評価された力　本文',
    class: 'mb-5',
    disabled: true,
  },

  {
    label: '学生との関係',
    name: 'type_relationship',
    type: 'select',
    value: null,
    items: type_relationships.value,
    item_text: 'name',
    item_value: 'id',
    placeholder: '選択してください',
    width: '219px',
    required: true,
    rules: 'required:評価された力',
    class: 'mb-2',
    disabled: true,
  },
  {
    label: '評価された力',
    name: 'super_power_review',
    type: 'select',
    value: null,
    items: [],
    item_text: 'name',
    item_value: 'id',
    placeholder: '選択してください',
    width: '219px',
    required: true,
    rules: 'required:評価された力',
    class: 'mb-2',
    disabled: true,
  },
  {
    label: '評価された力　本文',
    name: 'super_power_comment',
    type: 'text-area',
    value: null,
    placeholder: '入力してください',
    required: true,
    rules: 'required:評価された力　本文',
    class: 'mb-5',
    disabled: true,
  },
  {
    label: '期待したい力',
    name: 'growth_idea_review',
    type: 'select',
    value: null,
    items: [],
    item_text: 'name',
    item_value: 'id',
    placeholder: '選択してください',
    width: '219px',
    required: true,
    rules: 'required:期待したい力',
    class: 'mb-2',
    disabled: true,
  },
  {
    label: '期待したい力　本文',
    name: 'growth_idea_comment',
    type: 'text-area',
    value: null,
    placeholder: '入力してください',
    required: true,
    rules: 'required:期待したい力　本文',
    class: 'mb-5',
    disabled: true,
  },
  {
    label: '',
    name: 'flg_feedback_target',
    class: 'mb-5',
    disabled: true,
  },
  {
    type: 'text',
    label: '登録日',
    name: 'created_at',
    class: 'mb-9',
    width: '250px',
    disabled: true,
  },
]);

const otherField = {
  label: '学生との関係その他テキスト',
  name: 'text_relationship_others',
  type: 'text-area',
  value: null,
  placeholder: '入力してください',
  width: '219px',
  required: true,
  rules: 'required:評価された力　本文',
  class: 'mb-2',
};

// Prevent the watch from triggering when the field we add/remove is being processed.
let isUpdating = false;

watch(
  () => fields.value[1].value,
  (newValue, oldValue) => {
    if (isUpdating) return; // Prevent recursive triggering

    isUpdating = true; // Set flag to indicate update in progress.

    if (newValue === 6) {
      if (
        !fields.value.some((field) => field.name === 'text_relationship_others')
      ) {
        fields.value.splice(2, 0, { ...otherField }); // Insert at index 2, after the relationship field.
      }
    } else {
      fields.value = fields.value.filter(
        (field) => field.name !== 'text_relationship_others'
      );
    }

    isUpdating = false; // Reset the flag.
  }
);

/**
 * Component initialization
 * Sets up initial data and fetches necessary information based on route parameters
 */
onMounted(() => {
  checkPageType();
});

/**
 * Initializes form fields with master data options
 * Sets review options for power and growth idea fields
 */
const setfieldsItemsData = () => {
  fields.value = fields.value.map((field) => {
    if (['super_power_review', 'growth_idea_review'].includes(field.name)) {
      field.items = getMasterData.value.reviews_option;
    }
    return field;
  });
};

/**
 * Determines page type (create/edit) and initializes accordingly
 * Handles both direct analysis editing and company/student specific creation
 */
const checkPageType = () => {
  setfieldsItemsData();
  // Edit mode initialization
  if (route.params?.id) {
    analysisID.value = route.params.id;
    pageType.value = 'edit';
    getPageData();
  }
};

/**
 * Fetches analysis data for editing
 * Redirects to listing on error
 */
const getPageData = async () => {
  try {
    const response = await store.dispatch('ANALYSIS_GET', {
      id: analysisID.value,
    });

    if (response) {
      setPageData(response); // Handle response data
    } else {
      throw new Error('No data received from API');
    }
  } catch (error) {
    router.push({ name: 'AnalysisFromOthers' });
  }
};

/**
 * Populates form fields with fetched analysis data
 * Handles special formatting for date fields
 */
const setPageData = () => {
  fields.value.forEach((field) => {
    // Set company and student data
    field.value = getSingleAnalysis.value[field.name];
  });
  fields.value[0].value = getSingleAnalysis.value.student?.full_name;
  fields.value[7].value = moment(getSingleAnalysis.value.created_at).format('YYYY/MM/DD');
};

/**
 * Updates analysis status and saves changes
 * @param {number} status - New analysis status (2: draft, 3: pending, 4: published)
 */
const updateAnalysis = () => {
  // Prepare data for API
  const data = {
    id: parseInt(analysisID.value),
  };

  // Add form field values
  fields.value.forEach((field) => {
    data[field.name] = field.value;
  });

  // Format dates and submit
  store.dispatch('ANALYSIS_UPDATE', data).then(() =>
    router.push({
      name: 'AnalysisFromOthers',
    })
  );
};

/**
 * Deletes analysis and shows success message
 */
const deleteAnalysis = () => {
  store
    .dispatch('ANALYSIS_DELETE', {
      id: route.params.id,
    })
    .then(() => (dialog.value.deleteSuccess = true))
    .finally(() => (loading.value = false));
};
</script>
<style lang="scss">
.kotonaru-card {
  background-color: #fffbf2;
  /* Light yellow background */
  border-radius: 8px;
  /* Rounded corners */
  overflow: hidden;
  /* Clip children to fit the rounded corners */
  min-height: 131.76px;
}

.kotonaru-card-title {
  background-color: #f9cb53;
  /* Amber color for the title background */
  color: #fff;
  /* White text color for contrast */
  font-weight: bold;
  /* Bold text for title */
  padding: 8px 12px;
  /* Padding around the title */
  text-align: center;
  /* Centered text */
  font-size: 14px;
  /* Adjust font size */
}

.kotonaru-card-text {
  padding: 12px;
  /* Padding for the text */
  font-size: 12px !important;
  /* Font size for the description */
  color: #333;
  /* Dark color for the text */
  text-align: left;
  min-height: 131.76px;
}

.button-width {
  max-width: 259px !important;
  .btn-container {
    width: 100% !important;
  }
}
</style>

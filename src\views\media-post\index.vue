<template>
  <div v-show="!loading && !$store.getters.getApiProcessingStatus">
    <PageTitle :items="titleItems" />

    <v-fade-transition>
      <SearchBox
        v-if="isSearchVisible"
        class="mb-5"
        :searchPlaceholder="'タイトル'"
        :toggleSearch="isSearchVisible"
        @toggleSearch="hideSearch"
        @search-table="handleSearch"
      />
    </v-fade-transition>

    <DataTable
      ref="dataTableRef"
      :headers="headers"
      :items="mediaPosts"
      :total-records="totalRecords"
      :number-of-pages="totalPages"
      :loading="loading"
      @update:options="updateTable"
      @row-clicked="handleRowClick"
    >
      <template #item.title="{ item }">
        <v-sheet color="transparent" class="text-truncate" max-width="450px">
          {{ item.title }}
        </v-sheet>
      </template>

      <template #item.public_date="{ item }">
        {{ formatDate(item.public_date) }}
      </template>
    </DataTable>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, defineAsyncComponent } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import moment from 'moment';
const DataTable = defineAsyncComponent(
  () => import('@/components/ui/DataTable.vue')
);
const SearchBox = defineAsyncComponent(
  () => import('@/components/ui/SearchBox.vue')
);

const store = useStore();
const router = useRouter();
const dataTableRef = ref(null);

const loading = ref(false);
const initialLoad = ref(true);
const isSearchVisible = ref(false);
const selectedStatus = ref('N');
const sortConfig = ref({
  field: 'public_date',
  order: 'desc',
});

const headers = ref([
  {
    title: 'No.',
    value: 'id',
    align: 'center',
    width: '10.72%',
    sortable: false,
  },
  {
    title: 'タイトル',
    value: 'title',
    width: '61.95%',
    align: 'left',
    sortable: false,
  },
  {
    title: '公開日',
    value: 'public_date',
    width: '7.58%',
    align: 'center',
    sortable: true,
  },
  {
    title: '表示順',
    value: 'display_order',
    width: '9.87%',
    align: 'center',
    sortable: false,
  },
  {
    title: '閲覧数',
    value: 'media_views_count',
    align: 'center',
    width: '15%',
    sortable: false,
  },
]);

/**
 * Computed properties for accessing Vuex store data
 * Provides reactive access to media posts and pagination
 */
const mediaPosts = computed(() => store.getters.getAllMedia);
const pagination = computed(() => store.getters.getMediaPagination);
const mediaCounts = computed(
  () => store.getters.getMediaCounts || { total_opened: 0, total_drafted: 0 }
);

/**
 * Computed properties for pagination display
 */
const totalRecords = computed(() => pagination.value?.records_total || 0);
const totalPages = computed(() => pagination.value?.total_pages || 0);

/**
 * Page title configuration with tabs and action buttons
 * Includes published/draft tabs and search/create buttons
 */
const titleItems = computed(() => ({
  title: 'コラム', // Column
  subTitle: '一覧', // List
  tabs: [
    {
      title: '公開中', // Published
      count: mediaCounts.value.total_opened,
      action: () => handleStatusChange('N'),
      selected: selectedStatus.value === 'N',
    },
    {
      title: '下書き', // Draft
      count: mediaCounts.value.total_drafted,
      action: () => handleStatusChange('Y'),
      selected: selectedStatus.value === 'Y',
    },
  ],
  buttons: [
    {
      title: '詳細条件検索', // Detailed search
      class: 'bg-white',
      variant: 'outlined',
      action: toggleSearch,
    },
    {
      title: '新規作成', // Create new
      icon: 'mdi-plus-circle',
      action: createNewPost,
    },
  ],
}));

/**
 * Formats date to Japanese format or placeholder
 * @param {string} date - Date string to format
 * @returns {string} Formatted date (YYYY/MM/DD) or placeholder
 */
const formatDate = (date) => {
  return date ? moment(date).format('YYYY/MM/DD') : '---- / - / -';
};

/**
 * Resets table pagination to first page
 * Updates pagination counter to trigger re-render
 */
const resetPagination = () => {
  if (dataTableRef.value) {
    dataTableRef.value.currentPage = 1;
    dataTableRef.value.updatePaginate++;
  }
};

/**
 * Handles tab change between published and draft posts
 * @param {string} status - 'N' for published, 'Y' for draft
 */
const handleStatusChange = async (status) => {
  selectedStatus.value = status;
  resetPagination();
  await fetchData();
};

/**
 * Search visibility toggle handlers
 */
const toggleSearch = () => {
  isSearchVisible.value = !isSearchVisible.value;
};

const hideSearch = () => {
  isSearchVisible.value = false;
};

/**
 * Navigation handler for creating new post
 */
const createNewPost = () => {
  router.push({ name: 'CreateMediaPost' });
};

/**
 * Handles row click navigation to edit page
 * @param {Object} item - Selected post data
 */
const handleRowClick = (item) => {
  router.push({
    name: 'EditMediaPost',
    params: { id: item.id },
  });
};

/**
 * Handles search input and triggers data fetch
 * @param {string} searchTerm - Search query text
 */
const handleSearch = async (searchTerm) => {
  await fetchData(undefined, { search: searchTerm });
};

/**
 * Fetches media posts data with pagination and filters
 * @param {Object} options - Pagination and sorting options
 * @param {Object} additionalParams - Additional search parameters
 */
const fetchData = async (options = {}, additionalParams = {}) => {
  loading.value = true;
  try {
    const params = {
      sort_by: sortConfig.value.field,
      sort_by_order: sortConfig.value.order,
      page: typeof options === 'number' ? options : (options?.page ?? 1),
      paginate: options?.itemsPerPage || 25,
      is_draft: selectedStatus.value,
      ...additionalParams,
    };

    // Remove empty search parameter
    if (params.search === '') {
      delete params.search;
    }

    await store.dispatch('MEDIA_POST_GET_ALL', params);
  } catch (error) {
    console.error('Failed to fetch media posts:', error);
  } finally {
    loading.value = false;
    initialLoad.value = false;
  }
};

/**
 * Handles table sorting and pagination updates
 * @param {Object} options - Table options including sort and page
 */
const updateTable = async (options) => {
  if (!initialLoad.value) {
    if (options.sortBy?.length) {
      sortConfig.value = {
        field: options.sortBy[0],
        order: options.sortDesc[0] ? 'desc' : 'asc',
      };
    }
    await fetchData(options);
  }
};

/**
 * Component initialization
 * Fetches initial data on mount
 */
// Fetch data when the component is mounted
onMounted(async () => {
  loading.value = true;
  await fetchData();
  loading.value = false;
});
</script>

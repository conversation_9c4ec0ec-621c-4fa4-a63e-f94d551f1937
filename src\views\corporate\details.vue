<template>
  <div class="corporate-page create-page">
    <v-sheet color="transparent">
      <PageTitle
        :items="{
          title: '企業管理',
          subTitle: '詳細',
          back: {
            action: () => {
              $router.push({
                name: 'Corporate',
              });
            },
          },
        }"
      ></PageTitle>
    </v-sheet>
    <!-- detail company -->
    <v-row>
      <v-col cols="12">
        <v-card elevation="0" class="w-full py-8">
          <v-row class="d-flex justify-center">
            <v-col
              class="w-full d-flex container-history justify-end"
              cols="12"
              md="10"
              :class="{
                'mr-8': mdAndDown,
              }"
            >
              <div>
                <v-btn
                  color=""
                  width="148"
                  height="35"
                  class="bg-default white--text"
                  placeholder="企業名"
                  @click="$router.push(`/corporate-edit/${$route.params.id}`)"
                >
                  編集
                </v-btn>
              </div>
            </v-col>
          </v-row>
          <v-row class="d-flex justify-center">
            <v-col
              class="w-full d-flex justify-center"
              cols="12"
              md="3"
              :class="{
                'container-history': lgAndUp,
                'container-history-mobile': mdAndDown,
              }"
            >
              <div class="detail-image-corporate">
                <img
                  class="w-full h-full detail-image-corporate"
                  :src="companyDetails[9].content"
                  alt="Company Logo"
                />
              </div>
            </v-col>
            <v-col
              class="w-full"
              cols="12"
              md="7"
              :class="{
                'container-history': lgAndUp,
                'container-history-mobile px-8': mdAndDown,
              }"
            >
              <div
                v-for="(item, index) in companyDetails"
                :key="index"
                class="no-truncate-history"
              >
                <div v-if="item.japLabel !== 'ロゴ画像'">
                  <div class="no-truncate-history d-flex justify-space-between">
                    <div
                      class="text-97 text-333"
                      :class="{
                        'font-18px min-w-150': lgAndUp,
                        'font-14px min-w-120': mdAndDown,
                      }"
                    >
                      <div
                        :class="{
                          'font-18px min-w-150': lgAndUp,
                          'font-14px min-w-120': mdAndDown,
                        }"
                      >
                        {{ item.japLabel }}
                      </div>
                    </div>
                    <div
                      v-if="item.label === 'business_industry'"
                      class="text-container"
                      :class="{
                        'font-16px': lgAndUp,
                        'font-12px': mdAndDown,
                      }"
                    >
                      {{ item.content[0]?.name }}
                    </div>
                    <div
                      v-if="item.label === 'flg_slack_notification'"
                      class="text-container"
                      :class="{
                        'font-16px': lgAndUp,
                        'font-12px': mdAndDown,
                      }"
                    >
                      {{ item.content ? 'オン' : 'オフ' }}
                    </div>
                    <div
                      v-else
                      class="text-container"
                      :class="{
                        'font-16px': lgAndUp,
                        'font-12px': mdAndDown,
                      }"
                    >
                      {{ item.content }}
                    </div>
                  </div>
                </div>
                <v-divider
                  v-if="index + 1 !== 9"
                  style="background-color: #e5e5e5"
                  class="mb-4"
                ></v-divider>
              </div>
            </v-col>
          </v-row>
        </v-card>
      </v-col>
    </v-row>

    <!-- user modal -->
    <CompanyUserDialog
      text="この企業ユーザを削除してもよろしいですか?"
      :dialog="dialog.add"
      :companyUsers="companyUsers"
      @closeModel="closeModal"
      :selectedCompanyUser="selectedCompanyUser"
    />
    <!-- users company -->
    <v-sheet color="transparent">
      <PageTitle
        :items="{
          title: '企業ユーザ - 一覧',
          subTitle: '',
          buttons: [
            {
              title: '新規作成',
              icon: 'mdi-plus-circle',
              action: () => {
                dialog.add = true;
              },
            },
          ],
        }"
      ></PageTitle>
    </v-sheet>
    <v-row>
      <v-col cols="12" md="12" class="d-flex w-100">
        <DataTable
          :items="initialLoad ? [] : companyUsers"
          :headers="headers"
          @row-clicked="handleRowClick"
        >
          <template v-slot:serial_number="{ item, index }">
            <!-- Calculate the correct index -->
          </template>
          <template v-slot:[`item.email_valid`]="{ item }">
            {{ item.email_valid ? item.email_valid : item.email_invalid }}
            {{ item.flg_email_approved === '1' ? '（認証済）' : '（未認証）' }}
          </template>
          <template v-slot:item.delete="{ item }">
            <v-btn
              v-if="!item.isFirstUser || item.type_user === 2"
              variant="text"
              color="transparent"
              @click.stop="deleteInitiate(item.id)"
            >
              <v-icon size="20">$delete</v-icon></v-btn
            >
          </template>
        </DataTable>
      </v-col>
    </v-row>
    <SimpleModel
      text="この企業ユーザを削除してよろしいですか？"
      :dialog="dialog.delete"
      @submitSuccess="deleteCompnayUser()"
      @closeModel="dialog.delete = false"
      :submitButtonText="'削除する'"
    ></SimpleModel>
  </div>
</template>
<script>
import { useStore } from 'vuex';
import { ref, computed, onMounted, defineAsyncComponent } from 'vue';
import { useDisplay } from 'vuetify';
const DataTable = defineAsyncComponent(
  () => import('@/components/ui/DataTable.vue')
);
const CompanyUserDialog = defineAsyncComponent(
  () => import('@/components/models/CompanyUserDialog.vue')
);
const SimpleModel = defineAsyncComponent(
  () => import('@/components/models/SimpleModel.vue')
);
import CorporateMixin from './corporate.mixin';
import { useRoute } from 'vue-router';

export default {
  name: 'CorporateDetails',
  metaInfo: {
    title: 'コトナル 管理者 企業管理 | 企業詳細',
  },

  components: { DataTable, SimpleModel, CompanyUserDialog },
  setup() {
    const { fields } = CorporateMixin();
    const route = useRoute();
    const { mdAndUp, smAndDown, lgAndUp, mdAndDown } = useDisplay();
    const store = useStore();
    const initialLoad = ref(true);
    const companyDetails = ref([
      { japLabel: '企業ID', label: 'random_number_id', content: '' },
      { japLabel: '内部ID', label: 'internal_company_id', content: '' },
      { japLabel: '企業名', label: 'name', content: '' },
      { japLabel: '企業名フリガナ', label: 'furigana_name', content: '' },
      { japLabel: '業界', label: 'business_industry', content: '-' },
      { japLabel: '住所', label: 'office_address', content: '' },
      { japLabel: '代表電話番号', label: 'office_phone', content: '' },
      { japLabel: 'ホームページURL', label: 'website_url', content: '' },
      { japLabel: 'メモ', label: 'admin_memo', content: '' },
      {
        japLabel: 'ロゴ画像',
        label: 'logo_img',
        content: '',
      },
      { japLabel: 'Slack通知', label: 'flg_slack_notification', content: '' },
    ]);
    const headers = ref([
      {
        title: 'No.', // Column for row number
        align: 'center',
        sortable: false,
        value: 'serial_number',
      },
      {
        title: '企業ユーザ名',
        align: 'left',
        sortable: false,
        value: 'fullName',
      },
      {
        title: 'ユーザ区分',
        align: 'left',
        sortable: false,
        value: 'type_user_title',
      },
      {
        title: 'メールアドレス',
        align: 'left',
        sortable: false,
        value: 'email_valid',
      },
      {
        title: '電話番号',
        align: 'left',
        sortable: false,
        value: 'tel',
      },
      {
        title: '',
        value: 'delete',
        sortable: false,
      },
    ]);
    /**
     * Reactive references for managing UI state and data
     */
    const showToggleCopy = ref(false);
    const temporaryDeleteId = ref(null); // Stores ID of user pending deletion
    const basicInformation = ref(null);
    const selectedCompanyUser = ref(null); // Currently selected user for edit/view
    const dialog = ref({
      delete: false, // Controls delete confirmation dialog
      add: false, // Controls add/edit user dialog
      edit: false, // Reserved for future edit functionality
    });

    /**
     * Handle row click in company users table
     * Opens dialog for editing selected user
     * @param {Object} item - Selected user data
     */
    const handleRowClick = async (item) => {
      selectedCompanyUser.value = item;
      dialog.value.add = true;
    };

    /**
     * Submit form handler for company user data
     * Handles both creation and update scenarios
     * @param {Object} data - Form data to be submitted
     */
    const submitForm = async (data) => {
      basicInformation.value = data;
      // Add required validation to fields marked as required
      basicInformation.value = basicInformation.value.map((item) => {
        if (item.requiredChecks) {
          item.rules = 'required|' + item.rules;
        }
        return item;
      });

      // Handle update or create based on whether a user is selected
      if (selectedCompanyUser.value) {
        let params = {
          id: selectedCompanyUser.value.id,
        };
        await savePageData('update', params);
      } else {
        await savePageData('create');
      }

      await getPageDataFromApi();
    };

    /**
     * Close modal handler with optional data refresh
     * @param {boolean} isRefresh - Whether to refresh data after closing
     */
    const closeModal = (isRefresh) => {
      dialog.value.add = false;
      // Clear selected user after animation completes
      setTimeout(() => {
        selectedCompanyUser.value = null;
      }, 500);
      if (isRefresh) getPageDataFromApi();
    };

    /**
     * Delete company user handler
     * Sends delete request and handles response
     */
    const deleteCompnayUser = async () => {
      let params = {
        id: temporaryDeleteId.value,
      };
      await store
        .dispatch('COMPANY_USER_DELETE', params)
        .then(() => {
          getPageDataFromApi();
          dialog.value.delete = false;
        })
        .catch((error) => {
          console.error(
            'An error occurred while deleting the company user:',
            error
          );
          dialog.value.delete = false;
        });
    };

    /**
     * Initialize delete process for a company user
     * @param {number} id - ID of user to be deleted
     */
    const deleteInitiate = (id) => {
      dialog.value.delete = true;
      temporaryDeleteId.value = id;
    };

    /**
     * Fetch and update company details and user data
     * Handles special formatting for business industry data
     */
    const getPageDataFromApi = async () => {
      await store.dispatch('CORPORATE_GET', { id: route.params.id });
      if (initialLoad.value) initialLoad.value = false;

      // Update company details with fetched data
      for (let index in companyDetails.value) {
        if (companyDetails.value.hasOwnProperty(index)) {
          if (companyDetails.value[index].label === 'business_industry') {
            // Special handling for business industry data
            const industry = getCorporateCategories.value.find(
              (category) =>
                category.id === getSingleCorporate.value.business_industry_id
            );
            if (industry && industry.name) {
              companyDetails.value[index].content = industry.name;
            }
          } else {
            // Default handling for other fields
            companyDetails.value[index].content =
              getSingleCorporate.value[companyDetails.value[index].label] ??
              '-';
          }
        }
      }
      fields.value.id = getSingleCorporate.value.id;
    };

    /**
     * Vuex getters for corporate data
     */
    const getSingleCorporate = computed(() => store.getters.getSingleCorporate);
    const getCorporateCategories = computed(
      () => store.getters.getCorporateCategories
    );

    /**
     * Computed property for company users list
     * Sorts users by creation date and marks the first type 1 user
     * @returns {Array} Processed list of company users
     */
    const companyUsers = computed(() => {
      if (!getSingleCorporate.value?.company_user) return [];

      // Sort users by creation date
      const sortedUsers = getSingleCorporate.value.company_user
        .slice()
        .sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

      // Track first type 1 user for each company
      const firstUsers = {};
      return sortedUsers.map((user) => {
        // Mark first type 1 user as undeletable
        if (user.type_user === 1 && !firstUsers[user.company_id]) {
          firstUsers[user.company_id] = true;
          return { ...user, isFirstUser: true };
        }
        return { ...user, isFirstUser: false };
      });
    });

    /**
     * Component initialization
     * Fetches required data on mount
     */
    onMounted(() => {
      getPageDataFromApi();
    });

    return {
      showToggleCopy,
      companyDetails,
      fields,
      basicInformation,
      companyUsers,
      selectedCompanyUser,
      dialog,
      temporaryDeleteId,
      handleRowClick,
      submitForm,
      closeModal,
      deleteCompnayUser,
      deleteInitiate,
      getPageDataFromApi,
      getSingleCorporate,
      getCorporateCategories,
      companyUsers,
      mdAndUp,
      smAndDown,
      lgAndUp,
      mdAndDown,
      headers,
      initialLoad,
    };
  },
};
</script>

<style lang="scss" src="./style.scss" scope></style>

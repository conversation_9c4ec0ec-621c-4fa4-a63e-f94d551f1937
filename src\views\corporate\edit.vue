<template>
  <div class="corporate-page create-page">
    <v-sheet color="transparent">
      <PageTitle
        :items="{
          title: '企業管理',
          subTitle: '編集',
          back: {
            action: () => {
              $router.push({
                name: 'Corporate',
              });
            },
          },
        }"
      ></PageTitle>
      <v-row>
        <v-col cols="12">
          <div>
            <Form @submit="submit">
              <v-card class="pa-5 rounded-sm">
                <v-container class="container-main">
                  <v-row>
                    <v-col cols="12" md="4" class="mt-1">
                      <div
                        class="d-flex flex-column align-center justify-center"
                      >
                        <div
                          class="file-input-box d-flex align-center justify-center preview"
                          @mouseover="$refs.close.style.visibility = 'visible'"
                          @mouseleave="$refs.close.style.visibility = 'hidden'"
                          v-if="croppedImagePreview"
                        >
                          <div
                            class="close d-flex align-center justify-center"
                            ref="close"
                          >
                            <v-btn
                              @click="imageCancel"
                              width="50px"
                              height="50px"
                              color="primary"
                              fab
                              dark
                              ><v-icon color="white">mdi-close</v-icon></v-btn
                            >
                          </div>
                          <img
                            class="full-height full-width"
                            :src="croppedImagePreview"
                          />
                        </div>
                        <Field
                          v-else
                          v-slot="{
                            field: { value, ...fieldWithoutValue },
                            errors,
                          }"
                          :value="getSingleCorporate['logo_img']"
                          name="logo_img"
                          rules="size:2048|ext:jpg,png|maxDimensions:500,500"
                        >
                          <div
                            class="file-input-box d-flex align-center justify-center"
                          >
                            <div class="file-input-inr">
                              <v-file-input
                                ref="fileInput"
                                accept="image/*"
                                v-bind="fieldWithoutValue"
                                :error-messages="errors"
                                :error="errors.length !== 0"
                                :hide-details="errors.length <= 0"
                                hide-input
                                truncate-length="1"
                                name="logo_image"
                                @change="previewImage"
                                v-model="fields.logo_img"
                                prepend-icon="mdi-plus-circle"
                                class="d-flex align-center justify-center"
                              ></v-file-input>
                              <h6 class="font-14px fw-500 text-default mb-0">
                                ロゴを登録する
                              </h6>
                            </div>
                          </div>
                          <template v-if="errors.length > 0">
                            <span class="font-12px text-red">{{
                              errors[0]
                            }}</span>
                          </template>
                        </Field>

                        <ImageCrop
                          v-if="previewImageURL"
                          :imageSrc="previewImageURL"
                          @image-crop-success="imageCropSuccess"
                          @crop-image-cancel="imageCropCancel"
                        ></ImageCrop>
                        <v-sheet
                          color="#F9F9F9"
                          width="200px"
                          height="91px"
                          class="d-flex align-center justify-center font-14px mt-4 rounded"
                        >
                          画素：256px×256px以上<br />
                          サイズ：2Mb以下<br />
                          形式：jpg、png
                        </v-sheet>
                      </div>
                    </v-col>
                    <v-col cols="12" md="8">
                      <v-row>
                        <v-col cols="12" md="12" class="mb-n5">
                          <label class="d-block font-14px mb-1">
                            <span>内部ID</span>
                            <span class="error--text ml-2 font-12px">必須</span>
                          </label>
                          <Field
                            v-slot="{
                              field: { value, ...fieldWithoutValue },
                              errors,
                            }"
                            name="company_id"
                            ref="observer"
                            :value="getSingleCorporate['internal_company_id']"
                            rules="required:内部ID"
                          >
                            <v-text-field
                              v-bind="fieldWithoutValue"
                              :error-messages="errors"
                              :error="errors.length !== 0"
                              :hide-details="errors.length <= 0"
                              variant="outlined"
                              v-model="fields.internal_company_id"
                              density="compact"
                              placeholder="内部ID"
                            >
                            </v-text-field>
                          </Field>
                        </v-col>
                        <v-col cols="12" md="12" class="mb-n5">
                          <label class="d-block font-14px mb-1">
                            <span>企業名</span>
                            <span class="error--text ml-2 font-12px">必須</span>
                          </label>
                          <Field
                            v-slot="{
                              field: { value, ...fieldWithoutValue },
                              errors,
                            }"
                            :value="getSingleCorporate['name']"
                            name="name"
                            rules="required:企業名 "
                          >
                            <v-text-field
                              v-bind="fieldWithoutValue"
                              :error-messages="errors"
                              :error="errors.length !== 0"
                              :hide-details="errors.length <= 0"
                              variant="outlined"
                              v-model="fields.name"
                              density="compact"
                              placeholder="企業名"
                              color="grey"
                              id="name"
                              name="name"
                              @input="handleNameInput"
                            >
                            </v-text-field>
                          </Field>
                        </v-col>
                        <v-col cols="12" md="12" class="mb-n5">
                          <label class="d-block font-14px mb-1">
                            <span>企業名フリガナ</span>
                          </label>
                          <Field
                            v-slot="{
                              field: { value, ...fieldWithoutValue },
                              errors,
                            }"
                            :value="getSingleCorporate['furigana_name']"
                            name="furigana_name"
                            rules="full_width_katakana|max:100"
                          >
                            <v-text-field
                              v-bind="fieldWithoutValue"
                              :error-messages="errors"
                              :error="errors.length !== 0"
                              :hide-details="errors.length <= 0"
                              variant="outlined"
                              v-model="fields.furigana_name"
                              density="compact"
                              id="furigana"
                              name="furigana"
                              placeholder="企業名フリガナ"
                            >
                            </v-text-field>
                          </Field>
                        </v-col>
                        <v-col cols="12" md="12" class="mb-n5">
                          <label class="d-block font-14px mb-1">
                            <span>業界</span>
                          </label>
                          <Field
                            v-slot="{
                              field: { value, ...fieldWithoutValue },
                              errors,
                            }"
                            :value="getSingleCorporate['business_industry_id']"
                            name="business_industry_id"
                          >
                            <v-select
                              v-bind="fieldWithoutValue"
                              :error-messages="errors"
                              :error="errors.length !== 0"
                              :hide-details="errors.length <= 0"
                              variant="outlined"
                              v-model="fields.business_industry_id"
                              density="compact"
                              placeholder="選択してください"
                              item-title="name"
                              item-value="id"
                              :items="
                                $store.state.master.master.business_industories
                              "
                              color="grey"
                            >
                            </v-select>
                          </Field>
                        </v-col>
                        <v-col cols="12" md="12" class="mb-n5">
                          <label class="d-block font-14px mb-1">
                            <span>住所</span>
                          </label>
                          <Field
                            v-slot="{
                              field: { value, ...fieldWithoutValue },
                              errors,
                            }"
                            :value="getSingleCorporate['office_address']"
                            name="office_address"
                            rules="max:100"
                          >
                            <v-text-field
                              v-bind="fieldWithoutValue"
                              :error-messages="errors"
                              :error="errors.length !== 0"
                              :hide-details="errors.length <= 0"
                              auto-select-first
                              chips
                              deletable-chips
                              density="compact"
                              multiple
                              small-chips
                              v-model="fields.office_address"
                              variant="outlined"
                              item-text="ja"
                              item-value="id"
                              placeholder="住所"
                              color="grey"
                            ></v-text-field>
                          </Field>
                        </v-col>
                      </v-row>
                      <v-row>
                        <v-col cols="12" md="12" class="mb-n5">
                          <label class="d-block font-14px mb-1">
                            <span>電話番号</span>
                          </label>
                          <Field
                            v-slot="{
                              field: { value, ...fieldWithoutValue },
                              errors,
                            }"
                            :value="getSingleCorporate['office_phone']"
                            name="office_phone"
                            rules="enter_half_width_numbers_hyphens"
                          >
                            <v-text-field
                              v-bind="fieldWithoutValue"
                              :error-messages="errors"
                              :error="errors.length !== 0"
                              :hide-details="errors.length <= 0"
                              variant="outlined"
                              v-model="fields.office_phone"
                              density="compact"
                              placeholder="電話番号"
                            >
                            </v-text-field>
                          </Field>
                        </v-col>
                      </v-row>
                      <v-row>
                        <v-col cols="12" md="12" class="mb-n5">
                          <label class="d-block font-14px mb-1">
                            <span>ホームページURL</span>
                          </label>
                          <Field
                            v-slot="{
                              field: { value, ...fieldWithoutValue },
                              errors,
                            }"
                            :value="getSingleCorporate['website_url']"
                            name="website_url"
                            rules="url"
                          >
                            <v-text-field
                              v-bind="fieldWithoutValue"
                              :error-messages="errors"
                              :error="errors.length !== 0"
                              :hide-details="errors.length <= 0"
                              variant="outlined"
                              v-model="fields.website_url"
                              density="compact"
                              placeholder="ホームページURL"
                            >
                            </v-text-field>
                          </Field>
                        </v-col>
                      </v-row>
                      <v-row>
                        <v-col cols="12" md="12" class="mb-n5">
                          <label class="d-block font-14px mb-1">
                            <span>メモ</span>
                          </label>
                          <Field
                            v-slot="{
                              field: { value, ...fieldWithoutValue },
                              errors,
                            }"
                            :value="getSingleCorporate['admin_memo']"
                            name="admin_memo"
                            rules="max:1500"
                          >
                            <v-textarea
                              v-bind="fieldWithoutValue"
                              :error-messages="errors"
                              :error="errors.length !== 0"
                              :hide-details="errors.length <= 0"
                              variant="outlined"
                              v-model="fields.admin_memo"
                              density="compact"
                              placeholder="メモ"
                            >
                            </v-textarea>
                          </Field>
                        </v-col>
                      </v-row>

                      <div class="slack-container mt-10">
                        <v-row class="py-4 px-6" align="center">
                          <v-col cols="8" md="8">
                            <div>Slack通知</div>
                            <div class="text-8e">
                              システム内でメール通知があったタイミングで、
                            </div>
                            <div class="text-8e">
                              Slackチャンネルに通知が届きます。
                            </div>
                          </v-col>
                          <v-col cols="4" md="4">
                            <Field
                              v-slot="{
                                field: { value, ...fieldWithoutValue },
                                errors,
                              }"
                              :value="
                                getSingleCorporate['flg_slack_notification']
                              "
                              name="flg_slack_notification"
                              rules=""
                            >
                              <v-select
                                v-bind="fieldWithoutValue"
                                density="compact"
                                variant="outlined"
                                single-line
                                color="#13ABA3"
                                class="font-14px mt-1 small-label"
                                :hide-details="true"
                                :items="slackOptions"
                                item-title="name"
                                item-value="value"
                                v-model="fields.flg_slack_notification"
                                menu-icon="$greyExpansionDropdown"
                                style="background-color: white"
                              />
                            </Field>
                          </v-col>
                          <v-col v-if="fields.flg_slack_notification" cols="12">
                            <Field
                              v-slot="{
                                field: { value, ...fieldWithoutValue },
                                errors,
                              }"
                              :value="getSingleCorporate['slack_webhook']"
                              name="slack_webhook"
                              :rules="
                                fields.flg_slack_notification
                                  ? 'error_slack'
                                  : ''
                              "
                            >
                              <v-text-field
                                v-bind="fieldWithoutValue"
                                :hide-details="true"
                                auto-select-first
                                density="compact"
                                placeholder="Webhook URL を設定してください。"
                                v-model="fields.slack_webhook"
                                variant="outlined"
                                style="background-color: white"
                              ></v-text-field>
                              <div
                                class="error--text font-12px"
                                v-if="errors.length !== 0"
                              >
                                {{ errors[0] }}
                              </div>
                            </Field>
                          </v-col>
                        </v-row>
                      </div>

                      <v-row>
                        <v-col cols="12" md="12" class="mb-n5">
                          <Field
                            v-slot="{ field, errors }"
                            :value="getSingleCorporate['status']"
                            name="status"
                            rules=""
                          >
                            <v-col cols="6" class="ml-16 pl-8">
                              <v-switch
                                color="#13ABA3"
                                class="mt-0 pt-0"
                                :error-messages="errors"
                                :error="errors.length !== 0"
                                :hide-details="errors.length <= 0"
                                :model-value="switchValue"
                                @update:modelValue="onSwitchChange"
                                outlined
                                :label="switchLabel"
                              ></v-switch>
                            </v-col>
                          </Field>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12" md="4">
                      <v-btn
                        variant="text"
                        :ripple="false"
                        elevation="0"
                        color="#E14D56"
                        class="delete-btn d-flex align-center font-14px px-2 py-1 ml-16"
                        @click.prevent="dialog.delete = true"
                      >
                        <v-icon size="18" class="mr-1"> $Warning </v-icon>
                        削除する
                      </v-btn>
                      <SimpleModel
                        :dialog="dialog.delete"
                        :loading="loading"
                        submitButtonText="削除する"
                        @submitSuccess="deleteCorporate()"
                        @closeModel="dialog.delete = false"
                      ></SimpleModel>
                    </v-col>
                    <v-col cols="12" md="8" class="pl-10">
                      <v-btn
                        type="submit"
                        :loading="loading"
                        color=""
                        width="150"
                        height="35"
                        class="bg-default white--text ml-13"
                        placeholder="企業名"
                      >
                        更新
                      </v-btn>
                    </v-col>
                  </v-row>
                </v-container>
              </v-card>
            </Form>
          </div>
          <SuccessModel
            :text="`企業情報を更新しました。`"
            :buttonText="`企業詳細へ戻る`"
            :routeName="`CorporateDetails`"
            :specificId="currentCorporateId"
            :dialog="successDialog"
            @closeModel="successDialog.value = false"
          ></SuccessModel>
        </v-col>
      </v-row>
    </v-sheet>
  </div>
</template>

<script>
import * as AutoKana from 'vanilla-autokana';
import { ref, onMounted, computed, defineAsyncComponent } from 'vue';
const SimpleModel = defineAsyncComponent(
  () => import('@/components/models/SimpleModel.vue')
);
const SuccessModel = defineAsyncComponent(
  () => import('@/components/models/SuccessModel.vue')
);
const ImageCrop = defineAsyncComponent(
  () => import('@/components/admin/partials/Corporate/ImageCrop.vue')
);
import CorporateMixin from './corporate.mixin';
import { Field, Form } from 'vee-validate';
import { useStore } from 'vuex';
import { useRouter, useRoute } from 'vue-router';

export default {
  name: 'Create',
  components: { SuccessModel, ImageCrop, Field, Form, SimpleModel },
  metaInfo: {
    title: 'コトナル 管理者 企業管理 | 企業詳細',
  },
  setup() {
    let autokana;
    const store = useStore();
    const router = useRouter();
    const route = useRoute();
    const {
      previewImageURL,
      croppedImagePreview,
      loading,
      postalCodeLoading,
      fields,
      switchLabel,
      handleNameInput,
      imageCropSuccess,
      imageCropCancel,
      previewImage,
      logoImageId,
      successDialog,
    } = CorporateMixin();

    const dialog = ref({
      delete: false,
    });

    const getSingleCorporate = computed(() => store.getters.getSingleCorporate);

    const switchValue = ref(false);
    const onSwitchChange = (value) => {
      switchValue.value = value; // Update the local switch state
      fields.value.status = value; // Update the Vee-Validate form value manually
    };

    const currentCorporateId = ref(null);
    const slackOptions = ref([
      {
        value: 0,
        name: 'オフ',
      },
      {
        value: 1,
        name: 'オン',
      },
    ]);

    /**
     * Handles form submission for corporate update
     * @param {Object} values - Form values from vee-validate
     */
    const submit = async (values) => {
      loading.value = true;
      fields.logo_img = croppedImagePreview.value;
      try {
        // Update form values with current state
        values.status = switchValue.value;
        // Check if logo is a new upload or existing URL
        fields.value.image_update = !isValidHttpUrl(fields.value.logo_img);
        values.image_update = fields.value.image_update;
        values.logo_img = croppedImagePreview.value;
        // Preserve internal company ID from existing data
        values.internal_company_id =
          getSingleCorporate.value['internal_company_id'];
        values.id = route.params.id;
        const data = { ...values };

        // Update corporate data and refresh master data
        const response = await store.dispatch('CORPORATE_UPDATE', data);
        currentCorporateId.value = response.data.data.data.id;
        successDialog.value = true;
      } catch (error) {
        // Error handling could be added here
      } finally {
        loading.value = false;
      }
    };

    /**
     * Populates form fields with existing corporate data
     * Handles special cases for logo image and status switch
     */
    const setPageData = async () => {
      for (let field in fields.value) {
        if (field === 'logo_img') {
          // Set logo preview if exists
          croppedImagePreview.value = getSingleCorporate.value[field];
        }
        if (field === 'status') {
          // Convert status to boolean for switch component
          switchValue.value = getSingleCorporate.value[field] ? true : false;
        }
        // Set field value or null if not exists
        fields.value[field] = getSingleCorporate.value[field] ?? null;
      }
      fields.value.id = getSingleCorporate.value.id;
    };

    /**
     * Fetches corporate data from API and populates form
     */
    const getPageDataFromApi = () => {
      store
        .dispatch('CORPORATE_GET', {
          id: route.params.id,
        })
        .then(setPageData);
    };

    /**
     * Handles logo image removal
     * Clears both preview and form value
     */
    const imageCancel = () => {
      croppedImagePreview.value = null;
      fields.value.logo_img = null;
    };

    /**
     * Validates if a string is a valid HTTP/HTTPS URL
     * @param {string} string - URL to validate
     * @returns {boolean} True if valid HTTP/HTTPS URL
     */
    const isValidHttpUrl = (string) => {
      let url;
      try {
        url = new URL(string);
      } catch (_) {
        return false;
      }
      return url.protocol === 'http:' || url.protocol === 'https:';
    };

    /**
     * Handles corporate deletion
     * Shows loading state and redirects to listing on success
     */
    const deleteCorporate = () => {
      loading.value = true;
      store
        .dispatch('CORPORATE_DELETE', {
          id: route.params.id,
        })
        .then(() => {
          router.push({ name: 'Corporate' });
        })
        .catch(() => {})
        .finally(() => (loading.value = false));
    };

    /**
     * Component initialization
     * Fetches required data and sets up autokana binding
     */
    onMounted(() => {
      getPageDataFromApi();
      // Bind autokana for automatic furigana conversion
      autokana = AutoKana.bind('#name', '#furigana');
    });

    return {
      currentCorporateId,
      slackOptions,
      submit,
      loading,
      fields,
      switchLabel,
      handleNameInput,
      imageCropSuccess,
      imageCropCancel,
      previewImage,
      logoImageId,
      previewImageURL,
      croppedImagePreview,
      postalCodeLoading,
      successDialog,
      onSwitchChange,
      switchValue,
      dialog,
      imageCancel,
      isValidHttpUrl,
      setPageData,
      getSingleCorporate,
      deleteCorporate,
    };
  },
};
</script>

<style lang="scss" src="./style.scss" scope></style>

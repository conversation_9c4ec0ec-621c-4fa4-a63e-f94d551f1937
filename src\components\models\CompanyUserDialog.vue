<template>
  <v-dialog v-model="selfDialog" max-width="820px" persistent>
    <Form @submit="submitForm">
      <v-card class="custom-card py-4" v-slot:default>
        <v-card-title class="title-container">
          <v-row no-gutters>
            <v-col cols="11" class="text-center">
              <div class="title-text">
                {{ selectedCompanyUser ? '編集' : '新規登録' }}
              </div>
            </v-col>
            <v-col cols="1" class="text-right">
              <v-btn
                variant="text"
                class="close-button"
                @click="selfDialog = false"
              >
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </v-col>
          </v-row>
        </v-card-title>
        <v-card-text class="px-16">
          <Field
            v-slot="{ field: { value, ...fieldWithoutValue }, errors }"
            name="企業名"
            rules="required"
            :value="form.type_user"
          >
            <span v-if="companyUsers.length > 0" class="error--text font-12px"
              >必須</span
            >
            <v-radio-group
              v-bind="fieldWithoutValue"
              v-model="form.type_user"
              row
              class="user-type mt-0 mb-2"
              style="margin-left: -5px"
              inline
              density="compact"
              :error-messages="errors"
              :error="errors.length !== 0"
              :hide-details="errors.length <= 0"
            >
              <v-radio
                label="管理ユーザ"
                value="1"
                class="mr-2"
                color="primary"
              ></v-radio>
              <v-radio
                :disabled="
                  companyUsers.length === 0 ||
                  (companyUsers.length === 1 && selectedCompanyUser) ||
                  selectedCompanyUser?.isFirstUser
                "
                label="一般ユーザ"
                value="2"
                color="primary"
              ></v-radio>
            </v-radio-group>
          </Field>

          <!-- Name Fields -->
          <v-row>
            <v-col cols="12" v-if="$route.name === 'CompanyUserListing'">
              <label class="d-block font-14px mb-1">
                <span>企業名</span>
                <span class="error--text ml-2 font-12px">必須</span>
              </label>
              <Field
                v-slot="{ field: { value, ...fieldWithoutValue }, errors }"
                name="企業名"
                rules="required"
                :value="company.value"
              >
                <v-autocomplete
                  v-bind="fieldWithoutValue"
                  density="compact"
                  variant="outlined"
                  color="#13ABA3"
                  class="font-16px mt-1"
                  :error-messages="errors"
                  :error="errors.length !== 0"
                  :hide-details="errors.length <= 0"
                  :search-input.sync="company.searched_text"
                  @keyup="
                    company.searchable ? company.search_api(company) : false
                  "
                  :loading="company.is_loading"
                  hide-no-data
                  hide-selected
                  :items="company.items"
                  :item-title="company.item_text"
                  :item-value="company.item_value"
                  :placeholder="company.placeholder"
                  v-model="company.value"
                  autocomplete="new-password"
                >
                </v-autocomplete>
              </Field>
            </v-col>
            <v-col cols="6">
              <div class="full-width">
                <div class="font-14px">
                  姓
                  <span class="font-12px error--text ml-2">必須</span>
                </div>
                <Field
                  v-slot="{ field, errors }"
                  :name="'surname'"
                  :rules="requiredRule"
                  :value="form.surname"
                >
                  <v-text-field
                    v-bind="field"
                    v-model="form.surname"
                    :error-messages="errors"
                    :error="errors.length !== 0"
                    :hide-details="errors.length <= 0"
                    single-line
                    color="#13ABA3"
                    class="mt-1"
                    variant="outlined"
                    density="compact"
                  ></v-text-field>
                </Field>
              </div>
            </v-col>
            <v-col cols="6">
              <div class="full-width">
                <div class="font-14px">
                  名
                  <span class="font-12px error--text ml-2">必須</span>
                </div>
                <Field
                  v-slot="{ field, errors }"
                  :name="'name'"
                  :rules="requiredRule"
                  :value="form.name"
                >
                  <v-text-field
                    v-bind="field"
                    v-model="form.name"
                    :error-messages="errors"
                    :error="errors.length !== 0"
                    :hide-details="errors.length <= 0"
                    single-line
                    color="#13ABA3"
                    class="mt-1"
                    variant="outlined"
                    density="compact"
                  ></v-text-field>
                </Field>
              </div>
            </v-col>
          </v-row>

          <!-- Email Field -->
          <div class="full-width">
            <div class="font-14px">
              メールアドレス
              <span class="font-12px error--text ml-2">必須</span>
            </div>
            <Field
              v-slot="{ field, errors }"
              :name="'email_invalid'"
              :rules="emailRule"
              :value="form.email_invalid"
            >
              <v-text-field
                v-bind="field"
                v-model="form.email_invalid"
                :error-messages="errors"
                :error="errors.length !== 0"
                :hide-details="errors.length <= 0"
                single-line
                color="#13ABA3"
                class="mt-1"
                variant="outlined"
                density="compact"
                @update:model-value="errorEmail = ''"
              ></v-text-field>
              <div
                class="mb-1 text-error mt-1 text-left font-12px"
                v-if="errorEmail && form.email_invalid"
              >
                <FlashMessage :error="errorEmail" />
              </div>
            </Field>
          </div>

          <!-- Phone Number Field -->
          <div class="full-width">
            <div class="font-14px">電話番号</div>
            <Field
              v-slot="{ field, errors }"
              :name="'tel'"
              :rules="phoneRule"
              :value="form.tel"
            >
              <v-text-field
                v-bind="field"
                v-model="form.tel"
                :error-messages="errors"
                :error="errors.length !== 0"
                :hide-details="errors.length <= 0"
                single-line
                color="#13ABA3"
                class="mt-1"
                variant="outlined"
                density="compact"
              ></v-text-field>
            </Field>
          </div>

          <!-- Password Field -->
          <div class="full-width">
            <div class="font-14px">
              パスワード 英数字8文字以上
              <span class="font-12px error--text ml-2">必須</span>
            </div>
            <Field
              v-slot="{ field, errors }"
              :name="'password'"
              :rules="selectedCompanyUser && !form.password ? '' : passwordRule"
            >
              <div class="d-flex align-start">
                <v-text-field
                  v-bind="field"
                  name="password"
                  v-model="form.password"
                  :error-messages="errors"
                  :error="errors.length !== 0"
                  :hide-details="errors.length <= 0"
                  :placeholder="selectedCompanyUser ? '.....' : ''"
                  :type="'text'"
                  single-line
                  color="#13ABA3"
                  class="mt-1"
                  variant="outlined"
                  density="compact"
                  style="flex: 1"
                  @focus="clearPasswordIfPlaceholder"
                ></v-text-field>
                <v-btn
                  class="ml-2 mt-1"
                  max-width="120px"
                  height="39px"
                  style="flex: 1; color: white"
                  density="compact"
                  :color="form.password ? '#13ABA3' : '#B8B8B8'"
                  :disabled="!form.password"
                  @click="copyToClipboard(form.password)"
                >
                  コピー
                </v-btn>
              </div>
            </Field>
          </div>

          <!-- Confirm Password Field -->
          <div class="full-width">
            <div class="font-14px">
              パスワード（確認用）
              <span class="font-12px error--text ml-2">必須</span>
            </div>
            <Field
              v-slot="{ field, errors }"
              :name="'confirmPassword'"
              :rules="
                selectedCompanyUser && !form.password ? '' : confirmPasswordRule
              "
            >
              <v-text-field
                v-bind="field"
                name="password_confirmation"
                v-model="form.confirmPassword"
                :error-messages="errors"
                :error="errors.length !== 0"
                :hide-details="errors.length <= 0"
                :placeholder="selectedCompanyUser ? '.....' : ''"
                :type="'text'"
                single-line
                color="#13ABA3"
                class="mt-1"
                style="max-width: 560px"
                variant="outlined"
                density="compact"
                @focus="clearConfirmPasswordIfPlaceholder"
              ></v-text-field>
            </Field>
          </div>
        </v-card-text>

        <v-card-actions class="d-flex justify-center">
          <v-btn
            v-if="selectedCompanyUser"
            color="primary"
            @click="selfDialog = false"
            style="width: 20%"
            variant="outlined"
            elevation="4"
          >
            戻る
          </v-btn>
          <v-btn
            color="#13ABA3"
            class="bg-primary text-white"
            elevation="4"
            type="submit"
            style="width: 20%"
          >
            {{ selectedCompanyUser ? '保存' : '登録' }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </Form>
  </v-dialog>
</template>

<script>
import { computed, ref, watch } from 'vue';
import { useStore } from 'vuex';
import { debounce } from 'debounce';
import { Field, Form, useForm } from 'vee-validate';
import { useRoute } from 'vue-router';
import FlashMessage from '@/components/FlashMessage.vue';

export default {
  components: {
    Field,
    Form,
    FlashMessage,
  },
  props: {
    dialog: {
      type: Boolean,
      default: true,
      required: true,
    },
    selectedCompanyUser: {
      type: Object,
      required: false,
    },
    companyUsers: {
      type: Array,
      required: false,
    },
  },
  /**
   * Component Setup
   * Handles company user creation and editing functionality
   * @param {Object} props - Component props containing dialog state and selected user
   * @param {Function} emit - Event emitter for parent communication
   */
  setup(props, { emit }) {
    const store = useStore();
    const route = useRoute();

    /**
     * Form State Management
     * Default form values for company user
     */
    const form = ref({
      type_user: '', // Default to general user (1: admin, 2: general)
      surname: '', // Family name
      name: '', // Given name
      email_invalid: '', // Email address
      tel: '', // Phone number
      password: '', // Password
      confirmPassword: '', // Password confirmation
    });

    /**
     * Company Data Management
     */
    // Get all companies from Vuex store
    const getAllCompany = computed(() => store.getters.getAllCompany);

    // Format company suggestion text for autocomplete
    const getAutoSuggestionText = (item) =>
      `${item.internal_company_id} / ${item.name} ${item.business_industry?.name ?? ''}`;

    /**
     * Company Search Handler
     * Debounced function to prevent excessive API calls
     */
    const searchCompany = debounce(function (field) {
      field.is_loading = true;

      store
        .dispatch('COMPANY_GET_ALL', {
          search: field.searched_text ?? null,
          silent_loading: true,
          page: 1,
          paginate: 10,
          showActive: 1,
        })
        .then(() => {
          // Handle company data assignment
          if (Array.isArray(field.items)) {
            field.items = getAllCompany.value;
          } else {
            field.items = [...getAllCompany.value];
          }
          field.is_loading = false;
        })
        .catch((error) => {
          console.error('Error fetching company data:', error);
        });
    }, 500);

    /**
     * Company Field Configuration
     * Settings for company selection autocomplete
     */
    const company = ref({
      label: '企業名', // Company name
      name: 'company_id',
      placeholder: '内部ID、または企業名フリガナを入力してください', // Enter internal ID or company name in katakana
      row_class: '',
      item_value: 'id',
      item_text: getAutoSuggestionText,
      searchable: true,
      search_api: searchCompany,
      is_loading: false,
      searched_text: '',
      items: getAllCompany.value,
      value: null,
      rules: 'required',
      requiredChecks: true,
    });

    /**
     * Form Validation Rules and State
     */
    const totalCompanyUser = ref(0); // Total users in company
    const companyInternalID = ref(''); // Company internal ID
    const requiredRule = ref('required'); // Required field validation
    const emailRule = ref('required|email'); // Email validation
    const passwordRule = ref('required|min:8'); // Password validation (8+ chars)
    const confirmPasswordRule = ref('required|confirmed:@password'); // Password confirmation
    const phoneRule = ref('enter_half_width_numbers_hyphens'); // Phone format validation

    /**
     * Clear placeholder dots when focusing on password field
     */
    const clearPasswordIfPlaceholder = () => {
      if (props.selectedCompanyUser && !form.value.password) {
        form.value.password = '';
      }
    };

    /**
     * Clear placeholder dots when focusing on confirm password field
     */
    const clearConfirmPasswordIfPlaceholder = () => {
      if (props.selectedCompanyUser && !form.value.confirmPassword) {
        form.value.confirmPassword = '';
      }
    };

    /**
     * Dialog State Watcher
     * Handles form initialization and reset
     */
    watch(
      () => props.dialog,
      (visible) => {
        if (visible && props.selectedCompanyUser) {
          // Initialize form with selected user data for editing
          let modifiedCompanyUser = { ...props.selectedCompanyUser };
          modifiedCompanyUser.type_user = JSON.stringify(
            props.selectedCompanyUser?.type_user
          );
          companyInternalID.value =
            props.selectedCompanyUser.company_internal_id;
          totalCompanyUser.value = props.selectedCompanyUser.total_user_counts;

          // Clear password fields on edit to show placeholder dots
          modifiedCompanyUser.password = '';
          modifiedCompanyUser.confirmPassword = '';

          form.value = modifiedCompanyUser;
          company.value.value = props.selectedCompanyUser?.company_id;
          company.value.items = getAllCompany.value;
        } else {
          // Reset form for new user creation
          form.value = {
            type_user: '',
            surname: '',
            name: '',
            email_invalid: '',
            tel: '',
            password: '',
            confirmPassword: '',
          };
          if (
            props.companyUsers?.length === 0 ||
            (props.companyUsers?.length === 1 && props.selectedCompanyUser) ||
            props.selectedCompanyUser?.isFirstUser
          ) {
            form.value.type_user = '1';
          }
        }
      },
      { immediate: true }
    );

    /**
     * Dialog Control
     * Two-way binding for dialog visibility
     */
    const selfDialog = computed({
      get() {
        return props.dialog;
      },
      set() {
        emit('closeModel', true);
      },
    });

    /**
     * Form Submission Handlers
     */
    const submitForm = () => {
      register();
    };

    const { setFieldError } = useForm();
    const errorEmail = ref('');
    /**
     * User Registration/Update Handler
     * Handles both creation and updating of company users
     */
    const register = () => {
      const createOrUpdate = props.selectedCompanyUser
        ? 'COMPANY_USER_UPDATE'
        : 'COMPANY_USER_CREATE';

      form.value.company_id = parseInt(route.params.id);
      form.value.flg_delete = 0;

      if (company.value && route.name === 'CompanyUserListing') {
        form.value.company_id = company.value.value;
      }

      delete form.value.isFirstUser;

      if (props.selectedCompanyUser && !form.value.password) {
        delete form.value.password;
        delete form.value.confirmPassword;
      }

      store
        .dispatch(createOrUpdate, form.value)
        .then(() => {
          emit('closeModel', true);
        })
        .catch((error) => {
          console.log(error);

          if (error?.data?.errors) {
            const errors = error.data.errors;

            // Loop through all error fields and set them in VeeValidate
            Object.keys(errors).forEach((field) => {
              if (field === 'email_invalid') {
                errorEmail.value = errors[field][0];
              }
            });
          }
        });
    };

    /**
     * Utility Functions
     */
    // Copy password to clipboard
    const copyToClipboard = (text) => {
      if (text) {
        navigator.clipboard.writeText(text);
      }
    };

    // Format company item display
    const getItem = (item) => `${item.internal_company_id} / ${item.name}`;

    // Expose necessary properties and methods
    return {
      form,
      company,
      totalCompanyUser,
      companyInternalID,
      requiredRule,
      emailRule,
      passwordRule,
      confirmPasswordRule,
      phoneRule,
      selfDialog,
      submitForm,
      register,
      copyToClipboard,
      getItem,
      getAutoSuggestionText,
      searchCompany,
      setFieldError,
      errorEmail,
      clearPasswordIfPlaceholder,
      clearConfirmPasswordIfPlaceholder,
    };
  },
};
</script>

<style scoped>
.user-type {
  margin-bottom: 16px; /* Space below user type radio buttons */
}

.custom-card {
  padding-top: 16px !important;
  padding-bottom: 32px !important;
}

.register-button {
  background: linear-gradient(
    to bottom,
    #4a90e2,
    #4285f4
  ); /* Blue gradient button */
  width: 100%;
  color: white;
  font-weight: bold;
}

.register-button:hover {
  background: linear-gradient(
    to bottom,
    #357ae8,
    #4285f4
  ); /* Slightly darker on hover */
}

.font-14px {
  font-size: 14px;
}

.font-12px {
  font-size: 12px;
}

.error--text {
  color: red;
}

.mt-1 {
  margin-top: 4px;
}

.ml-2 {
  margin-left: 8px;
}

.v-menu__content.theme--light.menuable__content__active.v-autocomplete__content {
  z-index: 99999999 !important;
}

:deep(.v-text-field .v-input__control > .v-input__slot input) {
  font-size: 14px !important;
}
</style>

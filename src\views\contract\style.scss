.container-div {
  width: 100% !important;
}
.contract-background {
  background-color: white !important;
  max-width: 590px !important;
  min-height: 200px !important;
  border-radius: 12px !important;
  .contract-container {
    width: 100% !important;
    height: 100% !important;
  }
}
.button-width {
  max-width: 259px !important;
  .btn-container {
    width: 100% !important;
  }
}

.v-radio-group {
  display: flex;
  flex-wrap: wrap;
}

/* Add margin to individual radio buttons */
.custom-radio {
  flex: 1 1 auto; /* Grow and shrink as needed */
  margin: 4px; /* Adjust margin for spacing */
}

/* Optional: Prevent mouse events if necessary */
.cancel-mouse-event {
  pointer-events: none;
}

<template>
  <div v-show="!$store.getters.getApiProcessingStatus">
    <Report class="mt-8"></Report>
  </div>
</template>

<script>
import { defineAsyncComponent } from 'vue';
const Report = defineAsyncComponent(() => import('@/views/student/report.vue'));

import config from '@/config';

export default {
  name: 'Error',
  components: {
    Report,
  },
  data() {
    return {
      config,
    };
  },
};
</script>

<style></style>

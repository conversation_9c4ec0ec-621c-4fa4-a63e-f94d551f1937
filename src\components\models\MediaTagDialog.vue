<template>
  <v-dialog v-model="getLaunch" max-width="775px">
    <v-card :elevation="0" color="white" width="775px">
      <v-card-title
        v-if="edit"
        class="d-flex justify-center pt-15 pb-8 font-20px"
        >編集</v-card-title
      >
      <v-card-title v-else class="d-flex justify-center pt-15 pb-8 font-20px"
        >新規登録</v-card-title
      >
      <div class="d-flex flex-column align-center justify-space-between">
        <div>
          <Form @submit="submitForm" :initial-values="fields">
            <div class="d-flex flex-column align-center justify-center">
              <div class="input-width pt-2">
                <label class="d-block font-14px mb-1">
                  <span>タグ名</span>
                </label>
                <Field
                  v-slot="{ field, errors }"
                  name="name"
                  rules="required:タグ名 "
                >
                  <v-text-field
                    v-bind="field"
                    :error-messages="errors"
                    :error="errors.length !== 0"
                    :hide-details="errors.length <= 0"
                    variant="outlined"
                    density="compact"
                    v-model="fields.name"
                    placeholder="入力してください"
                  >
                  </v-text-field>
                </Field>
              </div>
            </div>
            <v-card-actions
              v-if="edit"
              class="d-flex justify-center pt-7 pb-16"
            >
              <v-btn
                variant="outlined"
                color="primary"
                width="188px"
                height="35px"
                @click="getLaunch = false"
                >戻る</v-btn
              >
              <v-btn
                type="submit"
                variant="elevated"
                color="primary"
                class="white--text ml-5"
                width="188px"
                height="35px"
                >保存</v-btn
              >
            </v-card-actions>

            <v-card-actions v-else class="d-flex justify-center pt-7 pb-16">
              <v-btn
                type="submit"
                variant="elevated"
                color="primary"
                width="188px"
                height="35px"
                >登録</v-btn
              >
            </v-card-actions>
          </Form>
        </div>
      </div>
    </v-card>
  </v-dialog>
</template>
<script>
import { ref, computed, watch } from 'vue';
import { Field, Form, useForm } from 'vee-validate';
import { useStore } from 'vuex';

export default {
  name: 'MediaTagDialog',
  components: {
    Field,
    Form,
  },
  props: {
    edit: {
      default: true,
    },
    launch: {
      required: true,
    },
    item: {
      required: false,
      default: null,
    },
  },
  setup(props, { emit }) {
    /**
     * Component Setup
     * Handles media tag creation and editing functionality
     * @param {Object} props - Component props containing dialog state and tag data
     * @param {Function} emit - Event emitter for parent communication
     */
    const store = useStore();

    /**
     * Form State Management
     * Manages media tag form data
     */
    const fields = ref({
      name: null, // Tag name (タグ名)
    });

    /**
     * Form Initialization
     * Sets up form with vee-validate and initial values
     */
    const { resetForm } = useForm({
      initialValues: fields,
    });

    /**
     * Form Data Watcher
     * Handles form initialization and reset based on dialog state
     * Populates form when editing existing tag
     */
    watch(
      () => props.launch,
      (val) => {
        if (val && props.item) {
          // Initialize form with existing tag data
          fields.value.name = props.item.name || null;
        } else {
          // Reset form for new tag entry
          resetForm();
          fields.value = { name: null };
        }
      }
    );

    /**
     * Dialog Control
     * Two-way binding for dialog visibility with parent component
     */
    const getLaunch = computed({
      get() {
        return props.launch; // Get dialog state from parent
      },
      set(value) {
        emit('update:launch', value); // Update parent's dialog state
      },
    });

    /**
     * Form Submission Handler
     * Processes form submission for both create and update operations
     * @param {Object} values - Form field values
     */
    const submitForm = async (values) => {
      if (props.edit) {
        updateMediaTag(values);
        return;
      }
      try {
        // Create new media tag
        await store.dispatch('MEDIA_TAGS_CREATE', values);
        resetForm(); // Clear form
        emit('refresh'); // Notify parent to refresh list
        getLaunch.value = false; // Close dialog
      } catch (error) {
        console.error(error);
      }
    };

    /**
     * Tag Update Handler
     * Updates existing media tag with new values
     * @param {Object} values - Updated form values
     */
    const updateMediaTag = async (values) => {
      try {
        values.id = props.item.id; // Add ID for update operation
        await store.dispatch('MEDIA_TAGS_EDIT', values);
        resetForm(); // Clear form
        emit('refresh'); // Notify parent to refresh list
        getLaunch.value = false; // Close dialog
      } catch (error) {
        console.error(error);
      }
    };

    /**
     * Dialog Action Handler
     * Manages dialog closure and form reset
     */
    const handleModal = () => {
      emit('update:launch', false); // Close dialog
      resetForm(); // Clear form state
    };

    // Expose necessary properties and methods
    return {
      getLaunch, // Dialog visibility control
      submitForm, // Form submission handler
      updateMediaTag, // Tag update handler
      getEducationFacilityType: computed(
        () => store.getters.getEducationFacilityType
      ),
      handleModal, // Dialog close handler
      fields, // Form field values
    };
  },
};
</script>
<style lang="scss" src="@/styles/forms.scss"></style>
<style lang="scss">
.input-width {
  width: 543px;
}
</style>

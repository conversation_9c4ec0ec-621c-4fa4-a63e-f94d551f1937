<template>
  <v-dialog v-model="getLaunch" max-width="775px">
    <v-card elevation="0" color="white" width="775px">
      <v-card-title class="d-flex justify-end pr-9 pb-2 pt-8">
        <v-btn variant="text" icon @click="handleModal">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>

      <!-- Title for Edit / New Registration -->
      <v-card-title
        v-if="edit"
        class="d-flex justify-center pt-0 pb-8 font-20px"
        >編集</v-card-title
      >
      <v-card-title v-else class="d-flex justify-center pt-0 pb-8 font-20px"
        >新規登録</v-card-title
      >

      <div class="d-flex flex-column align-center justify-space-between">
        <Form @submit="submitForm" :initial-values="fields">
          <div class="d-flex flex-column align-center justify-center">
            <div class="input-width">
              <label class="d-block font-14px mb-1">教育機関タイプ</label>
              <Field name="type" rules="required" v-slot="{ field, errors }">
                <v-select
                  v-bind="field"
                  :items="getEducationFacilityType"
                  item-title="name"
                  item-value="id"
                  v-model="fields.type"
                  density="compact"
                  variant="outlined"
                  placeholder="選択してください"
                  :error-messages="errors"
                  :error="errors.length > 0"
                  :hide-details="errors.length <= 0"
                  class="font-14px mt-1"
                />
              </Field>
            </div>

            <div class="input-width pt-2">
              <label class="d-block font-14px mb-1">大学名</label>
              <Field name="name" rules="required" v-slot="{ field, errors }">
                <v-text-field
                  v-bind="field"
                  v-model="fields.name"
                  density="compact"
                  variant="outlined"
                  placeholder="入力してください"
                  :error-messages="errors"
                  :error="errors.length > 0"
                  :hide-details="errors.length <= 0"
                />
              </Field>
            </div>
          </div>

          <!-- Card actions -->
          <v-card-actions class="d-flex justify-center pt-7 pb-16">
            <v-btn
              v-if="edit"
              variant="outlined"
              color="primary"
              width="188px"
              height="35px"
              @click="getLaunch = false"
              >戻る</v-btn
            >
            <v-btn
              type="submit"
              variant="elevated"
              color="primary"
              class="white--text ml-5"
              width="188px"
              height="35px"
            >
              {{ edit ? '保存' : '登録' }}
            </v-btn>
          </v-card-actions>
        </Form>
      </div>
    </v-card>
  </v-dialog>
</template>

<script>
import { ref, computed, reactive, watch } from 'vue';
import { useForm, Field, Form } from 'vee-validate';
import { useStore } from 'vuex';
import { mapGetters } from 'vuex';

export default {
  name: 'SchoolDialog',
  props: {
    edit: {
      type: Boolean,
      default: true,
    },
    launch: {
      type: Boolean,
      required: true,
    },
    item: {
      type: Object,
      default: null,
    },
  },
  setup(props, { emit }) {
    /**
     * Component Setup
     * Handles educational facility (school) creation and editing functionality
     * @param {Object} props - Component props containing dialog state and school data
     * @param {Function} emit - Event emitter for parent communication
     */
    const store = useStore();

    /**
     * Form State Management
     * Manages educational facility form data
     */
    const fields = ref({
      type: null, // Educational facility type (教育機関タイプ)
      name: null, // School name (大学名)
    });

    /**
     * Form Initialization
     * Sets up form with vee-validate and initial values
     */
    const { resetForm } = useForm({
      initialValues: fields,
    });

    /**
     * Form Data Watcher
     * Handles form initialization and reset based on dialog state
     * Populates form when editing existing school
     */
    watch(
      () => props.launch,
      (val) => {
        if (val && props.item) {
          // Initialize form with existing school data
          fields.value.name = props.item.name || null;
          fields.value.type = props.item.type?.id || null;
        } else {
          // Reset form for new school entry
          resetForm();
          fields.value = { name: null, type: null };
        }
      }
    );

    /**
     * Dialog Control
     * Two-way binding for dialog visibility with parent component
     */
    const getLaunch = computed({
      get() {
        return props.launch; // Get dialog state from parent
      },
      set(value) {
        emit('update:launch', value); // Update parent's dialog state
      },
    });

    /**
     * Form Submission Handler
     * Processes form submission for both create and update operations
     * @param {Object} values - Form field values
     */
    const submitForm = async (values) => {
      if (props.edit) {
        updateEducationFacility(values); // Update existing facility
        return;
      }
      try {
        // Create new educational facility
        await store.dispatch('FACILITIES_CREATE', values);
        resetForm(); // Clear form
        emit('refresh'); // Notify parent to refresh list
        getLaunch.value = false; // Close dialog
      } catch (error) {
        console.error(error);
      }
    };

    /**
     * Facility Update Handler
     * Updates existing educational facility with new values
     * @param {Object} values - Updated form values
     */
    const updateEducationFacility = async (values) => {
      try {
        values.id = props.item.id; // Add ID for update operation
        await store.dispatch('FACILITIES_EDIT', values);
        resetForm(); // Clear form
        emit('refresh'); // Notify parent to refresh list
        getLaunch.value = false; // Close dialog
      } catch (error) {
        console.error(error);
      }
    };

    /**
     * Dialog Action Handler
     * Manages dialog closure and form reset
     */
    const handleModal = () => {
      emit('update:launch', false); // Close dialog
      resetForm(); // Clear form state
    };

    return {
      getLaunch,
      submitForm,
      updateEducationFacility,
      getEducationFacilityType: computed(
        () => store.getters.getEducationFacilityType
      ),
      handleModal,
      fields,
    };
  },
};
</script>

<style lang="scss">
.input-width {
  width: 543px;
}

.v-text-field .v-input__details {
  padding-inline: 0 !important;
}
</style>

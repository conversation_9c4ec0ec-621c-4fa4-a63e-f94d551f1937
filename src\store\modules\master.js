import axios from '@/services/api';

const state = {
  master: {},
  counts: {},
  categories: [],
  domains: [],
  bigField: [],
  smallField: [],
};

const getters = {
  getMasterData: (state) => state.master,
  getCountsData: (state) => state.counts,
  getTotalCompanies: (state) => state.counts.total_companies,
  getNotApprovedCompanies: (state) => state.counts.total_companies_not_approved,
  getApprovedCompanies: (state) =>
    state.counts.total_companies - state.counts.total_companies_not_approved,
  getEducationFacilityType: (state) => state.master.educational_facility_type,
  getTotalStudents: (state) => state.counts.total_students,
  getTotalUnreadApplications: (state) => state.counts.total_unread_applications,
  getNewStudentCount: (state) => state.counts.new_student_arrival,
  getCategories: (state) => state.categories,
  getDomains: (state) => state.domains,
  getBigFieldData: (state) => state.bigField,
  getSmallFieldData: (state) => state.smallField,
};

const actions = {
  ['GET_MASTER_DATA']: ({ commit, dispatch }, showLoader = false) => {
    if (showLoader) {
      dispatch('API_PROCESSING', true, { root: true });
    }

    return new Promise((resolve, reject) => {
      axios
        .get('/master')
        .then((response) => {
          commit('GET_MASTER_DATA_SUCCESS', response.data);
          commit('UPDATE_TOTAL_UNREAD_APPLICATIONS', response.data);
          resolve(response);
        })
        .catch((error) => {
          reject(error);
        })
        .finally(() => {
          if (showLoader) {
            dispatch('API_PROCESSING', false, { root: true });
          }
        });
    });
  },

  ['GET_CATEGORIES_DATA']: ({ commit, dispatch }, showLoader = true) => {
    if (showLoader) {
      dispatch('API_PROCESSING', true, { root: true });
    }
    return new Promise((resolve, reject) => {
      axios
        .get('/admin/study-categories')
        .then((response) => {
          commit('GET_CATEGORIES_DATA_SUCCESS', response.data);
          resolve(response);
        })
        .catch((error) => {
          reject(error);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['GET_BIG_FIELD_DATA']: ({ commit, dispatch }, showLoader = false) => {
    if (showLoader) {
      dispatch('API_PROCESSING', true, { root: true });
    }
    return new Promise((resolve, reject) => {
      axios
        .get('/admin/study-big-fields')
        .then((response) => {
          commit('GET_BIG_FIELD_DATA_SUCCESS', response.data);
          resolve(response);
        })
        .catch((error) => {
          reject(error);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['GET_SMALL_FIELD_DATA']: ({ commit, dispatch }, showLoader = false) => {
    if (showLoader) {
      dispatch('API_PROCESSING', true, { root: true });
    }
    return new Promise((resolve, reject) => {
      axios
        .get('/admin/study-small-fields')
        .then((response) => {
          commit('GET_SMALL_FIELD_DATA_SUCCESS', response.data);
          resolve(response);
        })
        .catch((error) => {
          reject(error);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['GET_COUNTS_DATA']: ({ commit, dispatch }, showLoader = false) => {
    if (showLoader) {
      dispatch('API_PROCESSING', true, { root: true });
    }

    return new Promise((resolve, reject) => {
      axios
        .get('/admin-counts')
        .then((response) => {
          commit('GET_COUNTS_DATA_SUCCESS', response.data);
          resolve(response);
        })
        .catch((error) => {
          reject(error);
        })
        .finally(() => {
          if (showLoader) {
            dispatch('API_PROCESSING', false, { root: true });
          }
        });
    });
  },
};

const mutations = {
  ['UPDATE_TOTAL_UNREAD_APPLICATIONS']: (state, payload) => {
    state.master.total_unread_applications =
      payload?.data?.total_unread_applications;
  },
  ['GET_MASTER_DATA_SUCCESS']: (state, payload) => {
    state.master = payload.data;
  },
  ['GET_CATEGORIES_DATA_SUCCESS']: (state, payload) => {
    state.categories = payload.studyBig;
  },
  ['GET_DOMAINS_DATA_SUCCESS']: (state, payload) => {
    state.domains = payload.studyBig;
  },
  ['GET_BIG_FIELD_DATA_SUCCESS']: (state, payload) => {
    state.bigField = payload.studyBig;
  },
  ['GET_SMALL_FIELD_DATA_SUCCESS']: (state, payload) => {
    state.smallField = payload.studyBig;
  },
  ['GET_COUNTS_DATA_SUCCESS']: (state, payload) => {
    state.counts = payload;
  },
};

export default {
  state,
  actions,
  mutations,
  getters,
};

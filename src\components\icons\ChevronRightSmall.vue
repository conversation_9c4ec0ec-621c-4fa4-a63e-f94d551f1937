<template>
  <svg
    width="17"
    height="18"
    viewBox="0 0 17 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_72_7198)">
      <path
        d="M8.5 17.5C13.1871 17.5 17 13.6871 17 9C17 4.31295 13.1871 0.5 8.5 0.5C3.81295 0.5 0 4.31295 0 9C0 13.6871 3.81295 17.5 8.5 17.5ZM6.58255 5.95912C6.30561 5.68218 6.30561 5.2345 6.58255 4.95755C6.72068 4.81943 6.902 4.75 7.08336 4.75C7.26468 4.75 7.44603 4.81943 7.58416 4.95755L11.1258 8.4992C11.4027 8.77614 11.4027 9.22382 11.1258 9.50077L7.58412 13.0424C7.30718 13.3194 6.8595 13.3194 6.58255 13.0424C6.30561 12.7655 6.30561 12.3178 6.58255 12.0409L9.62343 9L6.58255 5.95912Z"
        fill="#13ABA3"
      />
    </g>
    <defs>
      <clipPath id="clip0_72_7198">
        <rect
          width="17"
          height="17"
          fill="white"
          transform="matrix(-1 0 0 -1 17 17.5)"
        />
      </clipPath>
    </defs>
  </svg>
</template>
<script>
export default {
  props: {
    fill: {
      default: 'white',
      required: false,
    },
  },
};
</script>

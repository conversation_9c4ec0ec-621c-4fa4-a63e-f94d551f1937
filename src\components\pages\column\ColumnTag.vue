<template>
  <div>
    <v-card class="mt-5" min-height="200px">
      <div class="ml-9 mr-7">
        <div class="pt-10">
          <div class="font-14px">タグ</div>
          <Field v-slot="{ errors }" name="media_tag_id" rules="">
            <v-autocomplete
              :error-messages="errors"
              :error="errors.length !== 0"
              :hide-details="errors.length <= 0"
              variant="outlined"
              multiple
              dense
              color="#13ABA3"
              class="font-16px mt-1 pb-3"
              :items="allMediaTags"
              item-title="name"
              item-value="id"
              placeholder="選択してください"
              v-model="mediaTag.value"
              autocomplete="new-password"
            >
              <template v-slot:selection="{ attrs, item, select, selected }">
                <v-chip
                  color="#D4F8F6"
                  class="mt-1"
                  v-bind="attrs"
                  :input-value="selected"
                  @click="select"
                >
                  <span>{{ item.title }}</span>
                  <v-avatar size="26" right @click="removeItem(item.id)">
                    <v-icon class="ml-2" size="22">$GreenCross</v-icon>
                  </v-avatar>
                </v-chip>
              </template>
            </v-autocomplete>
          </Field>
        </div>
      </div>
    </v-card>
  </div>
</template>

<script>
import { computed } from 'vue';
import { useStore } from 'vuex';
import { Field } from 'vee-validate';

export default {
  name: 'ColumnTag',
  props: {
    mediaTag: {
      required: true,
    },
  },
  components: {
    Field,
  },
  setup(props) {
    const store = useStore();

    const allMediaTags = computed(() => store.getters.getAllMediaTags);

    const removeItem = (id) => {
      props.mediaTag.value = props.mediaTag.value.filter((item) => item !== id);
    };

    const getDataFromApi = async () => {
      await store.dispatch('MEDIA_TAGS_GET_ALL', {
        sort_by: 'created_at',
        sort_by_order: 'desc',
        paginate: 100,
        page: 1,
      });
    };

    getDataFromApi();

    return {
      allMediaTags,
      removeItem,
    };
  },
};
</script>

<style lang="scss" scoped>
.v-input__append-inner {
  display: none !important;
}
</style>

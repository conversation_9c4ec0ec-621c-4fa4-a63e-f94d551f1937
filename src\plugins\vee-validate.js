import { defineRule, configure } from 'vee-validate';
import { localize } from '@vee-validate/i18n';
import createI18nInstance from '@/plugins/i18n'; // Import the i18n async function
const i18n = createI18nInstance();
import {
  required,
  email,
  max,
  min,
  size,
  max_value,
  confirmed,
  integer,
  regex,
  ext,
} from '@vee-validate/rules';

// Define validation rules
defineRule('required', required);
defineRule('email', email);
defineRule('max', max);
defineRule('min', min);
defineRule('size', size);
defineRule('max_value', max_value);
defineRule('confirmed', confirmed);
defineRule('integer', integer);
defineRule('regex', regex);
defineRule('ext', ext);

const maxDimensionsRule = (value, [width, height]) => {
  const validateImage = (file, maxWidth, maxHeight) => {
    const URL = window.URL || window.webkitURL;
    return new Promise((resolve) => {
      const image = new Image();
      image.onerror = () => resolve(false); // Invalid if image can't be loaded
      image.onload = () => {
        resolve(
          image.width <= Number(maxWidth) && image.height <= Number(maxHeight)
        );
      };
      image.src = URL.createObjectURL(file);
    });
  };

  // If no files are provided, it's valid
  if (!value || value.length === 0) {
    return true;
  }

  const fileList = Array.isArray(value) ? value : [value]; // Handle single or multiple files

  // Validate each file
  const validations = fileList.map((file) => {
    if (!/\.(jpg|svg|jpeg|png|bmp|gif)$/i.test(file.name)) {
      return false; // Reject non-image files
    }
    return validateImage(file, width, height);
  });

  // Wait for all validations to resolve
  return Promise.all(validations).then((results) =>
    results.every((isValid) => isValid)
  );
};

// Register the rule
defineRule('maxDimensions', maxDimensionsRule);

// Define custom rules
defineRule('url', (value) => {
  // Check if the value is a string
  if (typeof value !== 'string' || !value) {
    return true; // Skip validation for non-string values
  }

  // URL validation regex
  const urlRegex =
    /^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+\/?$/;
  // Return true if valid, or an error message string if invalid
  return urlRegex.test(value)
    ? true
    : 'ホームページURLは有効なURLでなければなりません';
});

defineRule('verify_password', (value) => {
  const strongRegex = new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9]).{7,}$');
  return strongRegex.test(value);
});

defineRule('only_english_lang_allowed', (value) => {
  // Validation to allow only English characters
  const strongRegex = new RegExp(`[^\u0000-\u0080]+`);

  // If value is empty, we don't want to fail the validation
  if (!value || value.trim() === '') {
    return true;
  }

  // Return true if validation passes, otherwise return the error message
  return !strongRegex.test(value) || 'Only English characters are allowed';
});

defineRule(
  'slug',
  (value) => /^[a-z0-9-]+$/i.test(value) || 'The slug must be valid.'
);

// Full-width Katakana validation
defineRule('full_width_katakana', (value) => {
  // Validation to allow full-width Katakana characters
  const jaRegex = /^[\u30A0-\u30FF]*$/;

  // If value is empty, we don't want to fail the validation
  if (!value || value.trim() === '') {
    return true;
  }

  // Return true if validation passes, otherwise return the error message
  return jaRegex.test(value) || '全角カタカナで入力してください。';
});

defineRule('error_slack', (value) => {
  if (required(value)) {
    return true; // Validation passed
  }
  return 'Slack通知をONにする際は、Webhook URLを入力してください。'; // Error message
});

// Half-width numbers and hyphens validation
defineRule('enter_half_width_numbers_hyphens', (value) => {
  // If value is empty, we don't want to fail the validation
  if (!value || value.trim() === '') {
    return true;
  }

  // Regular expressions for phone number validation
  const regexElevenPhoneHyphen =
    /^\(?\d{3}\)?[-]?(\d{4})[-]?(\d{4})$|^\(?\d{3}\)?[-]?(\d{3})[-]?(\d{4})$|^\(?\d{2}\)?[-]?(\d{4})[-]?(\d{4})$|^\(?\d{4}\)?[-]?(\d{2})[-]?(\d{4})$|^\(?\d{4}\)?[-]?(\d{3})[-]?(\d{3})$|^\(?\d{5}\)?[-]?(\d{1})[-]?(\d{4})$/;
  const regexTenPhoneHyphen = /^(1-)?\d{3}-\d{3}-\d{4}$/;

  // Return true if either regex matches, otherwise return the error message
  return (
    regexElevenPhoneHyphen.test(value) ||
    regexTenPhoneHyphen.test(value) ||
    '半角の数字とハイフンを使用し入力してください'
  );
});

defineRule('min_value', (value, [limit]) => {
  if (!value || isNaN(value)) return '無効な数値です';
  return Number(value) >= limit
    ? true
    : `値は少なくとも${limit}以上である必要があります`;
});

// i18n configuration for validation messages (translations will be added in main.js)
configure({
  generateMessage: localize({}),
  validateOnInput: true, // Validate as you type
});

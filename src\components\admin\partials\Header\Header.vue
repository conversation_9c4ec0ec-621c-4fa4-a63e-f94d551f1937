<template>
  <v-app-bar class="main-header" height="64" fixed color="whie">
    <v-btn icon class="mx-1" @click.stop="toggleDrawer">
      <v-icon size="large" color="#fff">mdi-menu</v-icon>
    </v-btn>
    <v-icon color="green-darken-2" icon="$Logo" size="60"></v-icon>
    <v-spacer></v-spacer>

    <v-chip
      class=""
      color="white"
      variant="flat"
      v-if="user"
      style="margin-right: 16px"
    >
      {{ user.name }} さん
    </v-chip>
  </v-app-bar>
</template>

<script>
import { computed, ref, onMounted, watch } from 'vue';
import { useStore } from 'vuex';

export default {
  name: 'Header',
  setup() {
    const store = useStore();
    const user = ref(null);

    // Fetch the user data on mount
    onMounted(() => {
      user.value = store.state.auth.user;
    });

    // Watch for changes in the user state
    watch(
      () => store.state.auth.user,
      (newUser) => {
        user.value = newUser;
      }
    );

    // Computed property for drawer state
    const drawerState = computed({
      get: () => store.state.drawer,
      set: (newValue) => {
        if (newValue !== store.state.drawer) {
          toggleDrawer();
        }
      },
    });

    // Method to toggle the drawer
    const toggleDrawer = () => {
      store.dispatch('TOGGLE_DRAWER');
    };

    return {
      user,
      drawerState,
      toggleDrawer,
    };
  },
};
</script>

<style src="./Header.scss" lang="scss"></style>

<template>
  <div>
    <PageTitle
      :items="{
        title: 'コラム',
        subTitle: '新規作成',
        back: {
          action: () => router.push({ name: 'Media' }),
        },
      }"
    />

    <v-form @submit.prevent="submitMediaPost()">
      <v-row>
        <v-col cols="8">
          <ColumnInformation
            :sectionTitle="false"
            :basicInformation="columnInformation"
          />
        </v-col>
        <v-col cols="4">
          <v-card height="264px" class="text-center pt-14 pa-4">
            <div class="button-width mx-auto">
              <div :class="{ 'px-2': smAndDown }" class="btn-container">
                <v-btn
                  @click="openDraftPopup"
                  variant="outlined"
                  color="#13ABA3"
                  min-width="150px"
                  width="259px"
                >
                  下書き保存
                </v-btn>
                <v-btn
                  @click="submitMediaPost('P')"
                  variant="outlined"
                  class="mt-6"
                  color="#13ABA3"
                  min-width="150px"
                  width="259px"
                >
                  プレビュー
                </v-btn>
                <v-btn
                  @click="submitMediaPost()"
                  color="#13ABA3"
                  class="white--text mt-6"
                  min-width="150px"
                  width="259px"
                >
                  公開
                </v-btn>
              </div>
            </div>
          </v-card>
          <ImageUpload :data="imageDetails" />
          <ColumnTag :mediaTag="mediaTags" />
        </v-col>
      </v-row>
    </v-form>

    <SimpleModel
      text="このコラムを下書きとして保存しますか？"
      :dialog="dialog.saveAsDraft"
      @submitSuccess="saveAsDraft"
      @closeModel="() => (dialog.saveAsDraft = false)"
    />
  </div>
</template>

<script>
import { computed, onMounted, nextTick, defineAsyncComponent } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
const ColumnInformation = defineAsyncComponent(
  () => import('@/components/pages/PostInputs.vue')
);
const ImageUpload = defineAsyncComponent(
  () => import('@/components/ui/ImageUpload.vue')
);
const ColumnTag = defineAsyncComponent(
  () => import('@/components/pages/column/ColumnTag.vue')
);
const SimpleModel = defineAsyncComponent(
  () => import('@/components/models/SimpleModel.vue')
);
import { useDisplay } from 'vuetify';
import MediaMixin from './media.mixin';

export default {
  name: 'ColumnCreate',
  components: {
    ColumnInformation,
    ColumnTag,
    ImageUpload,
    SimpleModel,
  },
  setup() {
    const {
      imageDetails,
      mediaTags,
      columnInformation,
      isDraftOrPublic,
      dialog,
      isSaved,
      openDraftPopup,
      saveAsDraft,
      savePageData,
      saveAction,
      getPageFields,
    } = MediaMixin();

    const { mdAndUp, smAndDown } = useDisplay();

    const store = useStore();
    const router = useRouter();

    /**
     * Computed URL for preview functionality
     */
    const getPreviewPageURL = computed(() => store.getters.getPreviewPageURL);

    /**
     * Handles post submission with different modes
     * @param {string} isDraftCheck - 'N' for publish, 'D' for draft, 'P' for preview
     */
    const submitMediaPost = async (isDraftCheck = 'N') => {
      let action = saveAction;

      if (isDraftCheck === 'N') {
        isDraftOrPublic.value = '0'; // Public post
      } else if (isDraftCheck === 'P') {
        action = (result) => {
          isSaved.value = result.data.data.data.id;
          store.commit('UPDATE_PREVIEW_URL', {
            preview_url: `post-detail/${result.data.data.data.seo_slug}?preview=true`,
          });
          window.open(getPreviewPageURL.value, '_blank');
        };
      }

      await nextTick(() => {
        savePageData('create', action); // Calling mixin function
        dialog.saveAsDraft = false;
      });
    };

    /**
     * Component initialization
     */
    onMounted(() => {
      getPageFields();
    });

    return {
      dialog,
      columnInformation,
      imageDetails,
      mediaTags,
      isDraftOrPublic,
      isSaved,
      getPreviewPageURL,
      submitMediaPost,
      openDraftPopup,
      saveAsDraft,
      router,
      mdAndUp,
      smAndDown,
      savePageData,
      saveAction,
      getPageFields,
    };
  },
};
</script>

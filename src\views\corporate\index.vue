<template>
  <div
    v-show="!loading && !$store.getters.getApiProcessingStatus"
    class="font-Noto-Sans corporate-page"
  >
    <v-sheet color="transparent">
      <PageTitle
        :items="{
          title: '企業管理',
          subTitle: '一覧',
          tabs: [
            {
              title: 'アクティブ',
              count: getApprovedCompanies,
              action: showActiveTables,
            },
            {
              title: 'インアクティブ',
              count: getNotApprovedCompanies,
              action: showNotActiveTables,
            },
          ],
          buttons: [
            {
              title: 'CSVエクスポート',
              class: 'bg-white text-ff862f',
              color: 'text-ff862f',
              variant: 'outlined',
              action: () => downloadCsv(),
            },
            {
              title: '新規作成',
              icon: 'mdi-plus-circle',
              action: () => {
                $router.push({
                  name: 'CorporateCreate',
                });
              },
            },
          ],
        }"
      ></PageTitle>
      <SearchBox @search-table="searchTable"></SearchBox>
      <v-row>
        <v-col cols="12" md="12" class="d-flex w-100">
          <DataTable
            :items="initialLoad ? [] : getAllCorporate"
            :headers="headers"
            :total-records="totalRecords"
            :number-of-pages="totalPages"
            @update:options="updateTable"
            :loading="loading"
            ref="pagination"
            @row-clicked="
              $router.push({
                name: 'CorporateDetails',
                params: { id: $event.id },
              })
            "
          >
            <template v-slot:[`item.id`]="{ item }">
              <span class="d-block text-center">
                {{ item.id }}
              </span>
            </template>
            <template v-slot:[`item.name`]="{ item }">
              <span>{{ item.name }}</span>
            </template>
            <template v-slot:item.created_at="{ item }">
              <span>{{ dateFormat(item.created_at) }}</span>
            </template>
            <template v-slot:item.business_industry="{ item }">
              <span>{{
                item.business_industry ? item.business_industry.name : ''
              }}</span>
            </template>
          </DataTable>
        </v-col>
      </v-row>
    </v-sheet>
    <SimpleModel
      :text="errorMessages"
      :dialog="dialog.errorDialog"
      :showCloseIcon="true"
      @closeModel="dialog.errorDialog = false"
      :buttonOption="{
        hideCancel: true,
        hideSubmit: true,
      }"
    ></SimpleModel>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, defineAsyncComponent } from 'vue';
import { useStore } from 'vuex';
const DataTable = defineAsyncComponent(
  () => import('@/components/ui/DataTable.vue')
);
const SearchBox = defineAsyncComponent(
  () => import('@/components/ui/SearchBox.vue')
);
const SimpleModel = defineAsyncComponent(
  () => import('@/components/models/SimpleModel.vue')
);
import moment from 'moment';
import Encoding from 'encoding-japanese';

export default {
  name: 'CorporateList',
  components: { DataTable, SearchBox, SimpleModel },
  metaInfo: {
    title: 'コトナル 管理者 企業管理 | 企業一覧',
  },

  setup() {
    const store = useStore();
    const dateFormat = (date) => {
      return date ? moment(date).format('YYYY/MM/DD') : '---- / - / -';
    };
    const showActive = ref(1);
    const errorMessages = ref(null);
    const selectedItem = ref(null);
    const loading = ref(false);
    const dialog = reactive({
      errorDialog: false,
    });
    const headers = ref([
      {
        title: 'ID',
        align: 'center',
        sortable: true,
        value: 'id',
      },
      {
        title: '企業ID',
        align: 'left',
        sortable: false,
        value: 'random_number_id',
      },
      {
        title: '内部ID',
        align: 'left',
        sortable: false,
        value: 'internal_company_id',
      },
      {
        title: '企業名',
        align: 'left',
        sortable: false,
        value: 'name',
      },
      {
        title: '企業名カナ',
        align: 'left',
        sortable: false,
        value: 'furigana_name',
      },
      {
        title: '業界',
        value: 'business_industry',
      },
      {
        title: '企業ユーザ数',
        value: 'no_of_company_users',
      },
      {
        title: '公開求人広告数',
        value: 'no_of_published_internship_posts',
      },
      {
        title: 'インターン生数',
        value: 'no_of_intern_ship_students',
      },
      {
        title: '登録日',
        width: '180px',
        sortable: true,
        value: 'created_at',
      },
    ]);
    /**
     * Flags for initial data loading and search state
     */
    const initialLoad = ref(true);
    const userSearchedInput = ref({});

    /**
     * Computed properties for accessing Vuex state
     * These maintain reactive connections to the store
     */
    const getAllCorporate = computed(() => store.getters.getAllCorporate);
    const getCorporatePagination = computed(
      () => store.getters.getCorporatePagination
    );
    const getNotApprovedCompanies = computed(
      () => store.getters.getNotApprovedCompanies
    );
    const getApprovedCompanies = computed(
      () => store.getters.getApprovedCompanies
    );
    const getCorporateCsvData = computed(
      () => store.getters.getCorporateCsvData
    );

    /**
     * Pagination-related computed properties
     */
    const totalRecords = computed(
      () => getCorporatePagination.value?.records_total
    );
    const totalPages = computed(
      () => getCorporatePagination.value?.total_pages
    );
    const pagination = ref(true);

    /**
     * Resets pagination to first page and triggers update
     */
    const resetPagination = () => {
      if (pagination) {
        pagination.currentPage = 1;
        pagination.updatePaginate++;
      }
    };

    /**
     * Shows active companies table (status = 1)
     * Resets pagination and refreshes data
     */
    const showActiveTables = () => {
      showActive.value = 1;
      resetPagination();
      getDataFromApi();
    };

    /**
     * Shows inactive companies table (status = 0)
     * Resets pagination and refreshes data
     */
    const showNotActiveTables = () => {
      showActive.value = 0;
      resetPagination();
      getDataFromApi();
    };

    /**
     * Downloads corporate data as CSV
     * Shows error dialog if no data available
     */
    const downloadCsv = async () => {
      await store.dispatch('CORPORATE_EXPORT_CSV');

      // Check if data exists
      if (store.getters.getCorporateCsvData?.message) {
        dialog.value.errorDialog = true;
        errorMessages.value =
          '<div class="pt-10">データが見つかりません。</div>'; // No data found
        return;
      }

      // Get CSV data as a UTF-8 string
      const csvData = store.getters.getCorporateCsvData.data.csv;

      // Convert UTF-8 CSV to Shift-JIS
      const sjisArray = Encoding.convert(Encoding.stringToCode(csvData), {
        to: 'SJIS',
        from: 'UNICODE',
      });

      const uint8Array = new Uint8Array(sjisArray);
      const blob = new Blob([uint8Array], { type: 'text/csv' });

      // Create and trigger file download
      const fileUrl = window.URL.createObjectURL(blob);
      const fileLink = document.createElement('a');
      fileLink.href = fileUrl;
      fileLink.setAttribute(
        'download',
        `企業管理_${new Date().toISOString().slice(0, 10)}.csv`
      );
      document.body.appendChild(fileLink);
      fileLink.click();
      document.body.removeChild(fileLink);
    };

    /**
     * Handles search input and triggers API call
     * @param {string} search - Search query string
     */
    const searchTable = (search) => {
      userSearchedInput.value = search || '';
      getDataFromApi(undefined);
    };

    /**
     * Fetches corporate data from API with sorting and pagination
     * @param {Object} e - Table options (sorting, pagination)
     */
    const getDataFromApi = async (e = undefined) => {
      loading.value = true;
      // Prepare request parameters

      const data = {
        sort_by_order: e?.sortDesc?.[0] ? 'asc' : 'desc',
        sort_by: e?.sortBy?.[0] ?? 'created_at',
        page: typeof e === 'number' ? e : (e?.page ?? 1),
        paginate: e?.itemsPerPage ?? 25,
        showActive: showActive?.value ?? false,
      };
      // Add search parameter if exists
      if (typeof userSearchedInput.value === 'string') {
        data.search = userSearchedInput.value;
      }
      // Fetch data and handle loading states
      await store
        .dispatch('CORPORATE_GET_ALL', data)
        .then(() => {
          if (initialLoad.value) initialLoad.value = false;
        })
        .finally(() => (loading.value = false));
      initialLoad.value = false;
    };

    /**
     * Updates table data when options change (sorting, pagination)
     * @param {Object} e - Table options
     */
    const updateTable = (e) => {
      if (!initialLoad.value) getDataFromApi(e);
    };

    /**
     * Initialize component with data
     */
    // Fetch data when the component is mounted
    onMounted(async () => {
      await getDataFromApi();
    });

    return {
      dialog,
      selectedItem,
      headers,
      initialLoad,
      userSearchedInput,
      showNotActiveTables,
      showActiveTables,
      showActive,
      totalRecords,
      totalPages,
      pagination,
      errorMessages,
      getCorporatePagination,
      getNotApprovedCompanies,
      getApprovedCompanies,
      getAllCorporate,
      getCorporateCsvData,
      showActive,
      dateFormat,
      resetPagination,
      updateTable,
      getDataFromApi,
      downloadCsv,
      searchTable,
      loading,
    };
  },
};
</script>
<style src="./style.scss" lang="scss" />

<template>
  <!-- ApplicationRejectModel Dialog -->
  <v-dialog
    v-model="selfDialog"
    max-width="775"
    height="800"
    content-class="no-shadow"
  >
    <template v-slot:default>
      <v-card class="pa-15">
        <v-card-text class="pb-5 text-1f2020">
          <template v-if="Array.isArray(text)">
            <div
              v-for="(tt, index) in text"
              :key="index"
              class="my-1 text-center d-flex justify-center align-center text-333"
              :class="[index == 0 ? 'font-20px mb-7' : 'font-14px']"
              v-html="tt"
            ></div>
          </template>
          <template v-else>
            <div class="d-flex justify-space-between align-center">
              <div></div>
              <div
                class="my-1 d-flex justify-center align-center text-333 font-24px font-weight-medium"
                v-html="text"
              ></div>
              <v-icon @click="submit()" size="18px">$close</v-icon>
            </div>
          </template>
          <Detail
            v-if="application && application.id"
            :application="application"
          />
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn
            v-if="!buttonOption.hideSubmit"
            class="white--text"
            color="primary"
            variant="elevated"
            elevation="4"
            min-width="188"
            @click.prevent="submit"
            :loading="loading"
          >
            {{ submitButtonText }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </template>
  </v-dialog>
  <!-- ApplicationRejectModel Dialog end -->
</template>

<script>
import { ref, watch } from 'vue';
import Detail from '@/views/application/detail.vue';

export default {
  components: {
    Detail,
  },
  name: 'ApplicationDetailDialog',
  props: {
    dialog: {
      type: Boolean,
      default: false,
      required: true,
    },
    text: {
      type: [String, Array],
      default: '',
      required: true,
    },
    submitButtonText: {
      type: String,
      default: 'とじる',
      required: false,
    },
    loading: {
      type: Boolean,
      default: false,
      required: false,
    },
    buttonOption: {
      type: [Object],
      default: () => {
        return {
          hideSubmit: false,
          hideCancel: true,
        };
      },
      required: false,
    },
    application: {
      type: Object,
      default: () => {},
      required: true,
    },
  },
  setup(props, { emit }) {
    const reason = ref(null);
    const selfDialog = ref(props.dialog);

    // Watch for changes in the dialog prop to update selfDialog
    watch(
      () => props.dialog,
      (newVal) => {
        selfDialog.value = newVal;
      }
    );

    const closeDialog = () => {
      selfDialog.value = false;
      emit('closeModel', true);
    };

    const submit = () => {
      emit('submitSuccess', reason.value);
    };

    return {
      reason,
      selfDialog,
      closeDialog,
      submit,
    };
  },
};
</script>
<style lang="css" scope>
.modal-submit {
  box-shadow: 0px 4px 4px rgb(0 0 0 / 25%);
  border-radius: 3px;
}
</style>

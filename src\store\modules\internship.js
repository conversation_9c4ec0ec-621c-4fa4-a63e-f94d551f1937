import axios from '@/services/api';

function initialState() {
  return {
    status: '',
    internship: [],
    singleInternship: {},
    internshipPagination: {},
    internshipCounts: {},
    previewPageURL: {},
    studentApplicationDetailsData: {},
  };
}

const state = initialState();

const getters = {
  getAllInternship: (state) => state.internship,
  getSingleInternship: (state) => state.singleInternship,
  getInternshipPagination: (state) => state.internshipPagination,
  getInternshipCounts: (state) => state.internshipCounts,
  getPreviewPageURL: (state) => state.previewPageURL,
  getStudentApplicationDetailsData: (state) =>
    state.studentApplicationDetailsData,
};

const actions = {
  ['INTERNSHIP_GET_ALL']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      axios
        .get(`/admin/internship-post`, { params })
        .then((response) => {
          commit('INTERNSHIP_GET_ALL_SUCCESS', response.data);
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['INTERNSHIP_GET']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      axios
        .get(`/admin/internship-post/${params.id}`)
        .then((response) => {
          commit('INTERNSHIP_GET_SUCCESS', response.data);

          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['INTERNSHIP_CREATE']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      axios
        .post(`/admin/internship-post`, params)
        .then((response) => {
          commit('INTERNSHIP_CREATE_SUCCESS', response.data);
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['INTERNSHIP_UPDATE']: ({ commit, dispatch }, params) => {
    dispatch('API_PROCESSING', true, { root: true });
    return new Promise((resolve, reject) => {
      axios
        .post(`/admin/internship-post-update/${params.get('id')}`, params)
        .then((response) => {
          commit('INTERNSHIP_UPDATE_SUCCESS', response.data);

          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['INTERNSHIP_DELETE']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      if (!params?.id) {
        return;
      }

      dispatch('API_PROCESSING', true, { root: true });
      commit('USERS_REQUEST');
      axios
        .delete(`/admin/internship-post/${params.id}`)
        .then((response) => {
          let data = response.data.data;
          commit('INTERNSHIP_DELETE_SUCCESS', { data });
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },
  ['GET_STUDENT_APPLICATION_DETAILS']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      if (!params?.studentID || !params?.applicationID) {
        return;
      }

      dispatch('API_PROCESSING_GET_DETAIL_APP', true, { root: true });

      axios
        .get(
          `/admin/student-application-details/${params.studentID}/${params.applicationID}`
        )
        .then((response) => {
          const { data } = response.data;
          commit('GET_STUDENT_APPLICATION_DETAILS_SUCCESS', data);
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING_GET_DETAIL_APP', false, { root: true });
        });
    });
  },
};

const mutations = {
  ['INTERNSHIP_GET_ALL_SUCCESS']: (state, payload) => {
    state.status = 'success';
    state.internship = payload.data.data;
    state.internshipPagination = payload.data.paginate;
    state.internshipCounts = payload.data.counts;
  },

  ['INTERNSHIP_GET_SUCCESS']: (state, payload) => {
    state.status = 'success';
    state.singleInternship = payload.data.data;
    state.previewPageURL = getPreviewURL(state.singleInternship);
  },

  ['INTERNSHIP_CREATE_SUCCESS']: (state, payload) => {
    state.status = 'success';
    state.singleInternship = payload.data.data;
    state.previewPageURL = getPreviewURL(state.singleInternship);
  },

  ['INTERNSHIP_UPDATE_SUCCESS']: (state, payload) => {
    state.status = 'success';
    state.singleInternship = payload.data.data;
    state.previewPageURL = getPreviewURL(state.singleInternship);
  },

  ['INTERNSHIP_DELETE_SUCCESS']: (state) => {
    state.status = 'success';
  },
  ['UPDATE_PREVIEW_URL']: (state, payload) => {
    state.previewPageURL = getPreviewURL(payload);
  },
  ['GET_STUDENT_APPLICATION_DETAILS_SUCCESS']: (state, payload) => {
    state.studentApplicationDetailsData = payload;
    state.status = 'success';
  },
};

function getPreviewURL(inter) {
  return `${import.meta.env.VITE_APP_USER_SITE_URL}/${inter.preview_url}`;
}

export default {
  state,
  getters,
  actions,
  mutations,
};

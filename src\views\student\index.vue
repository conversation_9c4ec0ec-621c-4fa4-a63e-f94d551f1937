<template>
  <div class="">
    <PageTitle :items="titleItems" />

    <SearchStudent @search-table="searchTable" :initial="search" class="mb-6" />
    <DataTable
      class="school-table"
      :headers="headers"
      :items="students"
      :total-records="totalRecords"
      :number-of-pages="totalPages"
      :loading="loading"
      :page="currentPage"
      @row-clicked="handleRowClick"
      @update:options="updateTable"
    >
      <!-- Unread indicator -->
      <template #[`item.is_admin_read`]="{ item }">
        <v-sheet
          v-if="item.is_admin_read === 0"
          width="6px"
          height="6px"
          class="rounded-circle bg-e14d56"
        />
        {{ item.is_admin_read !== 0 ? '' : '' }}
      </template>

      <!-- Student ID -->
      <template #[`item.student_internal_id`]="{ item }">
        <span class="d-block">
          {{ item.student_internal_id }}
        </span>
      </template>

      <!-- Full Name -->
      <template #[`item.first_name`]="{ item }">
        <span>{{ item.family_name }} {{ item.first_name }}</span>
      </template>

      <!-- School Name -->
      <template #[`item.education_facility_name`]="{ item }">
        <span>{{ item?.education_facility?.name }}</span>
      </template>

      <!-- Furigana Name -->
      <template #[`item.family_name_furigana`]="{ item }">
        <span
          >{{ item.family_name_furigana }} {{ item.first_name_furigana }}</span
        >
      </template>

      <!-- Graduate Year/Month -->
      <template #[`item.graduate_year`]="{ item }">
        <span>{{ item.graduate_year }}/{{ item.graduate_month }}</span>
      </template>

      <!-- Created Date -->
      <template #[`item.created_at`]="{ item }">
        <span>{{ formatDate(item.created_at) }}</span>
      </template>
    </DataTable>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, defineAsyncComponent } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
const SearchStudent = defineAsyncComponent(
  () => import('@/components/ui/SearchStudent.vue')
);
const DataTable = defineAsyncComponent(
  () => import('@/components/ui/DataTable.vue')
);
import moment from 'moment';

// Core setup
const store = useStore();
const router = useRouter();
const route = useRoute();

// Reactive state
const loading = ref(false);
const search = ref(route.query.search ?? null);
const initialLoad = ref(true);
const timeout = ref(null);
const sortBy = ref('created_at');
const sortOrder = ref('desc');

// Table headers definition
const headers = ref([
  {
    text: '',
    value: 'is_admin_read',
    width: '2.41%',
    align: 'left',
    class: ['pa-0'],
    sortable: false,
  },
  {
    title: 'ID',
    value: 'student_internal_id',
    width: '8.23%',
    align: 'left',
    class: ['pa-0'],
    sortable: false,
  },
  {
    title: '学生名',
    width: '12.9%',
    value: 'first_name',
    class: ['pa-0'],
    align: 'left',
    sortable: false,
  },
  {
    title: 'フリガナ',
    width: '15.8%',
    value: 'family_name_furigana',
    class: ['pa-0'],
    align: 'left',
    sortable: false,
  },
  {
    title: '大学名',
    value: 'education_facility_name',
    width: '29.26%',
    align: 'left',
    class: ['pa-0'],
    sortable: false,
  },
  {
    title: '卒業予定',
    width: '15.3%',
    value: 'graduate_year',
    align: 'left',
    class: ['pa-0'],
    sortable: false,
  },
  {
    title: '登録日',
    width: '10.1%',
    value: 'created_at',
    align: 'left',
    class: ['pa-0'],
    sortable: true,
  },
]);

// Computed properties
/**
 * Vuex Store Getters
 * Access to global state for student data
 */
const students = computed(() => store.getters.getAllStudent); // List of all students
const pagination = computed(() => store.getters.getStudentPagination); // Pagination information
const csvData = computed(() => store.getters.getStudentCsvData); // CSV export data
const studentCounts = computed(() => store.getters.getStudentCounts); // Student status counts

/**
 * Pagination Computed Properties
 * Handles table pagination state
 */
const totalRecords = computed(() => pagination.value?.records_total || 0);
const totalPages = computed(() => pagination.value?.total_pages || 0);
const currentPage = computed(() => Number(route.query.page) || 1);

/**
 * Page Title Configuration
 * Defines header content, tabs, and action buttons
 */
const titleItems = computed(() => ({
  title: '学生', // Students
  subTitle: '学生一覧', // Student List
  tabs: [
    {
      title: 'アクティブ', // Active
      count: studentCounts.value?.active || 0,
      action: () => handleStatusChange(1),
      selected: Number(route.query.status) === 1,
    },
    {
      title: 'インアクティブ', // Inactive
      count: studentCounts.value?.inactive || 0,
      action: () => handleStatusChange(2),
      selected: Number(route.query.status) === 2,
    },
    {
      title: '退会済', // Withdrawn
      count: studentCounts.value?.cancelled || 0,
      action: () => handleStatusChange(3),
      selected: Number(route.query.status) === 3,
    },
  ],
  buttons: [
    {
      title: 'CSVエクスポート', // CSV Export
      action: downloadCsv,
      color: 'text-ff862f',
      variant: 'outlined',
      class: 'bg-white text-ff862f',
    },
  ],
}));

/**
 * Date Formatting Utility
 * @param {string} date - Date string to format
 * @returns {string} Formatted date in YYYY/MM/DD or placeholder
 */
const formatDate = (date) => {
  return date ? moment(date).format('YYYY/MM/DD') : '----/--/--';
};

/**
 * Status Change Handler
 * Updates student list based on selected status tab
 * @param {number} status - Status code (1: Active, 2: Inactive, 3: Withdrawn)
 */
const handleStatusChange = async (status) => {
  await updateRouteAndFetch({
    page: 1,
    status,
  });
};

/**
 * Row Click Handler
 * Navigates to student profile page
 * @param {Object} item - Student data object
 */
const handleRowClick = (item) => {
  router.push({
    name: 'StudentProfile',
    params: { id: item.id },
  });
};

/**
 * CSV Download Handler
 * Generates and downloads student data in CSV format
 */
const downloadCsv = async () => {
  try {
    await store.dispatch('STUDENT_CSV');
    const blob = new Blob([csvData.value.data.csv], {
      type: 'text/plain;charset=UTF-8',
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `学生情報_${moment().format('YYYYMMDD')}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('CSV download failed:', error);
  }
};

/**
 * Search Handler
 * Updates table with search results
 * @param {string} searchTerm - Search query
 */
const searchTable = (searchTerm) => {
  updateRouteAndFetch({
    search: searchTerm || '',
    page: 1,
  });
};

/**
 * Table Update Handler
 * Manages sorting and pagination changes
 * @param {Object} options - Table options including sort and page
 */
const updateTable = (options) => {
  if (!initialLoad.value) {
    const newSort = options.sortBy?.[0];
    updateRouteAndFetch({
      sort_by: newSort?.key || 'created_at',
      sort_by_order: newSort?.order || 'desc',
      page: typeof options === 'number' ? options : (options?.page ?? 1),
      search: search.value,
    });
  }
};

/**
 * Route Update and Data Fetch Handler
 * Debounced function to update URL and fetch new data
 * @param {Object} params - Query parameters to update
 */
const updateRouteAndFetch = async (params = {}) => {
  if (timeout.value) {
    clearTimeout(timeout.value);
  }

  timeout.value = setTimeout(async () => {
    const updatedQuery = {
      ...route.query,
      ...params,
    };

    await router.replace({
      name: route.name,
      query: updatedQuery,
    });

    initialLoad.value = true;
    await fetchData(updatedQuery);
  }, 100);
};

/**
 * Data Fetching Function
 * Retrieves student data based on current filters and pagination
 * @param {Object} params - API request parameters
 */
const fetchData = async (params = {}) => {
  loading.value = true;
  try {
    await store.dispatch('STUDENT_GET_ALL', {
      sort_by: params.sort_by || sortBy.value,
      sort_by_order: params.sort_by_order || sortOrder.value,
      page: params.page || 1,
      paginate: 25,
      status: params.status || 1,
      search: params.search || '',
    });
  } catch (error) {
    console.error('Failed to fetch students:', error);
  } finally {
    loading.value = false;
    if (initialLoad.value) initialLoad.value = false;
  }
};

/**
 * Route Query Watcher
 * Updates search value when URL query changes
 */
watch(
  () => route.query,
  (newQuery) => {
    search.value = newQuery.search ?? null;
  }
);

/**
 * Component Initialization
 * Fetches initial data on mount
 */
onMounted(() => {
  fetchData(route.query);
});
</script>
<style lang="scss" scoped></style>

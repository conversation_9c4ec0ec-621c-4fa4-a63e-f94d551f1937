import { createI18n } from 'vue-i18n';

// Load your locale messages (e.g., en.json, ja.json)
async function loadLocaleMessages() {
  const locales = import.meta.glob('../locales/*.json');
  const messages = {};

  for (const path in locales) {
    const matched = path.match(/([A-Za-z0-9-_]+)\./i);
    if (matched && matched.length > 1) {
      const locale = matched[1];
      const module = await locales[path](); // Await the promise to get the module
      messages[locale] = module.default;
    }
  }

  return messages;
}

async function createI18nInstance() {
  const messages = await loadLocaleMessages();

  const i18n = createI18n({
    legacy: false, // Use Composition API mode
    locale: 'jp', // Set default locale
    fallbackLocale: 'en',
    globalInjection: true, // Inject $t globally in components
    messages,
  });

  return i18n;
}

export default createI18nInstance;

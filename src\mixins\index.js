import { mapGetters } from 'vuex';
import moment from 'moment';

export default {
  computed: {
    ...mapGetters(['getMasterData']),
  },
  methods: {
    numberFormat(x) {
      return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },
    dateFormat(date, format = 'YYYY/MM/DD') {
      return moment(date).format(format);
    },
    yearMonthFormat(date, format = 'YYYY/MM') {
      return moment(date).format(format);
    },
    capitalizeFirstLetter(str) {
      return str.charAt(0).toUpperCase() + str.slice(1);
    },
    showAlert(error, status = true) {
      let errorText = null;
      if (typeof error === 'string') {
        errorText = error;
      } else {
        errorText =
          this.$t(error?.data?.message) ||
          this.$t(error?.data?.errors?.message);
      }
      this.$store.commit('showAlert', {
        text: errorText,
        successStatus: status,
      });
    },
    getDateRange(startDate, endDate, type, formattedDate = 'YYYY/MM/DD') {
      let fromDate = moment(startDate);
      let toDate = moment(endDate);
      let diff = toDate.diff(fromDate, type);
      let range = [];
      for (let i = 0; i < diff; i++) {
        range.push(moment(startDate).add(i, type).format(formattedDate));
      }
      return range;
    },
    scrollToTheFirstErrorInput() {
      this.$nextTick(() => {
        const el = document.querySelector(
          '.v-messages.error--text:first-of-type'
        );
        if (el) {
          el.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      });
    },
  },
};

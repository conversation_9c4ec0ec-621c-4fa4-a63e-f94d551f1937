<template>
  <div class="">
    <PageTitle
      :items="{
        title: 'コラム',
        subTitle: 'タグ管理',
        buttons: [
          {
            title: '新規作成',
            icon: 'mdi-plus-circle',
            action: () => {
              launchNew = true;
            },
          },
        ],
      }"
    ></PageTitle>
    <DataTable
      :items="getAllMediaTags"
      :headers="headers"
      :total-records="totalRecords ? totalRecords : 0"
      :number-of-pages="totalPages ? totalPages : 0"
      @row-clicked="
        launchEdit = true;
        editItem = $event;
      "
    >
      <template v-slot:item.delete="{ item }">
        <v-btn
          variant="text"
          color="transparent"
          @click.stop="deleteInitiate(item.id)"
        >
          <v-icon size="20">$delete</v-icon>
        </v-btn>
      </template>
    </DataTable>
    <MediaTagDialog
      v-model:launch="launchNew"
      :edit="false"
      @refresh="getDataFromApi"
    ></MediaTagDialog>

    <MediaTagDialog
      v-model:launch="launchEdit"
      :item="editItem"
      @refresh="getDataFromApi"
    ></MediaTagDialog>

    <SimpleModel
      text="このタグを削除してよろしいですか？"
      :dialog="dialog.delete"
      @submitSuccess="deleteMediaTags()"
      :submitButtonText="'削除する'"
      @closeModel="dialog.delete = false"
    ></SimpleModel>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useStore } from 'vuex';
import DataTable from '@/components/ui/DataTable.vue';
import MediaTagDialog from '@/components/models/MediaTagDialog.vue';
import SimpleModel from '@/components/models/SimpleModel.vue';

const launchEdit = ref(false);
const launchNew = ref(false);
const editItem = ref(null);
const dialog = ref({
  delete: false,
});

const headers = [
  {
    title: 'タグ',
    value: 'name',
    sortable: false,
    width: '90%',
  },
  {
    title: '',
    sortable: false,
    value: 'delete',
    width: '10%',
  },
];
/**
 * State Management
 */
// Temporary storage for tag being deleted
const temporaryDeleteId = ref(null);

// Flag to track initial data load
const initialLoad = ref(true);

const store = useStore();

/**
 * Computed Properties for Vuex Store Access
 */
// Get all media tags from store
const getAllMediaTags = computed(() => {
  return store.getters.getAllMediaTags;
});

// Get total number of records for pagination
const totalRecords = computed(
  () => store.getters.getMediaTagsPagination?.records_total || 0
);

// Get total number of pages for pagination
const totalPages = computed(
  () => store.getters.getMediaTagsPagination?.total_pages || 0
);

/**
 * Fetches media tags data from API with sorting and pagination
 * @param {Object} e - Table options including sort and pagination
 * @param {Object} obj - Additional search parameters
 */
const getDataFromApi = async (e = undefined, obj = {}) => {
  const data = {
    search: e?.search || obj.search,
    sort_by: e?.sortBy?.[0] || 'created_at',
    sort_by_order: e?.sortDesc?.[0] ? 'desc' : 'asc',
    page: typeof e === 'number' ? e : (e?.page ?? 1),
    paginate: e?.itemsPerPage || 25,
  };
  await store.dispatch('MEDIA_TAGS_GET_ALL', data).then(() => {
    if (initialLoad.value) {
      initialLoad.value = false;
    }
  });
};

/**
 * Deletes a media tag
 * Handles API call and UI updates after successful deletion
 */
const deleteMediaTags = async () => {
  await store
    .dispatch('MEDIA_TAGS_DELETE', temporaryDeleteId.value)
    .then((res) => {
      if (res.status === 200) {
        dialog.value.delete = false; // Close delete confirmation dialog
        getDataFromApi(); // Refresh tag list
        temporaryDeleteId.value = null; // Clear temporary ID
      }
    });
};

/**
 * Initiates tag deletion process
 * Opens confirmation dialog and stores tag ID
 * @param {number} id - ID of tag to be deleted
 */
const deleteInitiate = (id) => {
  dialog.value.delete = true;
  temporaryDeleteId.value = id;
};

/**
 * Component initialization
 * Fetches initial tag data on mount
 */
onMounted(async () => {
  await getDataFromApi();
});
</script>

<template>
  <div v-show="!loading && !$store.getters.getApiProcessingStatus">
    <PageTitle :items="titleItems" />

    <v-fade-transition>
      <SearchBox
        v-if="isSearchVisible"
        class="mb-5"
        :searchPlaceholder="'タイトル'"
        :toggleSearch="isSearchVisible"
        @toggleSearch="hideSearch"
        @search-table="handleSearch"
      />
    </v-fade-transition>

    <DataTable
      ref="dataTableRef"
      :headers="headers"
      :items="analysisList"
      :total-records="totalRecords"
      :number-of-pages="totalPages"
      :loading="loading"
      @update:options="updateTable"
    >
      <template v-slot:item.row_number="{ item }">
        <span class="d-block text-left"> {{ item.rowNumber }}. </span>
      </template>

      <!-- student -->
      <template v-slot:item.student_id="{ item }">
        <div class="font-12px fw-400">
          {{ item?.student?.internal_student_id }}
        </div>
        <div class="mouse-pointer">
          <router-link
            target="_blank"
            class="text-3f74c2 text-decoration-none truncate-lines lines-1"
            :to="{
              name: 'StudentProfile',
              params: { id: item.student_id },
            }"
          >
            <v-tooltip
              :text="`${item.student.full_name}`"
              location="top"
              color="white"
            >
              <template #activator="{ props }">
                <span v-bind="props">
                  {{ item.student.full_name }}
                </span>
              </template>
            </v-tooltip>
          </router-link>
        </div>
      </template>

      <template #item.created_at="{ item }">
        {{ formatDate(item.created_at) }}
      </template>

      <template #item.text_relationship_others="{ item }">
        {{ item.type_relationship === 'その他' ? item.text_relationship_others : item.type_relationship }}
      </template>

      <!-- action -->
      <template v-slot:item.action="{ item }">
        <div
          class="font-12px fw-400 cursor-pointer"
          @click.stop="
            $router.push({
              name: 'AnalysisFromOthersDetail',
              params: { id: item.id },
            })
          "
        >
          <v-icon color="red">$edit</v-icon>
        </div>
      </template>
    </DataTable>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, defineAsyncComponent } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import moment from 'moment';
const DataTable = defineAsyncComponent(
  () => import('@/components/ui/DataTable.vue')
);
const SearchBox = defineAsyncComponent(
  () => import('@/components/ui/SearchBox.vue')
);

const store = useStore();
const router = useRouter();
const dataTableRef = ref(null);

const loading = ref(false);
const initialLoad = ref(true);
const isSearchVisible = ref(false);
const selectedStatus = ref('N');
const sortConfig = ref({
  field: 'public_date',
  order: 'desc',
});

const headers = ref([
  {
    title: '番号', // Column for row number
    align: 'center',
    sortable: false,
    value: 'row_number',
  },
  {
    title: '学生ID',
    subTitle: '学生名',
    align: 'left',
    sortable: false,
    value: 'student_id',
  },
  {
    title: '学生との関係',
    align: 'left',
    sortable: false,
    value: 'text_relationship_others',
  },
  {
    title: '評価された力',
    align: 'left',
    sortable: false,
    value: 'super_power_review',
  },
  {
    title: '期待したい力',
    align: 'left',
    sortable: false,
    value: 'growth_idea_review',
  },
  {
    title: '登録日',
    align: 'left',
    sortable: false,
    value: 'created_at',
  },
  { title: '', value: 'action', flex: '1 1 15%' },
]);

/**
 * Computed properties for accessing Vuex store data
 * Provides reactive access to analysis posts and pagination
 */
const analysisList = computed(() => {
  return store.getters.getAllAnalysis.map((item, index) => ({
    ...item,
    rowNumber: index + 1, // Adding rowNumber starting from 1
  }));
});

const pagination = computed(() => store.getters.getAnalysisPagination);
const analysisCounts = computed(
  () => store.getters.getAnalysisCounts || { total_opened: 0, total_drafted: 0 }
);

/**
 * Computed properties for pagination display
 */
const totalRecords = computed(() => pagination.value?.records_total || 0);
const totalPages = computed(() => pagination.value?.total_pages || 0);

/**
 * Page title configuration with tabs and action buttons
 * Includes published/draft tabs and search/create buttons
 */
const titleItems = computed(() => ({
  title: '他己分析管理', // Column
  subTitle: '一覧', // List
  buttons: [
    {
      title: '詳細条件検索', // Detailed search
      class: 'bg-white',
      variant: 'outlined',
      action: toggleSearch,
    },
  ],
}));

/**
 * Formats date to Japanese format or placeholder
 * @param {string} date - Date string to format
 * @returns {string} Formatted date (YYYY/MM/DD) or placeholder
 */
const formatDate = (date) => {
  return date ? moment(date).format('YYYY/MM/DD') : '---- / - / -';
};

/**
 * Resets table pagination to first page
 * Updates pagination counter to trigger re-render
 */
const resetPagination = () => {
  if (dataTableRef.value) {
    dataTableRef.value.currentPage = 1;
    dataTableRef.value.updatePaginate++;
  }
};

/**
 * Handles tab change between published and draft posts
 * @param {string} status - 'N' for published, 'Y' for draft
 */
const handleStatusChange = async (status) => {
  selectedStatus.value = status;
  resetPagination();
  await fetchData();
};

/**
 * Search visibility toggle handlers
 */
const toggleSearch = () => {
  isSearchVisible.value = !isSearchVisible.value;
};

const hideSearch = () => {
  isSearchVisible.value = false;
};

/**
 * Handles row click navigation to edit page
 * @param {Object} item - Selected post data
 */
const handleRowClick = (item) => {
  router.push({
    name: 'AnalysisFromOthersDetail',
    params: { id: item.id },
  });
};

/**
 * Handles search input and triggers data fetch
 * @param {string} searchTerm - Search query text
 */
const handleSearch = async (searchTerm) => {
  await fetchData(undefined, { search: searchTerm });
};

/**
 * Fetches analysis  posts data with pagination and filters
 * @param {Object} options - Pagination and sorting options
 * @param {Object} additionalParams - Additional search parameters
 */
const fetchData = async (options = {}, additionalParams = {}) => {
  loading.value = true;
  try {
    const params = {
      sort_by: sortConfig.value.field,
      sort_by_order: sortConfig.value.order,
      page: options?.page || 1,
      paginate: options?.itemsPerPage || 25,
      ...additionalParams,
    };

    // Remove empty search parameter
    if (params.search === '') {
      delete params.search;
    }

    await store.dispatch('ANALYSIS_GET_ALL', params);
  } catch (error) {
    console.error('Failed to fetch analysis posts:', error);
  } finally {
    loading.value = false;
    initialLoad.value = false;
  }
};

const getMasterData = computed(() => store.getters.getMasterData);
const getReviewName = (reviewId) => {
  return (
    getMasterData.value.reviews_option?.find((option) => option.id === reviewId)
      ?.name || 'Unknown'
  );
};

/**
 * Handles table sorting and pagination updates
 * @param {Object} options - Table options including sort and page
 */
const updateTable = async (options) => {
  if (!initialLoad.value) {
    if (options.sortBy?.length) {
      sortConfig.value = {
        field: options.sortBy[0],
        order: options.sortDesc[0] ? 'desc' : 'asc',
      };
    }
    await fetchData(options);
  }
};

/**
 * Component initialization
 * Fetches initial data on mount
 */
// Fetch data when the component is mounted
onMounted(async () => {
  loading.value = true;
  await fetchData();
  loading.value = false;
});
</script>

<template>
  <div class="corporate-page create-page">
    <v-sheet color="transparent">
      <PageTitle
        :items="{
          title: '企業管理',
          subTitle: '企業新規作成',
          back: {
            action: () => {
              $router.push({
                name: 'Corporate',
              });
            },
          },
        }"
      ></PageTitle>
      <v-row>
        <v-col cols="12">
          <div>
            <Form @submit="submit">
              <v-card class="pa-5 rounded-sm">
                <v-container class="container-main">
                  <v-row>
                    <v-col cols="12" md="4" class="mt-1">
                      <div
                        class="d-flex flex-column align-center justify-center"
                      >
                        <div
                          class="file-input-box d-flex align-center justify-center preview"
                          @mouseover="$refs.close.style.visibility = 'visible'"
                          @mouseleave="$refs.close.style.visibility = 'hidden'"
                          v-if="croppedImagePreview"
                        >
                          <div
                            class="close d-flex align-center justify-center"
                            ref="close"
                          >
                            <v-btn
                              @click="croppedImagePreview = null"
                              width="50px"
                              height="50px"
                              color="primary"
                              fab
                              dark
                              ><v-icon color="white">mdi-close</v-icon></v-btn
                            >
                          </div>
                          <img
                            class="full-height full-width"
                            :src="croppedImagePreview"
                          />
                        </div>
                        <Field
                          v-else
                          v-slot="{
                            field: { value, ...fieldWithoutValue },
                            errors,
                          }"
                          :value="fields.logo_img"
                          name="logo_img"
                          rules="size:2048|ext:jpg,png|maxDimensions:500,500"
                        >
                          <div
                            class="file-input-box d-flex align-center justify-center"
                          >
                            <div class="file-input-inr">
                              <v-file-input
                                ref="fileInput"
                                accept="image/*"
                                v-bind="fieldWithoutValue"
                                :error-messages="errors"
                                :error="errors.length !== 0"
                                :hide-details="errors.length <= 0"
                                hide-input
                                truncate-length="1"
                                name="logo_image"
                                @change="previewImage"
                                v-model="fields.logo_img"
                                prepend-icon="mdi-plus-circle"
                                class="d-flex align-center justify-center"
                              ></v-file-input>
                              <h6 class="font-14px fw-500 text-default mb-0">
                                ロゴを登録する
                              </h6>
                            </div>
                          </div>
                          <template v-if="errors.length > 0">
                            <span class="font-12px text-red">{{
                              errors[0]
                            }}</span>
                          </template>
                        </Field>

                        <ImageCrop
                          v-if="previewImageURL"
                          :imageSrc="previewImageURL"
                          @image-crop-success="imageCropSuccess"
                          @crop-image-cancel="imageCropCancel"
                        ></ImageCrop>
                        <v-sheet
                          color="#F9F9F9"
                          width="200px"
                          height="91px"
                          class="d-flex align-center justify-center font-14px mt-4 rounded"
                        >
                          画素：256px×256px以上<br />
                          サイズ：2Mb以下<br />
                          形式：jpg、png
                        </v-sheet>
                      </div>
                    </v-col>
                    <v-col cols="12" md="8">
                      <v-row>
                        <v-col cols="12" md="12" class="mb-n5">
                          <label class="d-block font-14px mb-1">
                            <span>内部ID</span>
                            <span class="error--text ml-2 font-12px"></span>
                          </label>

                          <Field
                            v-slot="{
                              field: { value, ...fieldWithoutValue },
                              errors,
                            }"
                            :value="fields.internal_company_id"
                            name="内部ID"
                            ref="observer"
                            rules=""
                          >
                            <v-text-field
                              v-bind="fieldWithoutValue"
                              :error-messages="errors"
                              :error="errors.length !== 0"
                              :hide-details="errors.length <= 0"
                              variant="outlined"
                              v-model="fields.internal_company_id"
                              density="compact"
                              placeholder="登録時に生成します"
                              class="bg-input-disabled"
                            >
                            </v-text-field>
                          </Field>
                        </v-col>
                        <v-col cols="12" md="12" class="mb-n5">
                          <label class="d-block font-14px mb-1">
                            <span>企業名</span>
                            <span class="error--text ml-2 font-12px">必須</span>
                          </label>
                          <Field
                            v-slot="{
                              field: { value, ...fieldWithoutValue },
                              errors,
                            }"
                            :value="fields.name"
                            name="name"
                            rules="required:企業名 "
                          >
                            <v-text-field
                              v-bind="fieldWithoutValue"
                              :error-messages="errors"
                              :error="errors.length !== 0"
                              :hide-details="errors.length <= 0"
                              variant="outlined"
                              v-model="fields.name"
                              density="compact"
                              placeholder="企業名"
                              color="grey"
                              id="name"
                              name="name"
                              @input="handleNameInput"
                            >
                            </v-text-field>
                          </Field>
                        </v-col>
                        <v-col cols="12" md="12" class="mb-n5">
                          <label class="d-block font-14px mb-1">
                            <span>企業名フリガナ</span>
                          </label>
                          <Field
                            v-slot="{
                              field: { value, ...fieldWithoutValue },
                              errors,
                            }"
                            :value="fields.furigana_name"
                            name="furigana_name"
                            rules="full_width_katakana|max:100"
                          >
                            <v-text-field
                              v-model="furiganaName"
                              :error-messages="errors"
                              :error="errors.length !== 0"
                              :hide-details="errors.length <= 0"
                              variant="outlined"
                              density="compact"
                              id="furigana"
                              name="furigana"
                              placeholder="企業名フリガナ"
                            >
                            </v-text-field>
                          </Field>
                        </v-col>
                        <v-col cols="12" md="12" class="mb-n5">
                          <label class="d-block font-14px mb-1">
                            <span>業界</span>
                          </label>
                          <Field
                            v-slot="{
                              field: { value, ...fieldWithoutValue },
                              errors,
                            }"
                            :value="fields.business_industry_id"
                            name="business_industry_id"
                          >
                            <v-select
                              v-bind="fieldWithoutValue"
                              :error-messages="errors"
                              :error="errors.length !== 0"
                              :hide-details="errors.length <= 0"
                              variant="outlined"
                              v-model="fields.business_industry_id"
                              density="compact"
                              placeholder="選択してください"
                              item-title="name"
                              item-value="id"
                              :items="
                                $store.state.master.master.business_industories
                              "
                              color="grey"
                            >
                            </v-select>
                          </Field>
                        </v-col>
                        <v-col cols="12" md="12" class="mb-n5">
                          <label class="d-block font-14px mb-1">
                            <span>住所</span>
                          </label>
                          <Field
                            v-slot="{
                              field: { value, ...fieldWithoutValue },
                              errors,
                            }"
                            :value="fields.office_address"
                            name="office_address"
                            rules="max:100"
                          >
                            <v-text-field
                              v-bind="fieldWithoutValue"
                              :error-messages="errors"
                              :error="errors.length !== 0"
                              :hide-details="errors.length <= 0"
                              auto-select-first
                              chips
                              deletable-chips
                              density="compact"
                              multiple
                              small-chips
                              v-model="fields.office_address"
                              variant="outlined"
                              item-text="ja"
                              item-value="id"
                              placeholder="住所"
                              color="grey"
                            ></v-text-field>
                          </Field>
                        </v-col>
                      </v-row>
                      <v-row>
                        <v-col cols="12" md="12" class="mb-n5">
                          <label class="d-block font-14px mb-1">
                            <span>電話番号</span>
                          </label>
                          <Field
                            v-slot="{
                              field: { value, ...fieldWithoutValue },
                              errors,
                            }"
                            :value="fields.office_phone"
                            name="office_phone"
                            rules="enter_half_width_numbers_hyphens"
                          >
                            <v-text-field
                              v-bind="fieldWithoutValue"
                              :error-messages="errors"
                              :error="errors.length !== 0"
                              :hide-details="errors.length <= 0"
                              variant="outlined"
                              v-model="fields.office_phone"
                              density="compact"
                              placeholder="電話番号"
                            >
                            </v-text-field>
                          </Field>
                        </v-col>
                      </v-row>
                      <v-row>
                        <v-col cols="12" md="12" class="mb-n5">
                          <label class="d-block font-14px mb-1">
                            <span>ホームページURL</span>
                          </label>
                          <Field
                            v-slot="{
                              field: { value, ...fieldWithoutValue },
                              errors,
                            }"
                            :value="fields.website_url"
                            name="website_url"
                            rules="url"
                          >
                            <v-text-field
                              v-bind="fieldWithoutValue"
                              :error-messages="errors"
                              :error="errors.length !== 0"
                              :hide-details="errors.length <= 0"
                              variant="outlined"
                              v-model="fields.website_url"
                              density="compact"
                              placeholder="ホームページURL"
                            >
                            </v-text-field>
                          </Field>
                        </v-col>
                      </v-row>
                      <v-row>
                        <v-col cols="12" md="12" class="mb-n5">
                          <label class="d-block font-14px mb-1">
                            <span>メモ</span>
                          </label>
                          <Field
                            v-slot="{
                              field: { value, ...fieldWithoutValue },
                              errors,
                            }"
                            :value="fields.admin_memo"
                            name="admin_memo"
                            rules="max:1500"
                          >
                            <v-textarea
                              v-bind="fieldWithoutValue"
                              :error-messages="errors"
                              :error="errors.length !== 0"
                              :hide-details="errors.length <= 0"
                              variant="outlined"
                              v-model="fields.admin_memo"
                              density="compact"
                              placeholder="メモ"
                            >
                            </v-textarea>
                          </Field>
                        </v-col>
                      </v-row>

                      <div class="slack-container mt-10">
                        <v-row class="py-4 px-6" align="center">
                          <v-col cols="8" md="8">
                            <div>Slack通知</div>
                            <div class="text-8e">
                              システム内でメール通知があったタイミングで、
                            </div>
                            <div class="text-8e">
                              Slackチャンネルに通知が届きます。
                            </div>
                          </v-col>
                          <v-col cols="4" md="4">
                            <Field
                              v-slot="{
                                field: { value, ...fieldWithoutValue },
                                errors,
                              }"
                              :value="fields.flg_slack_notification"
                              name="flg_slack_notification"
                              rules=""
                            >
                              <v-select
                                v-bind="fieldWithoutValue"
                                density="compact"
                                variant="outlined"
                                single-line
                                color="#13ABA3"
                                class="font-14px mt-1 small-label"
                                :hide-details="true"
                                :items="slackOptions"
                                item-title="name"
                                item-value="value"
                                v-model="fields.flg_slack_notification"
                                menu-icon="$greyExpansionDropdown"
                                style="background-color: white"
                              />
                            </Field>
                          </v-col>
                          <v-col v-if="fields.flg_slack_notification" cols="12">
                            <Field
                              v-slot="{
                                field: { value, ...fieldWithoutValue },
                                errors,
                              }"
                              :value="fields.slack_webhook"
                              name="slack_webhook"
                              :rules="
                                fields.flg_slack_notification
                                  ? 'error_slack'
                                  : ''
                              "
                            >
                              <v-text-field
                                v-bind="fieldWithoutValue"
                                :hide-details="true"
                                auto-select-first
                                density="compact"
                                placeholder="Webhook URL を設定してください。"
                                v-model="fields.slack_webhook"
                                variant="outlined"
                                style="background-color: white"
                              ></v-text-field>
                              <div
                                class="error--text font-12px"
                                v-if="errors.length !== 0"
                              >
                                {{ errors[0] }}
                              </div>
                            </Field>
                          </v-col>
                        </v-row>
                      </div>

                      <v-row>
                        <v-col cols="12" md="12" class="mb-n5">
                          <Field
                            v-slot="{ field, errors }"
                            :value="fields.status"
                            name="status"
                            rules=""
                          >
                            <v-col cols="6" class="ml-16 pl-8">
                              <v-switch
                                color="#13ABA3"
                                class="mt-0 pt-0"
                                :error-messages="errors"
                                :error="errors.length !== 0"
                                :hide-details="errors.length <= 0"
                                :model-value="switchValue"
                                @update:modelValue="onSwitchChange"
                                outlined
                                :label="switchLabel"
                              ></v-switch>
                            </v-col>
                          </Field>
                        </v-col>
                      </v-row>
                      <v-col cols="12" md="8" class="mt-3 pl-10">
                        <v-btn
                          type="submit"
                          :loading="loading"
                          color=""
                          width="150"
                          height="35"
                          class="bg-default white--text ml-13"
                          placeholder="企業名"
                        >
                          登録
                        </v-btn>
                      </v-col>
                    </v-col>
                  </v-row>
                </v-container>
              </v-card>
            </Form>
          </div>
          <SuccessModel
            :text="`企業情報を登録しました。`"
            :buttonText="`企業ユーザ登録へ進む`"
            :routeName="`CorporateDetails`"
            :specificId="currentCorporateId"
            :dialog="successDialog"
            @closeModel="successDialog.value = false"
          ></SuccessModel>
        </v-col>
      </v-row>
    </v-sheet>
  </div>
</template>

<script>
import { ref, computed, defineAsyncComponent } from 'vue';
const SuccessModel = defineAsyncComponent(
  () => import('@/components/models/SuccessModel.vue')
);
const ImageCrop = defineAsyncComponent(
  () => import('@/components/admin/partials/Corporate/ImageCrop.vue')
);
import CorporateMixin from './corporate.mixin';
import { Field, Form } from 'vee-validate';
import { useStore } from 'vuex';

export default {
  name: 'Create',
  components: { SuccessModel, ImageCrop, Field, Form },
  metaInfo: {
    title: 'コトナル 管理者 企業管理 | 企業新規作成',
  },
  setup() {
    const store = useStore();
    const {
      previewImageURL,
      croppedImagePreview,
      loading,
      postalCodeLoading,
      fields,
      switchLabel,
      handleNameInput,
      imageCropSuccess,
      imageCropCancel,
      previewImage,
      logoImageId,
      successDialog,
    } = CorporateMixin();

    const switchValue = ref(true);
    const onSwitchChange = (value) => {
      switchValue.value = value; // Update the local switch state
      fields.value.status = value; // Update the Vee-Validate form value manually
    };

    const currentCorporateId = ref(null);
    const slackOptions = ref([
      {
        value: 0,
        name: 'オフ',
      },
      {
        value: 1,
        name: 'オン',
      },
    ]);
    const manualFurigana = ref(null);

    const furiganaName = computed({
      get() {
        return manualFurigana.value !== null
          ? manualFurigana.value
          : fields.value.name;
      },
      set(value) {
        manualFurigana.value = value;
      },
    });

    /**
     * Submit handler for corporate creation form
     * Handles the creation of a new corporate entity with logo and status
     * @param {Object} values - Form values from vee-validate
     * @returns {Promise<void>}
     */
    const submit = async (values) => {
      loading.value = true;
      // Set the cropped logo image to both fields and values
      fields.logo_img = croppedImagePreview.value;
      values.furigana_name = furiganaName.value;
      try {
        // Update form values with current switch state and logo
        values.status = switchValue.value; // Active/Inactive status
        values.logo_img = croppedImagePreview.value;
        const data = {
          ...values,
        };
        // Create new corporate and store response
        const response = await store.dispatch('CORPORATE_CREATE', data);
        // Store the newly created corporate ID for redirection
        currentCorporateId.value = response.data.data.data.id;
        // Refresh master data to include new corporate
        // Show success dialog for user feedback
        successDialog.value = true;
      } catch (error) {
        // Error handling could be added here
      } finally {
        // Ensure loading state is reset
        loading.value = false;
      }
    };

    return {
      currentCorporateId,
      slackOptions,
      submit,
      loading,
      fields,
      furiganaName,
      switchLabel,
      handleNameInput,
      imageCropSuccess,
      imageCropCancel,
      previewImage,
      logoImageId,
      previewImageURL,
      croppedImagePreview,
      postalCodeLoading,
      successDialog,
      onSwitchChange,
      switchValue,
    };
  },
};
</script>

<style lang="scss" src="./style.scss" scope></style>

<template>
  <v-container>
    <v-row justify="center">
      <v-col cols="12" sm="10" md="8">
        <v-card elevation="0" class="d-flex justify-start pl-10">
          <div
            class="mt-10"
            :class="{
              'container-history': $vuetify.breakpoint.lgAndUp,
              'container-history-mobile': $vuetify.breakpoint.mdAndDown,
            }"
          >
            <div
              v-for="(item, index) in detailApplicant"
              :key="index"
              class="no-truncate-history"
            >
              <div>
                <div class="no-truncate-history mb-6">
                  <div
                    class="text-97 pr-10"
                    :class="{
                      'font-18px': $vuetify.breakpoint.lgAndUp,
                      'font-14px': $vuetify.breakpoint.mdAndDown,
                    }"
                  >
                    {{ item.label }}
                  </div>
                  <div
                    class="d-flex justify-space-between align-center mt-1 pr-4"
                  >
                    <div
                      v-if="item.content && !item.isURL"
                      class="sub-label"
                      :class="{
                        'font-16px': $vuetify.breakpoint.lgAndUp,
                        'font-12px': $vuetify.breakpoint.mdAndDown,
                      }"
                    >
                      {{ item.content }}
                      <div v-if="item.content2">{{ item.content2 }}</div>
                      <div v-if="item.content3">{{ item.content3 }}</div>
                    </div>
                    <div
                      v-if="item.content && item.isURL"
                      class="sub-label text-3979d9"
                      :class="{
                        'font-16px': $vuetify.breakpoint.lgAndUp,
                        'font-12px': $vuetify.breakpoint.mdAndDown,
                      }"
                    >
                      <p v-if="item.content === '回答なし'">
                        {{ item.content }}
                      </p>
                      <a
                        v-if="
                          isUrl(item.content) && item.content !== '回答なし'
                        "
                        class="text-blue"
                        :href="extractUrl(item.content)"
                        target="_blank"
                      >
                        {{ item.content }}
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>
<script>
import InternshipMixins from '../../internship/internship.mixin';
import { mapGetters } from 'vuex';

export default {
  name: 'StudentApplicationDetails',
  mixins: [InternshipMixins],
  data() {
    return {
      detailApplicant: [
        { label: '姓名', content: '' },
        { label: 'セイメイ', content: '' },
        { label: '学校名', content: '' },
        { label: '学部/専攻', content: '' },
        { label: '卒業予定', content: '' },
        { label: 'メールアドレス ', content: '' },
        { label: '興味のある仕事', content: '' },
        { label: '自己PR', content: '' },
        { label: '履歴書URL', content: '' },
        { label: '資格・実績URL', content: '' },
        {
          label:
            'セキュリティ管理について、個人的に行っていることを記載してください。',
          content: '',
        },
        {
          label: 'もっとも自信のあるデザインワークのURLを記載してください。',
          content: '',
        },
      ],
      showToggleCopy: false,
    };
  },
  created() {
    this.$store.dispatch('GET_STUDENT_APPLICATION_DETAILS', {
      studentID: this.$route.params.studentID,
      applicationID: this.$route.params.applicationID,
    });
  },
  mounted() {
    this.fetchApplicantDetails();
  },
  methods: {
    fetchApplicantDetails() {
      this.detailApplicant = [
        {
          label: '姓名',
          content:
            `${this.getStudentApplicationDetailsData.family_name} ${this.getStudentApplicationDetailsData.first_name}` ||
            '回答なし',
        },
        {
          label: 'セイメイ',
          content:
            `${this.getStudentApplicationDetailsData.family_name_furigana} ${this.getStudentApplicationDetailsData.first_name_furigana}` ||
            '回答なし',
        },
        {
          label: '学校名',
          content:
            `${this.getStudentApplicationDetailsData.educationFacilityName}` ||
            '_',
        },
        {
          label: '学部/専攻',
          content:
            `${this.getStudentApplicationDetailsData.big_field_name} / ${this.getStudentApplicationDetailsData.small_field_name}` ||
            '回答なし',
        },
        {
          label: '卒業予定',
          content:
            `${
              this.getStudentApplicationDetailsData.graduate_year || '回答なし'
            } 年 ${this.getStudentApplicationDetailsData.graduate_month} 月` ||
            '回答なし',
        },
        {
          label: 'メールアドレス',
          content:
            this.getStudentApplicationDetailsData.email_valid || '回答なし',
        },
        {
          label: '興味のある仕事',
          content: `${
            this.getStudentApplicationDetailsData.text_interested_job_1 ||
            '回答なし'
          }`,
          content2: ` ${
            this.getStudentApplicationDetailsData.text_interested_job_2 || ''
          }`,
          content3: `${
            this.getStudentApplicationDetailsData.text_interested_job_3 || ''
          }`,
        },
        {
          label: '自己PR',
          content:
            this.getStudentApplicationDetailsData.self_introduction ||
            '回答なし',
          isCopy: true,
        },
        {
          label: '履歴書URL',
          content:
            this.getStudentApplicationDetailsData.resume_url !== null
              ? `${this.getStudentApplicationDetailsData.resume_url}`
              : '回答なし',
          isCopy: true,
          isURL: true,
        },
        {
          label: '資格・実績URL',
          content: `${
            this.getStudentApplicationDetailsData?.qualification_url ||
            '回答なし'
          }`,
          isCopy: true,
          isURL: true,
        },
        {
          label: this.getStudentApplicationDetailsData?.flg_required_item
            ? this.getStudentApplicationDetailsData?.required_item_prompt
            : 'セキュリティ管理について、個人的に行っていることを記載してください。',
          content: `${
            this.getStudentApplicationDetailsData?.required_item_answer ||
            '回答なし'
          }`,
          isCopy: true,
        },
        {
          label: this.getStudentApplicationDetailsData?.flg_required_url
            ? this.getStudentApplicationDetailsData?.required_url_prompt
            : 'もっとも自信のあるデザインワークのURLを記載してください。',
          content: `${
            this.getStudentApplicationDetailsData?.required_url_answer ||
            '回答なし'
          }`,
          isCopy: true,
          isURL: true,
        },
      ];
    },
    isUrl(content) {
      const urlPattern = new RegExp(
        '^(https?:\\/\\/)?' + // validate protocol
          '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.?)+[a-z]{2,}|' + // domain name
          '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
          '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*' + // port and path
          '(\\?[;&a-z\\d%_.~+=-]*)?' + // query string
          '(\\#[-a-z\\d_]*)?$',
        'i'
      ); // fragment locator
      return !!urlPattern.test(content);
    },
    extractUrl(content) {
      if (this.isUrl(content)) {
        return content.startsWith('http') ? content : `http://${content}`;
      }
      return '#';
    },
    copyToClipboard(text) {
      const el = document.createElement('textarea');
      el.value = text;
      document.body.appendChild(el);
      el.select();
      document.execCommand('copy');
      document.body.removeChild(el);
      this.showToggleCopy = true;
      this.ToggleFavouriteAlertMessage = 'インターンシップを辞退しました。';
      setInterval(() => {
        this.showToggleCopy = false;
      }, 2000);
    },
  },
  computed: {
    ...mapGetters(['getStudentApplicationDetailsData']),
  },
};
</script>

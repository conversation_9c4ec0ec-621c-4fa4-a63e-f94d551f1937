<template>
  <div v-show="!loading && !$store.getters.getApiProcessingStatus">
    <PageTitle
      :items="{
        title: '興味のある仕事',
        buttons: [
          {
            title: '詳細条件検索',
            action: () => {
              toggleSearch = true;
            },
            class: 'bg-white',
            variant: 'outlined',
            others: {
              outlined: true,
            },
          },
          {
            title: '新規作成',
            icon: 'mdi-plus-circle',
            action: () => {
              launchNew = true;
            },
          },
        ],
      }"
    ></PageTitle>

    <v-fade-transition>
      <SearchBox
        class="mb-5"
        searchPlacholder="プルダウン表示項目"
        :toggleSearch="toggleSearch"
        @toggleSearch="toggleSearch = false"
        @search-table="searchTable"
        v-if="toggleSearch"
      ></SearchBox>
    </v-fade-transition>
    <DataTable
      class="school-table"
      :headers="headers"
      :items="getInterestedJob"
      :total-records="totalRecords"
      :number-of-pages="totalPages"
      @update:options="updateTable"
      @row-clicked="handleRowClick"
    >
      <template v-slot:item.delete="{ item }">
        <v-btn
          variant="text"
          color="transparent"
          @click.stop="deleteInitiate(item.id)"
        >
          <v-icon size="20">$delete</v-icon>
        </v-btn>
      </template>
    </DataTable>
    <InterestedJobDialog
      v-model:launch="launchNew"
      :edit="false"
      @refresh="getDataFromApi"
    ></InterestedJobDialog>

    <InterestedJobDialog
      v-model:launch="launchEdit"
      :item="editItem"
      @refresh="getDataFromApi"
    ></InterestedJobDialog>

    <SimpleModel
      text="この特徴を削除してよろしいですか？"
      :dialog="dialog.delete"
      @submitSuccess="deleteInterestedJob"
      :submitButtonText="'削除する'"
      @closeModel="dialog.delete = false"
    ></SimpleModel>
    <SuccessModel
      :text="alertText"
      :buttonText="`とじる`"
      :routeName="routeName"
      :dialog="successDialog"
      @closeModel="successDialog = false"
    >
    </SuccessModel>
  </div>
</template>

<script>
import { ref, computed, onMounted, defineAsyncComponent } from 'vue';
import { useStore } from 'vuex';
const DataTable = defineAsyncComponent(
  () => import('@/components/ui/DataTable.vue')
);
const InterestedJobDialog = defineAsyncComponent(
  () => import('@/components/models/InterestedJobDialog.vue')
);
const SimpleModel = defineAsyncComponent(
  () => import('@/components/models/SimpleModel.vue')
);
const SearchBox = defineAsyncComponent(
  () => import('@/components/ui/SearchBox.vue')
);
const SuccessModel = defineAsyncComponent(
  () => import('@/components/models/SuccessModel.vue')
);

export default {
  components: {
    DataTable,
    InterestedJobDialog,
    SimpleModel,
    SearchBox,
    SuccessModel,
  },
  setup() {
    const store = useStore();

    const alertText = ref([]);
    const successDialog = ref(false);
    const routeName = ref('');
    const launchEdit = ref(false);
    const launchNew = ref(false);
    const editItem = ref(null);
    const toggleSearch = ref(false);
    const dialog = ref({
      delete: false,
    });

    const headers = ref([
      {
        title: 'プルダウン表示項目',
        width: '70%',
        align: 'left',
        value: 'job_name',
        sortable: false,
      },
      {
        title: '表示順位',
        width: '20%',
        value: 'display_order',
        align: 'right',
        sortable: false,
      },
      {
        title: '',
        value: 'delete',
        width: '10%',
        align: 'center',
        sortable: false,
      },
    ]);

    const initialLoad = ref(true);
    const temporaryDeleteId = ref(null);

    const getInterestedJob = computed(() =>
      store.getters.getAllInterestedJob.map((item) => ({
        id: item.id,
        job_name: item.job_name,
        display_order: item.display_order,
      }))
    );

    const totalRecords = computed(
      () => store.getters.getInterestedJobPagination?.records_total || 0
    );

    const totalPages = computed(
      () => store.getters.getInterestedJobPagination?.total_pages || 0
    );

    const searchTable = (search) => {
      getDataFromApi(undefined, { search });
    };

    const deleteInterestedJob = async () => {
      try {
        const res = await store.dispatch(
          'INTERESTED_JOB_DELETE',
          temporaryDeleteId.value
        );
        if (res.status === 200) {
          dialog.value.delete = false;
          temporaryDeleteId.value = null;
          getDataFromApi();
        }
      } catch (error) {
        dialog.value.delete = false;
        alertText.value = [
          'インターンシップに使用されています。',
          '削除できません。',
        ];
        routeName.value = 'InterestedJob';
        successDialog.value = true;
      }
    };

    const deleteInitiate = (id) => {
      dialog.value.delete = true;
      temporaryDeleteId.value = id;
    };

    const sort_by_order = ref('desc');
    const sort_by = ref('display_order');

    const getDataFromApi = async (e = undefined, obj = {}) => {
      let data = {
        sort_by_order: sort_by_order.value,
        sort_by: sort_by.value,
        page: typeof e === 'number' ? e : (e?.page ?? 1),
        paginate: e?.itemsPerPage || 25,
      };

      data = { ...data, ...obj };
      if (data.search === '') {
        delete data.search;
      }

      await store.dispatch('INTERESTED_JOB_GET_ALL', data);
      initialLoad.value = false;
    };

    const page = ref(0);

    const updateTable = (e) => {
      sort_by_order.value = e?.sortBy?.[0]?.order || 'desc';
      sort_by.value = e?.sortBy?.[0]?.key || 'display_order';

      const option = {
        page: typeof e === 'number' ? e : (e?.page ?? 1),
      };

      page.value = e.page;
      if (!initialLoad.value) {
        getDataFromApi(option);
      }
    };

    const handleRowClick = (event) => {
      launchEdit.value = true;
      editItem.value = event;
    };

    // Fetch data when the component is mounted
    const loading = ref(false);
    onMounted(async () => {
      loading.value = true;
      await getDataFromApi();
      loading.value = false;
    });

    return {
      alertText,
      successDialog,
      routeName,
      launchEdit,
      launchNew,
      editItem,
      toggleSearch,
      dialog,
      headers,
      initialLoad,
      totalRecords,
      totalPages,
      getInterestedJob,
      searchTable,
      deleteInterestedJob,
      deleteInitiate,
      getDataFromApi,
      updateTable,
      handleRowClick,
    };
  },
};
</script>

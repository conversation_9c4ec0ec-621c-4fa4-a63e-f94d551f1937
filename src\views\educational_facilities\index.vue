<template>
  <div v-show="!loading && !$store.getters.getApiProcessingStatus">
    <PageTitle
      :items="{
        title: '教育機関',
        buttons: [
          {
            title: '詳細条件検索',
            action: () => (toggleSearch = !toggleSearch),
            class: 'bg-white',
            variant: 'outlined',
          },
          {
            title: '新規作成',
            icon: 'mdi-plus-circle',
            action: () => (launchNew = true),
          },
        ],
      }"
    ></PageTitle>

    <v-fade-transition>
      <SearchBox
        class="mb-5"
        :searchPlaceholder="'学校名'"
        :toggleSearch="toggleSearch"
        @toggleSearch="toggleSearch = false"
        @search-table="searchTable"
        v-if="toggleSearch"
      />
    </v-fade-transition>

    <DataTable
      class="school-table"
      :headers="headers"
      :items="getFacilities"
      :total-records="totalRecords"
      :number-of-pages="totalPages"
      @update:options="updateTable"
      @row-clicked="handleRowClick"
    >
      <template v-slot:item.type="{ item }">
        {{ item.type ? item.type.name : '' }}
      </template>
      <template v-slot:item.delete="{ item }">
        <v-btn
          variant="text"
          color="transparent"
          @click.stop="deleteInitiate(item.id)"
        >
          <v-icon size="20">$delete</v-icon>
        </v-btn>
      </template>
    </DataTable>

    <SchoolDialog
      v-model:launch="launchNew"
      :edit="false"
      @refresh="getDataFromApi"
    />

    <SchoolDialog
      v-model:launch="launchEdit"
      :item="editItem"
      @refresh="getDataFromApi"
    />

    <SimpleModel
      text="この教育機関を削除してよろしいですか？"
      :dialog="dialog.delete"
      @submitSuccess="deleteInstitution"
      :submitButtonText="'削除する'"
      @closeModel="dialog.delete = false"
    />

    <SuccessModel
      :text="alertText"
      :buttonText="`とじる`"
      :routeName="routeName"
      :dialog="successDialog"
      @closeModel="successDialog.value = false"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted, defineAsyncComponent } from 'vue';
import { useStore } from 'vuex';
const DataTable = defineAsyncComponent(
  () => import('@/components/ui/DataTable.vue')
);
const SchoolDialog = defineAsyncComponent(
  () => import('@/components/models/SchoolDialog.vue')
);
const SimpleModel = defineAsyncComponent(
  () => import('@/components/models/SimpleModel.vue')
);
const SearchBox = defineAsyncComponent(
  () => import('@/components/ui/SearchBox.vue')
);
const SuccessModel = defineAsyncComponent(
  () => import('@/components/models/SuccessModel.vue')
);

export default {
  components: {
    DataTable,
    SchoolDialog,
    SimpleModel,
    SearchBox,
    SuccessModel,
  },
  setup() {
    const store = useStore();

    const alertText = ref([]);
    const successDialog = ref(false);
    const routeName = ref('');
    const launchEdit = ref(false);
    const launchNew = ref(false);
    const editItem = ref(null);
    const toggleSearch = ref(false);
    const dialog = ref({
      delete: false,
    });

    const headers = ref([
      {
        title: '教育機関タイプ',
        width: '18%',
        align: 'left',
        value: 'type',
        sortable: false,
      },
      {
        title: '学校名',
        width: '28%',
        value: 'name',
        align: 'left',
        sortable: false,
      },
      {
        title: 'その他の学校名',
        width: '38%',
        value: 'others',
        align: 'left',
        sortable: false,
      },
      {
        title: '',
        value: 'delete',
        width: '10%',
        align: 'center',
        sortable: false,
      },
    ]);

    const initialLoad = ref(true);
    const temporaryDeleteId = ref(null);

    const getFacilities = computed(() =>
      store.getters.getAllFacilities.map((facility) => {
        const type = store.getters.getEducationFacilityType.find(
          (type) => type.id === facility.type
        );
        return {
          id: facility.id,
          name: facility.name,
          type: type ?? null,
        };
      })
    );

    const totalRecords = computed(
      () => store.getters.getFacilitiesPagination?.records_total || 0
    );

    const totalPages = computed(
      () => store.getters.getFacilitiesPagination?.total_pages || 0
    );

    const searchTable = (search) => {
      getDataFromApi(undefined, { search });
    };

    const deleteInstitution = async () => {
      try {
        const res = await store.dispatch(
          'FACILITIES_DELETE',
          temporaryDeleteId.value
        );
        if (res.status === 200) {
          dialog.value.delete = false;
          getDataFromApi();
          temporaryDeleteId.value = null;
        }
      } catch (error) {
        dialog.value.delete = false;
        alertText.value = ['削除できません。'];
        routeName.value = 'EducationalFacilities';
        successDialog.value = true;
      }
    };

    const deleteInitiate = (id) => {
      dialog.value.delete = true;
      temporaryDeleteId.value = id;
    };

    const sort_by_order = ref('desc');
    const sort_by = ref('created_at');

    const getDataFromApi = async (e = undefined, obj = {}) => {
      let data = {
        sort_by_order: sort_by_order.value,
        sort_by: sort_by.value,
        page: typeof e === 'number' ? e : (e?.page ?? 1),
        paginate: e?.itemsPerPage || 25,
      };

      data = { ...data, ...obj };
      if (data.search === '') {
        delete data.search;
      }

      await store.dispatch('FACILITIES_GET_ALL', data);
      initialLoad.value = false;
    };

    const page = ref(0);

    const updateTable = (e) => {
      sort_by_order.value = e?.sortBy?.[0]?.order || 'desc';
      sort_by.value = e?.sortBy?.[0]?.key || 'created_at';

      let option = {};
      if (!e.page) {
        option.page = e;
      } else {
        option.page = e.page;
      }

      page.value = e.page;
      if (!initialLoad.value) {
        getDataFromApi(option);
      }
    };

    const handleRowClick = (event) => {
      launchEdit.value = true;
      editItem.value = event;
    };

    // Fetch data when the component is mounted
    const loading = ref(false);
    onMounted(async () => {
      loading.value = true;
      await getDataFromApi();
      loading.value = false;
    });

    return {
      alertText,
      successDialog,
      routeName,
      launchEdit,
      launchNew,
      editItem,
      toggleSearch,
      dialog,
      headers,
      initialLoad,
      totalRecords,
      totalPages,
      getFacilities,
      searchTable,
      deleteInstitution,
      deleteInitiate,
      getDataFromApi,
      updateTable,
      handleRowClick,
    };
  },
};
</script>

<template>
  <v-row justify="center">
    <v-col>
      <!-- info company -->
      <div>
        <div class="container-div text-1f2020">
          <div
            :class="{
              'mx-6 px-2 mt-6': smAndDown,
              'mt-11 mx-auto': mdAndUp,
            }"
            class="benefits-background"
          >
            <div
              class="benefits-container"
              :class="{
                'mx-2 pb-6': smAndDown,
                'px-10 pb-7': mdAndUp,
              }"
            >
              <div
                class="pt-5"
                :class="{
                  'font-14px pr-3 font-weight-medium': smAndDown,
                  'font-18px font-weight-medium': mdAndUp,
                }"
              >
                {{ getStudentApplicationDetailsData?.internship_post?.title }}
              </div>
              <div
                class="d-flex"
                :class="{
                  'mt-3': smAndDown,
                  'mt-2': mdAndUp,
                }"
              >
                <span
                  v-if="
                    getStudentApplicationDetailsData.company &&
                    getStudentApplicationDetailsData.company.logo_img
                  "
                  class="company-log-box mr-2 d-flex"
                >
                  <img
                    :src="
                      getStudentApplicationDetailsData.company &&
                      getStudentApplicationDetailsData.company.logo_img
                        ? getStudentApplicationDetailsData.company.logo_img
                        : '/img/company-logo.png'
                    "
                    onerror="this.src='/img/company-logo.png'"
                    class="ma-auto"
                  />
                </span>
                <span
                  :class="{
                    'font-14px ml-2': smAndDown,
                    'font-16px ml-3 pt-2': mdAndUp,
                  }"
                >
                  {{
                    getStudentApplicationDetailsData.company
                      ? getStudentApplicationDetailsData.company.name
                      : '-'
                  }}
                </span>
              </div>
              <div
                :class="{
                  'mt-2': smAndDown,
                  'mt-4': mdAndUp,
                }"
              >
                <div v-for="(text, index) in textsCompany" :key="index">
                  <div class="d-flex">
                    <div
                      class="d-flex justify-center align-center text-1f2020"
                      :class="{
                        'title-container-mobile font-12px mt-2': smAndDown,
                        'title-container-desktop font-16px mt-2': mdAndUp,
                      }"
                    >
                      <span class="text-1f2020">{{ text.title }}</span>
                    </div>
                    <div
                      :class="{
                        'font-14px ml-2 pt-2': smAndDown,
                        'font-16px ml-3 pt-2': mdAndUp,
                      }"
                    >
                      {{ text.content }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- body -->
      <div
        class="mt-10"
        :class="{
          'container-history': lgAndUp,
          'container-history-mobile': mdAndDown,
        }"
      >
        <div
          v-for="(item, index) in detailApplicant"
          :key="index"
          class="no-truncate-history"
        >
          <div>
            <div class="no-truncate-history mb-6">
              <div
                class="text-b8b8b8 pr-10"
                v-if="item.isShow"
                :class="{
                  'font-18px': lgAndUp,
                  'font-14px': mdAndDown,
                }"
              >
                {{ item.label }}
              </div>
              <div
                v-if="item.isShow"
                class="d-flex justify-space-between align-center mt-1 pr-4"
              >
                <div
                  v-if="item.content && !item.isURL"
                  class="sub-label text-1f2020 text-introduction"
                  :class="{
                    'font-16px': lgAndUp,
                    'font-12px': mdAndDown,
                  }"
                >
                  {{ item.content }}
                  <div class="text-introduction">{{ item?.content2 }}</div>
                  <div class="text-introduction">{{ item?.content3 }}</div>
                </div>
                <div
                  v-if="item.content && item.isURL"
                  class="sub-label text-1f2020"
                  :class="{
                    'font-16px': lgAndUp,
                    'font-12px': mdAndDown,
                  }"
                >
                  <a
                    v-if="isUrl(item.content)"
                    class="text-blue"
                    :href="extractUrl(item.content)"
                    target="_blank"
                  >
                    {{ item.content }}
                  </a>
                </div>
                <div
                  v-if="item.content && item.isCopy"
                  class="cursor-pointer ml-4 pl-4 icon-copy"
                  @click="copyToClipboard(item.content)"
                >
                  <v-icon v-if="lgAndUp" color="primary">$CopyIcon</v-icon>
                  <v-icon v-else color="primary">$CopySmallIcon</v-icon>
                </div>
              </div>
            </div>
          </div>
          <div
            v-if="item.label === '興味のある仕事'"
            class="my-4"
            style="border-top: 2px solid #e5e5e5"
          ></div>
        </div>
      </div>
    </v-col>
  </v-row>
  <SuccessModel
    :text="alertText"
    :buttonText="`とじる`"
    :dialog="showModalCopy"
    @closeModel="showModalCopy = false"
  ></SuccessModel>
</template>
<script>
import { useStore } from 'vuex';
import { ref, computed, watch, onMounted, defineAsyncComponent } from 'vue';
import { useDisplay } from 'vuetify';
const SuccessModel = defineAsyncComponent(
  () => import('@/components/models/SuccessModel.vue')
);

export default {
  name: 'StudentApplicationDetails',
  props: {
    // Application object containing student and internship details
    application: {
      type: Object,
      default: null,
      required: true,
    },
  },
  components: {
    SuccessModel, // Modal component for showing copy success message
  },
  setup(props) {
    // Vuetify display helpers for responsive design
    const { mdAndUp, smAndDown, lgAndUp, mdAndDown } = useDisplay();
    const store = useStore();

    // Initialize array of applicant details with empty values
    // This will be populated with actual data from API
    const detailApplicant = ref([
      { label: '姓名', content: '' },
      { label: 'セイメイ', content: '' },
      { label: '学校名', content: '' },
      { label: '学部/専攻', content: '' },
      { label: '卒業予定', content: '' },
      { label: 'メールアドレス ', content: '' },
      { label: '興味のある仕事', content: '' },
      { label: '自己PR', content: '' },
      { label: '履歴書URL', content: '' },
      { label: '資格・実績', content: '' },
      {
        label:
          'セキュリティ管理について、個人的に行っていることを記載してください。',
        content: '',
      },
      {
        label: 'もっとも自信のあるデザインワークのURLを記載してください。',
        content: '',
      },
    ]);

    // State for copy to clipboard functionality
    const showModalCopy = ref(false);
    const alertText = ref('');

    // Computed property to get application details from Vuex store
    const getStudentApplicationDetailsData = computed(
      () => store.getters.getStudentApplicationDetailsData
    );

    // Company information to be displayed in the header
    const textsCompany = computed(() => [
      {
        title: '業界', // Industry
        content:
          getStudentApplicationDetailsData.value?.business_industries?.name ??
          '-',
      },
      {
        title: '職種', // Job Category
        content:
          getStudentApplicationDetailsData.value?.work_categories?.name ?? '-',
      },
    ]);

    // Computed properties for interested job names
    const textIdInterestJob1 = computed(
      () =>
        getStudentApplicationDetailsData.value.application_details
          .interested_job_name_1 || ''
    );
    const textIdInterestJob2 = computed(
      () =>
        getStudentApplicationDetailsData.value.application_details
          .interested_job_name_2 || ''
    );
    const textIdInterestJob3 = computed(
      () =>
        getStudentApplicationDetailsData.value.application_details
          .interested_job_name_3 || ''
    );

    // Format interested job display strings with additional text if available
    const intJob1 = computed(() => {
      const detail = getStudentApplicationDetailsData.value.application_details;
      return `${textIdInterestJob1.value}${
        detail.text_interested_job_1 ? ' / ' + detail.text_interested_job_1 : ''
      }`;
    });

    const intJob2 = computed(() => {
      const detail = getStudentApplicationDetailsData.value.application_details;
      return `${textIdInterestJob2.value}${
        detail.text_interested_job_2 ? ' / ' + detail.text_interested_job_2 : ''
      }`;
    });

    const intJob3 = computed(() => {
      const detail = getStudentApplicationDetailsData.value.application_details;
      return `${textIdInterestJob3.value}${
        detail.text_interested_job_3 ? ' / ' + detail.text_interested_job_3 : ''
      }`;
    });

    // Fetch application details from API
    const getData = async () => {
      await store.dispatch('GET_STUDENT_APPLICATION_DETAILS', {
        studentID:
          props.application.student?.id || props.application.student_id,
        applicationID: props.application.id,
      });
      fetchApplicantDetails();
    };

    // Process and format application details for display
    const fetchApplicantDetails = () => {
      const details = getStudentApplicationDetailsData.value;
      detailApplicant.value = [
        // Email address
        {
          label: 'メールアドレス',
          content: details.studentDetails.email_valid || '回答なし', // Show '回答なし' (No answer) if email is not available
          isShow: true,
        },
        // Full name in Kanji
        {
          label: '姓名',
          content:
            `${details.studentDetails.family_name} ${details.studentDetails.first_name}` ||
            '回答なし',
          isShow: true,
        },
        {
          label: 'セイメイ',
          content:
            `${details.studentDetails.family_name_furigana} ${details.studentDetails.first_name_furigana}` ||
            '回答なし',
          isShow: true,
        },
        {
          label: '学校名',
          content: `${details.education_facility.educationFacilityName}` || '_',
          isShow: true,
        },
        {
          label: '勉強していること',
          content:
            `${details.bigFieldData.big_field_name} / ${details.small_field.small_field_name}` ||
            '回答なし',
          isShow: true,
        },
        {
          label: '卒業予定',
          content:
            `${details.studentDetails.graduate_year || '回答なし'} 年 ${
              details.studentDetails.graduate_month
            } 月` || '回答なし',
          isShow: true,
        },
        {
          label: '興味のある仕事',
          content: intJob1.value,
          content2: intJob2.value,
          content3: intJob3.value,
          isShow:
            intJob1.value || intJob2.value || intJob3.value ? true : false, // Only show if at least one job exists
        },
        {
          label: '自己PR',
          content: details.application_details.self_introduction || '回答なし',
          isCopy: true,
          isShow: !!details.application_details.self_introduction,
        },
        {
          label: '履歴書URL',
          content:
            details.application_details.resume_url !== null
              ? `${details.application_details.resume_url}`
              : '回答なし',
          isCopy: true,
          isURL: details.application_details.resume_url !== null ? true : false,
          isShow: true,
        },
        {
          label: '資格・実績URL',
          content:
            details.application_details.qualification_url !== null
              ? `${details.application_details.qualification_url}`
              : '回答なし',
          isCopy: true,
          isURL:
            details.application_details.qualification_url !== null
              ? true
              : false,
          isShow: true,
        },
        {
          label: details?.internship_post?.required_item_prompt || '',
          content: details?.application_details?.required_item_answer,
          isCopy: true,
          isShow: !!details.application_details.required_item_answer,
        },
        {
          label: details?.internship_post?.required_url_prompt || '',
          content: details?.application_details?.required_url_answer,
          isCopy: true,
          isURL: details.application_details.required_url_answer !== null ? true : false,
          isShow: !!details.application_details.required_url_answer,
        },
      ];
    };

    // Validate if a string is a valid URL
    const isUrl = (content) => {
      const urlPattern = new RegExp(
        '^(https?:\\/\\/)?' + // protocol
          '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' + // domain name
          '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
          '(\\:\\d+)?(\\/[^\\s]*)?$', // port and path
        'i'
      );
      return urlPattern.test(content);
    };

    // Format URL with proper protocol
    const extractUrl = (content) => {
      if (isUrl(content)) {
        return content.startsWith('http') ? content : `http://${content}`;
      }
      return '#';
    };

    // Copy content to clipboard and show success message
    const copyToClipboard = (text) => {
      navigator.clipboard
        .writeText(text)
        .then(() => {
          showModalCopy.value = true;
          alertText.value = '内容をコピーしました。'; // Content copied
          setTimeout(() => {
            showModalCopy.value = false;
          }, 2000);
        })
        .catch((err) => {
          console.error('Failed to copy: ', err);
        });
    };

    // Watch for changes in application prop to reload data
    watch(
      () => props.application,
      () => {
        getData();
      }
    );

    // Initial data fetch on component mount
    onMounted(() => {
      getData();
    });

    // Expose necessary properties and methods to template
    return {
      detailApplicant,
      showModalCopy,
      textsCompany,
      intJob1,
      intJob2,
      intJob3,
      getData,
      fetchApplicantDetails,
      isUrl,
      extractUrl,
      copyToClipboard,
      getStudentApplicationDetailsData,
      smAndDown,
      mdAndUp,
      lgAndUp,
      mdAndDown,
      alertText,
    };
  },
};
</script>

<style>
.container-div {
  width: 100% !important;
}
.benefits-background {
  background-color: #f4fdfc !important;
  max-width: 590px !important;
  min-height: 200px !important;
  border-radius: 12px !important;
  .benefits-container {
    width: 100% !important;
    height: 100% !important;
  }
}
.title-container-mobile {
  width: 47px !important;
  height: 25px !important;
  background-color: #e5e5e5;
  border-radius: 5px !important;
}
.title-container-desktop {
  width: 58px !important;
  height: 31px !important;
  background-color: #e5e5e5;
  border-radius: 5px !important;
}
.email-class-container {
  max-width: 590px !important;
  max-height: 239px !important;
  .email-class {
    width: 100% !important;
  }
}
.company-log-box {
  width: 40px;
  height: 40px;
  background-color: #fff;
  img {
    max-width: 100%;
    max-height: 100%;
    border-radius: 3px;
  }
}

.no-truncate-history {
  white-space: normal;
  overflow: visible;
  text-overflow: clip;
  word-break: break-word;
}
</style>

<template>
  <div
    class="full-width white px-16 border-radius elevation bg-white"
    :class="[contracts.length ? '' : 'h-585']"
  >
    <template v-if="contracts.length === 0">
      <div
        class="full-height d-flex align-center flex-column justify-center mh-585"
      >
        <div class="text-8e8e font-14px fw-400">契約はありません</div>
      </div>
    </template>
    <template v-if="contracts.length !== 0">
      <div class="white h-585 mh-585 overflow-y-auto pt-8">
        <div v-if="!isDetail">
          <div v-if="contracts.length">
            <!-- desktop -->
            <v-expansion-panels
              v-if="mdAndUp"
              flat
              flast
              accordion
              @change="handlePanelChange"
            >
              <v-expansion-panel v-for="(contract, i) in contracts" :key="i">
                <v-expansion-panel-title class="px-0 py-0 my-0">
                  <div class="fw-500 font-18px">
                    {{ contract?.company?.name }}
                  </div>
                  <template #actions>
                    <v-icon
                      :class="{ 'rotate-icon': expandedPanels === i }"
                      color="primary"
                    >
                      $expand
                    </v-icon>
                  </template>
                </v-expansion-panel-title>
                <v-expansion-panel-text class="content-wrap">
                  <!-- need to update items -->
                  <div v-for="(item, j) in contract.contracts" :key="item.id">
                    <div
                      class="d-flex align-center justify-space-between list-item-contract mx-1"
                      :class="{ 'bg-EE6C9B26 ': item.status === 2 }"
                    >
                      <div class="d-flex align-center pl-2">
                        <div>
                          <v-chip
                            variant="flat"
                            dense
                            size="small"
                            :color="getStatusColor(item.status)"
                          >
                            <div class="font-12px text-white">
                              {{ getStatus(item.status) }}
                            </div>
                          </v-chip>
                        </div>
                        <!-- for ongoing contract  -->
                        <div
                          class="cursor-pointer ml-12 font-16px mb-2px w-150"
                          :class="{
                            'text-529AEE ': item.status !== 2,
                            'text-F1575F': item.status === 2,
                          }"
                          @click="contractDetail(item.id)"
                        >
                          {{
                            item.status !== 2
                              ? '契約内容を確認'
                              : '契約内容を確認する'
                          }}
                          <span v-if="item.status !== 2">
                            <v-icon
                              size="20px"
                              color="#529AEE"
                              style="margin-bottom: 1px"
                            >
                              mdi mdi-open-in-new
                            </v-icon>
                          </span>
                        </div>
                        <!-- for finished contract  -->
                        <div class="text-8E8E8E ml-12 font-16px mb-2px">
                          {{ item?.company?.internal_company_id }}
                        </div>
                      </div>
                      <div class="text-97 font-16px mb-2px mr-6">
                        {{ formattedDate(item?.date_employment_start) }}
                        -
                        {{ formattedDate(item?.date_employment_end) }}
                      </div>
                    </div>
                    <v-divider
                      v-if="j + 1 !== contract?.contracts?.length"
                      style="background-color: #8e8e8e"
                      class="mr-1 ml-1"
                    ></v-divider>
                  </div>
                </v-expansion-panel-text>
              </v-expansion-panel>
            </v-expansion-panels>

            <!-- mobile -->
            <div v-else>
              <v-expansion-panels flat accordion @change="handlePanelChange">
                <v-expansion-panel
                  v-for="(contract, i) in contracts"
                  :key="contract.id"
                >
                  <v-expansion-panel-title class="px-0 py-0 my-0">
                    <div class="fw-500 font-18px">
                      {{ contract?.company?.name }}
                    </div>
                    <template #actions>
                      <v-icon
                        :class="{ 'rotate-icon': expandedPanels === i }"
                        color="primary"
                      >
                        $expand
                      </v-icon>
                    </template>
                  </v-expansion-panel-title>
                  <v-expansion-panel-text class="content-wrap-mobile">
                    <div v-for="(item, j) in contract.contracts" :key="item.id">
                      <v-divider
                        v-if="j === 0"
                        style="background-color: #8e8e8e"
                      ></v-divider>
                      <div
                        class="d-flex align-center list-item-contract-mobile w-full"
                        :class="{ 'bg-EE6C9B26 ': item.status === 2 }"
                      >
                        <div class="w-full" style="width: 100%">
                          <div
                            class="text-97 d-flex justify-space-between font-12px mb-2px w-full"
                          >
                            <div class="w-full">
                              {{ formattedDate(date_employment_start) }}
                              -
                              {{ formattedDate(date_employment_end) }}
                            </div>
                            <div
                              class="text-8E8E8E ml-12 font-14px mb-2px w-full"
                            >
                              {{ item?.company?.internal_company_id }}
                            </div>
                          </div>
                          <div class="d-flex align-center mt-1 pl-2">
                            <v-chip
                              variant="flat"
                              dense
                              size="small"
                              :color="getStatusColor(item.status)"
                            >
                              <div class="font-12px text-white">
                                {{ getStatus(item.status) }}
                              </div>
                            </v-chip>
                            <div
                              class="cursor-pointer ml-6 font-14px mb-2px w-150"
                              :class="{
                                'text-529AEE ': item.status !== 2,
                                'text-F1575F': item.status === 2,
                              }"
                              @click="contractDetail(item.id)"
                            >
                              {{
                                item.status !== 2
                                  ? '契約内容を確認'
                                  : '契約内容を確認する'
                              }}
                              <span v-if="item.status !== 2">
                                <v-icon
                                  size="18px"
                                  color="#529AEE"
                                  style="margin-bottom: 1px"
                                >
                                  mdi mdi-open-in-new
                                </v-icon>
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <v-divider
                        v-if="j + 1 !== contract?.contracts?.length"
                        style="background-color: #8e8e8e"
                      ></v-divider>
                    </div>
                  </v-expansion-panel-text>
                </v-expansion-panel>
              </v-expansion-panels>
            </div>
          </div>
        </div>
        <ContractDetail
          :id="idContract"
          v-if="isDetail"
          @hide="isDetail = false"
        ></ContractDetail>
      </div>
    </template>
  </div>
</template>
<script>
/**
 * Student Contract Management Component
 * Handles display and management of student contracts with companies
 */
import { ref, computed, onMounted } from 'vue';
import { useStore } from 'vuex';
import ContractDetail from './contractDetail.vue';
import moment from 'moment';
import { useDisplay } from 'vuetify';
import { useRouter, useRoute } from 'vue-router';

export default {
  components: {
    ContractDetail,
  },
  setup() {
    const route = useRoute();
    const store = useStore();
    const { mdAndUp, smAndDown } = useDisplay(); // Responsive display utilities

    /**
     * Reactive State Management
     */
    const expandedPanels = ref(null); // Tracks expanded accordion panels
    const selectedContractMobile = ref('株式会社Airbnb'); // Selected company in mobile view
    const isDetail = ref(false); // Controls detail view visibility
    const idContract = ref(null); // Currently selected contract ID
    const getAllContractsStudent = computed(
      () => store.getters.getAllContractsStudent
    );

    /**
     * Formats date to Japanese format (YYYY年M月D日)
     * @param {string} item - Date string to format
     * @returns {string} Formatted date
     */
    const formattedDate = (item) => {
      return moment(item).format('YYYY年M月D日');
    };

    /**
     * Groups contracts by company
     * Creates a hierarchical structure of companies and their contracts
     */
    const contracts = computed(() => {
      const groupedByCompany = getAllContractsStudent.value.reduce(
        (acc, contract) => {
          const { company_id, status, company } = contract;

          // Initialize company group if not exists
          if (!acc[company_id]) {
            acc[company_id] = { company, contracts: [] };
          }
          acc[company_id].contracts.push(contract);

          return acc;
        },
        {}
      );

      // Transform grouped data into array format
      return Object.keys(groupedByCompany).map((company_id) => ({
        company_id: parseInt(company_id),
        company: groupedByCompany[company_id].company,
        contracts: groupedByCompany[company_id].contracts,
      }));
    });

    /**
     * Fetch initial contract data on component mount
     */
    onMounted(async () => {
      await store.dispatch('CONTRACT_GET_ALL_STUDENT', route.params.id);
    });

    /**
     * Event Handlers and Utility Methods
     */
    // Handles expansion panel state changes
    const handlePanelChange = (panelIndex) => {
      expandedPanels.value = panelIndex;
    };

    // Formats date string to YYYY/MM/DD format
    const formatDate = (dateString) => {
      if (!dateString) return '';
      return dateString.split(' ')[0].replace(/-/g, '/');
    };

    /**
     * Returns color code based on contract status
     * @param {number} status - Contract status code
     * @returns {string} Hex color code
     */
    const getStatusColor = (status) => {
      switch (status) {
        case 1:
          return '#8B8000'; // Contract in preparation (黄色)
        case 2:
          return '#EE6C9B'; // Awaiting confirmation (ピンク)
        case 3:
          return '#60D1CB'; // Confirmed (ティール)
        case 4:
          return '#A7A7A7'; // Contract expired (グレー)
      }
    };

    /**
     * Returns status text based on contract status
     * @param {number} status - Contract status code
     * @returns {string} Status text in Japanese
     */
    const getStatus = (status) => {
      switch (status) {
        case 1:
          return '契約準備中  '; // Contract in preparation
        case 2:
          return '確認待ち'; // Awaiting confirmation
        case 3:
          return '確認済み'; // Confirmed
        case 4:
          return '契約満了'; // Contract expired
      }
    };

    /**
     * Toggles contract detail view
     * @param {number} id - Contract ID to display
     */
    const contractDetail = (id) => {
      isDetail.value = !isDetail.value;
      idContract.value = id;
    };

    // Placeholder for future text change handler
    const textChange = (event) => {};

    // Return exposed methods and variables
    return {
      expandedPanels,
      selectedContractMobile,
      isDetail,
      idContract,
      contracts,
      handlePanelChange,
      formatDate,
      getStatusColor,
      getStatus,
      contractDetail,
      textChange,
      smAndDown,
      mdAndUp,
      formattedDate,
    };
  },
};
</script>

<style scoped>
.text-white {
  color: white !important;
}

.w-150 {
  width: 150px;
}

.mx-1 {
  margin: 2px 0 2px 0;
}

.chip-contract {
  margin-left: 32px;
  min-width: 84px;
  max-height: 20px !important;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.chip-contract-mobile {
  width: 84px;
  max-height: 20px !important;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}
.red-dot {
  width: 12px;
  height: 12px;
  background-color: #f1575f;
  border-radius: 100%;
  margin-right: 4px;
  margin-left: 20px;
}
.list-item-contract {
  height: 60px;
}
.list-item-contract-mobile {
  height: 70px !important;
  padding: 0 !important;
  margin: 1px 0 1px 0 !important;
  width: 100% !important;
}
.list-item-contract-detail-mobile {
  min-height: 102px;
}
.border-b {
  border-bottom: 1px solid #e5e5e5;
}

/* More specific selector overide vuetify classes */
:deep(.content-wrap .v-expansion-panel-content__wrap) {
  padding: 0px 48px !important;
  flex: 1 1 auto;
  max-width: 100%;
}

/* More specific selector overide vuetify classes */
:deep(.content-wrap-mobile .v-expansion-panel-content__wrap) {
  padding: 0 !important;
  flex: 1 1 auto;
  max-width: 100%;
}

.selector-contract {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 170px;
  height: 38px;
  color: black;
  border: 1px solid #13aba3;
  border-radius: 24px; /* Optional: Add border radius if desired */
  padding: 0 10px; /* Optional: Add padding for better appearance */
  transition:
    background-color 0.3s,
    color 0.3s; /* Smooth transition for active state */
}

.selector-contract:active,
.selector-contract:focus {
  background-color: #13aba3;
  color: white;
}

.v-sheet.v-list {
  border-top-left-radius: 24px; /* Adjust the radius as needed */
  border-top-right-radius: 24px; /* Adjust the radius as needed */
  overflow: hidden; /* Ensures child elements are constrained by border radius */
}

.rotate-icon {
  margin-bottom: 6px;
  transform: rotate(180deg) translateY(-5px);
}

.expansion-header {
  border-top: 1px solid #8e8e8e; /* Add gray border at the bottom */
}
</style>

<template>
  <v-dialog
    v-model="dialogModel"
    :max-width="790"
    :style="{
      maxHeight: display.mdAndUp ? 'auto' : '432px',
    }"
    class="no-shadow"
  >
    <v-card class="pa-15">
      <v-card-text class="pb-5">
        <template v-if="Array.isArray(props.text)">
          <div
            v-for="(tt, index) in props.text"
            :key="index"
            class="my-1 text-center d-flex justify-center align-center text-333"
            :class="[index == 0 ? 'font-20px mb-7' : 'font-14px']"
            v-html="tt"
          ></div>
        </template>
        <template v-else>
          <div
            class="my-1 d-flex justify-center align-center text-333 font-20px"
            v-html="props.text"
          ></div>
        </template>

        <div
          :class="{
            'py-4': optionEmail === 2,
            'mb-8': optionEmail === 2,
          }"
        >
          <v-radio-group
            v-model="optionEmail"
            density="compact"
            inline
            class="custom-radio d-flex justify-center mt-4"
          >
            <v-radio
              v-for="option in options"
              :key="option.id"
              :label="option.name"
              color="primary"
              :value="option.id"
              class="pb-2 flex-grow-0 mr-4"
            ></v-radio>
          </v-radio-group>
        </div>

        <!-- Email header -->
        <div
          v-if="optionEmail === 1"
          class="email-header my-4 pa-4"
          style="margin-top: -20px"
        >
          <v-row>
            <v-col>
              <div>
                <strong>To:</strong>
                {{ props.selectedItem?.student?.email_valid }}
              </div>
              <div><strong>From:</strong> <EMAIL></div>
              <div>
                <strong>BCC: </strong>
                {{
                  props.selectedItem?.internship_post?.contactable_user1_email
                }}
                {{
                  props.selectedItem?.internship_post?.contactable_user2_email
                    ? ','
                    : ''
                }}
                {{
                  props.selectedItem?.internship_post?.contactable_user2_email
                }}
                {{
                  props.selectedItem?.internship_post?.contactable_user3_email
                    ? ','
                    : ''
                }}
                {{
                  props.selectedItem?.internship_post?.contactable_user3_email
                }}
                {{
                  props.selectedItem?.internship_post?.contactable_user4_email
                    ? ','
                    : ''
                }}
                {{
                  props.selectedItem?.internship_post?.contactable_user4_email
                }}
                {{
                  props.selectedItem?.internship_post?.contactable_user5_email
                    ? ','
                    : ''
                }}
                {{
                  props.selectedItem?.internship_post?.contactable_user5_email
                }}
              </div>
              <div>
                <strong>件名:</strong> {{ props.selectedItem?.company?.name }} /
                長期オンラインインターン合格のお知らせ
              </div>
            </v-col>
          </v-row>
        </div>

        <!-- Message body -->
        <div v-if="optionEmail === 1" class="mb-4 email-body pa-4">
          <v-row>
            <v-col>
              <div class="my-1">
                {{ props.selectedItem?.student?.education_facility?.name }}
                {{ props.selectedItem?.student?.family_name }}
                {{ props.selectedItem?.student?.first_name }} 様 <br /><br />
                こんにちは。コトナル事務局です。<br />
                コトナルをご利用いただき、誠にありがとうございます。<br /><br />
                企業様にて、応募内容を慎重に検討した結果、誠に残念ではございますが、<br />
                不合格となりましたので、ご連絡します。<br /><br />
                会社名：{{ props.selectedItem?.company?.name }}<br />
                求人名：{{ props.selectedItem?.internship_post?.title
                }}<br /><br />

                大変恐縮ではございますが、ご理解くださいますようお願い申し上げます。<br />
                末筆ではございますが、これからのご活躍をお祈り申し上げます。<br />
              </div>
            </v-col>
          </v-row>
        </div>
      </v-card-text>

      <v-card-actions
        :class="{ 'mt-10': optionEmail === 2 }"
        class="justify-center"
      >
        <v-btn
          v-if="!props.buttonOption.hideCancel"
          variant="outlined"
          elevation="5"
          color="primary"
          min-width="188"
          class="mr-6"
          @click="handleCancel"
        >
          {{ props.sentButtonText }}
        </v-btn>
        <v-btn
          v-if="!props.buttonOption.hideSubmit"
          variant="flat"
          color="primary"
          class="white--text modal-submit"
          min-width="188"
          @click="handleSubmit"
          :loading="props.loading"
        >
          {{ optionEmail === 1 ? props.notSendButtonText : '変更する' }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useDisplay } from 'vuetify';

const display = useDisplay();

const props = defineProps({
  dialog: {
    type: Boolean,
    default: false,
    required: true,
  },
  text: {
    type: [String, Array],
    default: '',
    required: true,
  },
  selectedItem: {
    type: Object,
    default: null,
    required: false,
  },
  sentButtonText: {
    type: String,
    default: '戻る',
    required: false,
  },
  notSendButtonText: {
    type: String,
    default: 'メールを送信する',
    required: false,
  },
  loading: {
    type: Boolean,
    default: false,
    required: false,
  },
  status: {
    type: Number,
    default: 0,
    required: false,
  },
  buttonOption: {
    type: Object,
    default: () => ({
      hideSubmit: false,
      hideCancel: false,
    }),
    required: false,
  },
});

const emit = defineEmits(['closeModel', 'update', 'back', 'submitSuccess']);

const optionEmail = ref(1);
const options = ref([
  { name: '送る', id: 1 },
  { name: '送らない', id: 2 },
]);

const dialogModel = computed({
  get: () => props.dialog,
  set: (value) => {
    if (!value) {
      emit('closeModel');
    }
  },
});

const handleSubmit = () => {
  emit('submitSuccess', {
    status: props.status,
    emailOption: optionEmail.value,
  });
};

const handleCancel = () => {
  emit('closeModel', false);
};
</script>

<style lang="scss">
.custom-radio {
  width: 100%;
}

.email-header.my-4.pa-4,
.mb-4.email-body.pa-4 {
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.875rem;
}

.modal-submit {
  box-shadow: 0px 4px 4px rgb(0 0 0 / 25%);
  border-radius: 3px;
}

.email-header {
  border: 1px dashed gray;
}

.email-body {
  border: 1px solid gray;
}

.custom-radio {
  .v-radio {
    margin-right: 2px;
  }

  .v-label {
    font-size: 14px !important;
    color: rgba(0, 0, 0, 0.87) !important;
  }
}

// Simplified talk bubble styles for Vuetify 3
.talk-bubble {
  margin: 60px;
  display: inline-block;
  position: relative;
  width: 270px;
  height: auto;
  background-color: #fef445;
  border-radius: 10px;

  &.border-bubble {
    border: 1px solid #666;
  }

  &.tri-right.btm-left-in {
    &:before {
      content: '';
      position: absolute;
      width: 0;
      height: 0;
      left: 37px;
      bottom: -18px;
      border: 18px solid transparent;
      border-top-color: #000000;
      border-right-width: 40px;
    }

    &:after {
      content: '';
      position: absolute;
      width: 0;
      height: 0;
      left: 38px;
      bottom: -17px;
      border: 18px solid transparent;
      border-top-color: #fef445;
      border-right-width: 39px;
    }
  }
}

.talktext {
  padding: 1em;
  text-align: left;
  line-height: 1.5em;

  p {
    margin: 0;
  }
}
</style>

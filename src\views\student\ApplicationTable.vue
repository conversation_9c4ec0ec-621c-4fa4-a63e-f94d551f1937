<template>
  <div class="full-width white border-radius elevation bg-white">
    <div>
      <AlreadyAppliedAndPassedApplicationTable />
    </div>
    <div>
      <ApplicationWithAllOtherStatus />
    </div>
  </div>
</template>
<script>
import AlreadyAppliedAndPassedApplicationTable from '@/views/student/applicationTableComponent/ApplicationWithAlreadyAppliedAndPassedStatus.vue';
import ApplicationWithAllOtherStatus from '@/views/student/applicationTableComponent/ApplicationWithAllOtherStatus.vue';
export default {
  components: {
    AlreadyAppliedAndPassedApplicationTable,
    ApplicationWithAllOtherStatus,
  },
};
</script>

<template>
  <v-row>
    <v-col col="12" md="12">
      <div class="search-section">
        <div
          v-if="toggleSearch !== null"
          class="d-flex align-center justify-space-between full-width"
          @click="toggleSearchState"
        >
          <div>検索</div>
          <v-btn
            variant="text"
            color="transparent"
            text
            @click.stop="toggleSearchState"
          >
            <span class="small-line"></span>
          </v-btn>
        </div>
        <div
          v-else
          class="d-flex align-center justify-space-between full-width"
        >
          <div>検索</div>
        </div>
        <div class="separator"></div>
        検索条件
        <div class="search-bar-and-button">
          <div class="d-flex align-center justify-center">
            <v-sheet color="transparent" width="463px">
              <v-text-field
                variant="outlined"
                density="compact"
                v-model="search"
                :placeholder="searchPlaceholder"
                :hide-details="true"
                @keyup.enter="emitSearch"
              ></v-text-field>
            </v-sheet>
          </div>
          <v-btn color="primary" @click="emitSearch"> 検索 </v-btn>
        </div>
      </div>
    </v-col>
  </v-row>
</template>

<script>
import { ref, computed } from 'vue';

export default {
  name: 'SearchSection',
  props: {
    toggleSearch: {
      type: Boolean,
      default: null,
      required: false,
    },
    searchPlaceholder: {
      type: String,
      default: '企業ID、企業名、企業名カナ',
      required: false,
    },
  },
  setup(props, { emit }) {
    // State variables
    const search = ref('');
    const ownToggleSearch = computed({
      get: () => props.toggleSearch,
      set: (value) => emit('toggleSearch', value),
    });

    // Methods
    const toggleSearchState = () => {
      ownToggleSearch.value = !ownToggleSearch.value;
    };

    const emitSearch = () => {
      emit('search-table', search.value.trim());
    };

    // Expose to the template
    return {
      search,
      ownToggleSearch,
      toggleSearchState,
      emitSearch,
    };
  },
};
</script>

<style src="@/styles/forms.scss" lang="scss"></style>
<style lang="scss" scoped>
.search-section {
  background: #ffffff;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  padding: 15px;
  color: #333;
  font-size: 14px;

  .separator {
    border: 1px solid #b8b8b8;
    margin-top: 12px;
    margin-bottom: 20px;
  }

  .search-bar-and-button {
    margin-top: 10px;
    display: flex;
    justify-content: space-between;

    button {
      color: #fff;
    }
  }
}
</style>

<template>
  <div>
    <ActionAlertCard
      v-if="messageFeedback"
      class="text-center"
      :message="messageFeedback"
      width="610px"
      height="190px"
      type="success"
      :style="{
        position: 'fixed',
        margin: 'auto',
        left: '0',
        right: '0',
      }"
    />
    <v-row>
      <v-col cols="12">
        <v-card class="text-center py-16 mt-2">
          <div class="button-width mx-auto">
            <div class="btn-container px-2">
              <v-btn
                block
                style="text-transform: uppercase"
                class="white--text button-width"
                type="button"
                @click="testFeedback"
                color="#13ABA3"
                >Test Feedback</v-btn
              >
              <v-btn
                block
                class="white--text button-width mt-4 px-4"
                type="button"
                @click="removeFeedback"
                color="#ff0000"
                >TEST REMOVE OLD FEEDBACK</v-btn
              >
              <v-btn
                block
                style="text-transform: uppercase"
                class="white--text button-width mt-4 px-4"
                type="button"
                @click="closeContract"
                color="#ff0000"
                >Close Finished contract</v-btn
              >
              <v-btn
                block
                style="text-transform: uppercase"
                class="white--text button-width mt-4 px-4"
                type="button"
                @click="removeInternship()"
                color="#ff0000"
                >Remove finished internship student record </v-btn
              >
              <v-btn
                block
                style="text-transform: uppercase"
                class="white--text button-width mt-4 px-4"
                type="button"
                @click="companyExportUser()"
                color="orange"
                >Company User CSV Export</v-btn
              >
            </div>
          </div>
        </v-card>
      </v-col>
    </v-row>
  </div>
</template>

<script>
import axios from '@/services/api';
import Encoding from 'encoding-japanese';
import ActionAlertCard from '@/components/ui/ActionAlertCard.vue';

export default {
  name: 'TestPage',
  components: {
    ActionAlertCard
  },
  data() {
    return {
      messageFeedback: '',
    };
  },
  created() {},
  methods: {
    testFeedback() {
      return new Promise((resolve, reject) => {
        axios
          .get(`/admin/current-month-feedback`)
          .then((response) => {
            let data = response.data.data;
            this.messageFeedback = response.data.message;
            resolve(response);
            setTimeout(() => {
              this.messageFeedback = '';
            }, 5000);
          })
          .catch((err) => {
            this.messageFeedback = response.data.message;
            setTimeout(() => {
              this.messageFeedback = '';
            }, 5000);
            reject(err);
          });
      });
    },
    removeFeedback() {
      axios
        .get(`/admin/remove-old-feedback`)
        .then((response) => {
          let data = response.data.data;
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        });
    },
    closeContract() {
      axios
        .get(`/admin/update-finished-contract-status`)
        .then((response) => {
          let data = response.data.data;
          this.messageFeedback = response.data.message;
          setTimeout(() => {
            this.messageFeedback = '';
          }, 5000);
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        });
    },

    removeInternship() {
      axios
        .get(`/admin/intership-student/soft-delete`)
        .then((response) => {
          let data = response.data.data;
          this.messageFeedback = response.data.message;
          setTimeout(() => {
            this.messageFeedback = '';
          }, 5000);
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        });
    },

    async companyExportUser() {
      await this.$store.dispatch('COMPANY_USER_EXPORT_CSV');

      const csvData = this.$store.getters.getCompanyUserCsvData.csv;

      // Convert UTF-8 CSV to Shift-JIS
      const sjisArray = Encoding.convert(Encoding.stringToCode(csvData), {
        to: 'SJIS',
        from: 'UNICODE',
      });

      const uint8Array = new Uint8Array(sjisArray);
      const fileUrl = window.URL.createObjectURL(
        new Blob([uint8Array], { type: 'text/csv;charset=Shift_JIS' })
      );

      const fileLink = document.createElement('a');
      fileLink.href = fileUrl;
      fileLink.setAttribute(
        'download',
        `契約管理_${new Date().toISOString().slice(0, 10)}.csv`
      );
      document.body.appendChild(fileLink);
      fileLink.click();
      document.body.removeChild(fileLink);
    },
  },
};
</script>

<style lang="scss" src="./style.scss" scoped></style>

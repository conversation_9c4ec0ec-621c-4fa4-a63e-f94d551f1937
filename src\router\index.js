import { createRouter, createWebHistory } from 'vue-router';
import store from '@/store/index.js';
import { createMetaManager } from 'vue-meta';

import Layout from '@/components/admin/layout/Layout.vue';

// Use Vue Meta in Vue 3
const metaManager = createMetaManager({
  refreshOnceOnNavigation: true,
});

// Define routes
const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: {
      public: true,
      guest: true,
    },
  },
  {
    path: '/forgot-password',
    name: 'ForgotPassword',
    component: () => import('@/views/auth/ForgotPassword.vue'),
    meta: {
      public: true,
      guest: true,
    },
  },
  {
    path: '/update-password',
    name: 'ResetPassword',
    component: () => import('@/views/auth/ResetPassword.vue'),
    meta: {
      public: true,
      guest: true,
    },
  },
  {
    path: '/privacy',
    name: 'Privacy',
    component: () => import('@/views/Privacy/privacy.vue'),
    meta: {
      public: true,
      guest: true,
    },
  },
  {
    path: '/terms-and-conditions',
    name: 'TermsAndConditions',
    component: () => import('@/views/TermsAndConditions/terms.vue'),
    meta: {
      public: true,
      guest: true,
    },
  },
  {
    path: '/',
    redirect: 'login',
    name: 'Layout',
    component: Layout,
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/Dashboard.vue'),
      },
      {
        path: '/feedback',
        name: 'Feedbacks',
        component: () => import('@/views/feedback/index.vue'),
        meta: { title: 'フィードバック - 一覧' },
      },
      {
        path: '/unaddress-feedback',
        name: 'UnaddressFeedbacks',
        component: () => import('@/views/feedback/index-unaddressed.vue'),
        meta: { title: 'フィードバック - 今月未対応' },
      },
      {
        path: '/feedback-create/:company_id?/:student_id?',
        name: 'AddressedFeedbackCreate',
        component: () => import('@/views/feedback/createOrEdit.vue'),
        meta: { title: 'フィードバック - 新規作成' },
      },
      {
        path: '/feedback-edit/:id',
        name: 'AddressedFeedbackEdit',
        component: () => import('@/views/feedback/createOrEdit.vue'),
        meta: { title: 'フィードバック - 新規作成' },
      },
      {
        path: '/unaddress-feedback-edit/:id',
        name: 'UnaddressFeedbackEdit',
        component: () => import('@/views/feedback/createOrEditUnaddress.vue'),
        meta: { title: 'フィードバック - 新規作成' },
      },
      {
        path: '/students',
        name: 'Students',
        component: () => import('@/views/student/index.vue'),
        meta: { title: '学生 - 学生一覧' },
      },
      {
        path: '/applications',
        name: 'Applications',
        component: () => import('@/views/application/index.vue'),
        meta: { title: '応募管理' },
      },
      {
        path: '/student-profile/:id',
        name: 'StudentProfile',
        component: () => import('@/views/student/profile.vue'),
        meta: { title: '学生 - 学生詳細' },
      },
      {
        path: '/student-application-details/:studentID/:applicationID',
        name: 'StudentApplicationDetails',
        component: () =>
          import('@/views/student/StudentDetailsView/StudentTabs.vue'),
        meta: { title: '学生 - 学生詳細' },
      },
      {
        path: '/student-register',
        name: 'StudentCreate',
        component: () => import('@/views/users/createOrEdit.vue'),
        meta: { title: '学生 - 新規作成' },
      },
      {
        path: '/corporate',
        name: 'Corporate',
        component: () => import('@/views/corporate/index.vue'),
        meta: { title: '企業管理 - 一覧' },
      },
      {
        path: '/corporate-register',
        name: 'CorporateCreate',
        component: () => import('@/views/corporate/create.vue'),
        meta: { title: '企業管理 - 企業新規作成' },
      },
      {
        path: '/corporate-edit/:id',
        name: 'CorporateEdit',
        component: () => import('@/views/corporate/edit.vue'),
        meta: { title: '企業管理 - 編集' },
      },
      {
        path: '/corporate-details/:id',
        name: 'CorporateDetails',
        component: () => import('@/views/corporate/details.vue'),
        meta: { title: '企業管理 - 詳細' },
      },
      {
        path: '/internship-create',
        name: 'InternshipPostCreate',
        component: () => import('@/views/internship/create.vue'),
        meta: { title: '求人広告 - 新規作成' },
      },
      {
        path: '/internship-edit/:id',
        name: 'InternshipPostEdit',
        component: () => import('@/views/internship/edit.vue'),
        meta: { title: '求人広告 - 編集' },
      },
      {
        path: '/internship-list',
        name: 'InternshipPostList',
        component: () => import('@/views/internship/list.vue'),
        meta: { title: '求人広告 - 一覧' },
      },
      {
        path: '/internship-draft',
        name: 'InternshipPostDraftList',
        component: () => import('@/views/internship/draft.vue'),
        meta: { title: '求人広告 - 下書き' },
      },
      {
        path: '/work-categories',
        name: 'WorkCategories',
        component: () => import('@/views/internship/occupation/index.vue'),
        meta: { title: '求人広告 - 職種管理' },
      },
      {
        path: '/internship-features',
        name: 'InternshipFeatures',
        component: () => import('@/views/internship/feature/index.vue'),
        meta: { title: '求人広告 - 特徴管理' },
      },
      {
        path: '/chat',
        name: 'Chat',
        component: () => import('@/views/chats/index.vue'),
        meta: { title: 'チャット - チャット一覧' },
      },
      {
        path: '/chat-details/:id',
        name: 'ChatDetails',
        component: () => import('@/views/chats/details.vue'),
        meta: { title: 'チャット - チャット詳細' },
      },
      {
        path: '/media',
        name: 'Media',
        component: () => import('@/views/media-post/index.vue'),
        meta: { title: 'コラム - 一覧' },
      },
      {
        path: '/media-tags',
        name: 'MediaTags',
        component: () => import('@/views/media-post/tags.vue'),
        meta: { title: 'コラム - タグ管理' },
      },
      {
        path: '/media-post-create',
        name: 'CreateMediaPost',
        component: () => import('@/views/media-post/create.vue'),
        meta: { title: 'コラム - 編集' },
      },
      {
        path: '/media-post-edit/:id',
        name: 'EditMediaPost',
        component: () => import('@/views/media-post/edit.vue'),
        meta: { title: 'コラム - 一覧' },
      },

      {
        path: '/notifications',
        name: 'Notifications',
        component: () => import('@/views/notifications/index.vue'),
        meta: { title: 'お知らせ管理 - お知らせ一覧' },
      },
      {
        path: '/notification-register',
        name: 'NotificationsCreate',
        component: () => import('@/views/notifications/create.vue'),
        meta: { title: 'お知らせ管理 - お知らせ編集' },
      },
      {
        path: '/notifications/:id',
        name: 'NotificationsEdit',
        component: () => import('@/views/notifications/edit.vue'),
        meta: { title: 'お知らせ管理 - お知らせ編集' },
      },
      {
        path: '/faqs',
        name: 'FAQs',
        component: () => import('@/views/faqs/index.vue'),
        meta: { title: 'FAQ管理 - FAQ一覧' },
      },
      {
        path: '/faq-register',
        name: 'FAQCreate',
        component: () => import('@/views/faqs/create.vue'),
        meta: { title: 'FAQ管理 - FAQ編集' },
      },
      {
        path: '/edit-faq/:id',
        name: 'FAQEdit',
        component: () => import('@/views/faqs/edit.vue'),
        meta: { title: 'FAQ管理 - FAQ編集' },
      },
      {
        path: '/educational_facilities',
        name: 'educational_facilities',
        component: () => import('@/views/educational_facilities/index.vue'),
        meta: { title: '教育機関' },
      },
      {
        path: '/admin-register',
        name: 'AdminsCreate',
        component: () => import('@/views/admin/create.vue'),
        meta: { title: '管理者 - 管理者登録' },
      },
      {
        path: '/admin-listing',
        name: 'AdminListing',
        component: () => import('@/views/admin/listing.vue'),
        meta: { title: '管理者 - 管理者登録' },
      },
      {
        path: '/admin-edit/:id',
        name: 'AdminsEdit',
        component: () => import('@/views/admin/edit.vue'),
        meta: { title: '管理者 - 管理者編集' },
      },
      {
        path: '/interested_job',
        name: 'InterestedJob',
        component: () => import('@/views/interested_job/index.vue'),
        meta: { title: '興味のある仕事' },
      },
      {
        path: '/contract',
        name: 'Contract',
        component: () => import('@/views/contract/contracts.vue'),
        meta: { title: '' },
      },
      {
        path: '/contract-detail/:id',
        name: 'ContractDetail',
        component: () => import('@/views/contract/contractsDetail.vue'),
        meta: { title: '労働条件通知書編集' },
      },
      {
        path: '/uncontract-detail/:id',
        name: 'UncontractDetail',
        component: () => import('@/views/contract/contractsDetail.vue'),
        meta: { title: '労働条件通知書編集' },
      },
      {
        path: '/contract-history',
        name: 'ContractHistory',
        component: () => import('@/views/contract/histories.vue'),
        meta: { title: '履歴' },
      },
      {
        path: '/uncontracted',
        name: 'Uncontracted',
        component: () => import('@/views/contract/uncontracteds.vue'),
        meta: { title: '未締結' },
      },
      {
        path: '/template-management',
        name: 'TemplateManagement',
        component: () => import('@/views/contractTemplate/list.vue'),
        meta: { title: 'テンプレート管理' },
      },
      {
        path: '/create-contract-template',
        name: 'CreateContractTemplate',
        component: () => import('@/views/contractTemplate/create.vue'),
        meta: { title: 'テンプレート管理' },
      },
      {
        path: '/edit-contract-template/:id',
        name: 'EditContractTemplate',
        component: () => import('@/views/contractTemplate/edit.vue'),
        meta: { title: 'テンプレート管理' },
      },
      {
        path: '/internship-student',
        name: 'InternshipStudent',
        component: () => import('@/views/internship_student/list.vue'),
        meta: { title: 'インターン生管理' },
      },
      {
        path: '/notification-management',
        name: 'NotificationManagement',
        component: () => import('@/views/notification_management/create.vue'),
        meta: { title: 'お知らせ管理 - 編集' },
      },
      {
        path: '/test',
        name: 'TestPage',
        component: () => import('@/views/Test/index.vue'),
        meta: { title: 'Test' },
      },
      {
        path: '/feedback-student/:id',
        name: 'FeedbackStudent',
        component: () => import('@/views/internship_student/feedback.vue'),
        meta: { title: 'インターン生管理' },
      },
      {
        path: '/analysis-from-others',
        name: 'AnalysisFromOthers',
        component: () => import('@/views/analysis-from-others/index.vue'),
        meta: { title: '他己分析管理 - 一覧' },
      },
      {
        path: '/analysis-from-others/detail/:id',
        name: 'AnalysisFromOthersDetail',
        component: () => import('@/views/analysis-from-others/detail.vue'),
        meta: { title: '他己分析管理 - 詳細' },
      },
    ],
  },
  // Updated catch-all route to prevent deprecated use of '*'
  {
    path: '/error-403',
    name: '403',
    component: () => import('@/views/error/Error403.vue'),
    meta: { title: '403 Forbidden' },
  },
  {
    path: '/:catchAll(.*)',
    name: 'Error',
    component: () => import('@/views/error/Error404.vue'),
    meta: { title: '404 Not Found' },
  },
];

// Router setup
const router = createRouter({
  history: createWebHistory(), // Using 'createWebHistory' for history mode
  routes, // Routes should be imported or defined above
});

// Auth guard
router.beforeEach((to, from, next) => {
  const authRequired = !to.matched.some((record) => record.meta.public);
  const loggedIn = store.getters.isLoggedIn;

  // Redirect logged-in users to the dashboard if they try to access the base URL or a guest route
  if (loggedIn && !authRequired && (to.path === '/login' || to.path === '')) {
    next('/dashboard');
  } else if (authRequired && !loggedIn) {
    // If the route requires authentication and the user is not logged in, redirect to login
    next({
      path: '/login',
      query: { redirect: to.fullPath },
    });
  } else {
    // Proceed to the intended route in other cases
    next();
  }
});

// Meta title handling
router.afterEach((to) => {
  if (to.meta.title !== undefined) {
    document.title =
      to.meta.title !== '' ? `${to.meta.title} | KOTONARU` : 'KOTONARU';
  }
});

export default router;

<template>
  <div>
    <PageTitle
      v-show="!loading && !$store.getters.getApiProcessingStatus"
      :items="{
        title: '求人広告',
        subTitle: '職種管理',
        buttons: [
          {
            title: '詳細条件検索',
            action: () => {
              toggleSearch = true;
            },
            class: 'bg-white',
            variant: 'outlined',
            others: {
              outlined: true,
            },
          },
          {
            title: '新規作成',
            icon: 'mdi-plus-circle',
            action: () => {
              launchNew = true;
            },
          },
        ],
      }"
    ></PageTitle>

    <v-fade-transition>
      <SearchBox
        class="mb-5"
        searchPlacholder="プルダウン表示項目"
        :toggleSearch="toggleSearch"
        @toggleSearch="toggleSearch = false"
        @search-table="searchTable"
        v-if="toggleSearch"
      ></SearchBox>
    </v-fade-transition>
    <DataTable
      v-show="!loading && !$store.getters.getApiProcessingStatus"
      class="school-table"
      :headers="headers"
      :items="getAllInternshipOccupations"
      :total-records="totalRecords"
      :number-of-pages="totalPages"
      @update:options="updateTable"
      @row-clicked="handleRowClick"
    >
      <template v-slot:item.delete="{ item }">
        <v-btn
          variant="text"
          color="transparent"
          @click.stop="deleteInitiate(item.id)"
        >
          <v-icon size="20">$delete</v-icon>
        </v-btn>
      </template>
    </DataTable>

    <OccupationFeatureDialog
      v-model:launch="launchNew"
      :edit="false"
      @submit="submit"
      @refresh="getDataFromApi"
    ></OccupationFeatureDialog>

    <OccupationFeatureDialog
      v-model:launch="launchEdit"
      :item="editItem"
      @update="update"
      @refresh="getDataFromApi"
    ></OccupationFeatureDialog>

    <SimpleModel
      text="この職種を削除してよろしいですか？"
      :dialog="dialog.delete"
      @submitSuccess="deleteInternOccupation"
      :submitButtonText="'削除する'"
      @closeModel="dialog.delete = false"
    ></SimpleModel>
    <SuccessModel
      :text="alertText"
      :buttonText="`とじる`"
      :routeName="routeName"
      :dialog="successDialog"
      @closeModel="successDialog = false"
    >
    </SuccessModel>
  </div>
</template>

<script>
import { ref, computed, onMounted, defineAsyncComponent } from 'vue';
const DataTable = defineAsyncComponent(
  () => import('@/components/ui/DataTable.vue')
);
const OccupationFeatureDialog = defineAsyncComponent(
  () => import('@/components/models/OccupationFeatureDialog.vue')
);
const SimpleModel = defineAsyncComponent(
  () => import('@/components/models/SimpleModel.vue')
);
const SearchBox = defineAsyncComponent(
  () => import('@/components/ui/SearchBox.vue')
);
const SuccessModel = defineAsyncComponent(
  () => import('@/components/models/SuccessModel.vue')
);
import { useStore } from 'vuex';

export default {
  components: {
    DataTable,
    OccupationFeatureDialog,
    SimpleModel,
    SearchBox,
    SuccessModel,
  },
  setup() {
    const store = useStore();

    const alertText = ref([]);
    const loading = ref(true);
    const successDialog = ref(false);
    const routeName = ref('');
    const launchEdit = ref(false);
    const launchNew = ref(false);
    const editItem = ref(null);
    const toggleSearch = ref(false);
    const dialog = ref({
      delete: false,
    });

    const headers = ref([
      {
        title: 'プルダウン表示項目',
        width: '70%',
        align: 'left',
        value: 'name',
        sortable: false,
      },
      {
        title: '表示順位',
        width: '20%',
        value: 'display_order',
        align: 'right',
        sortable: false,
      },
      {
        title: '',
        value: 'delete',
        width: '10%',
        align: 'center',
        sortable: false,
      },
    ]);

    /**
     * State management for initial load and deletion
     */
    const initialLoad = ref(true);
    const temporaryDeleteId = ref(null);

    /**
     * Computed properties for pagination data
     * Provides reactive access to total records and pages
     */
    const totalRecords = computed(() => {
      return (
        store.getters.getInternshipOccupationsPagination?.records_total || 0
      );
    });

    const totalPages = computed(() => {
      return store.getters.getInternshipOccupationsPagination?.total_pages || 0;
    });

    /**
     * Formats and retrieves all internship occupations
     * Maps raw data to simplified format with id and name
     */
    const getAllInternshipOccupations = computed(() => {
      return store.getters.getAllInternshipOccupations.map((occupation) => ({
        id: occupation.id,
        name: occupation.name,
      }));
    });

    /**
     * Handles search functionality
     * @param {string} search - Search query text
     */
    const searchTable = (search) => {
      getDataFromApi(undefined, { search });
    };

    /**
     * Handles occupation deletion process
     * Shows error if occupation is in use
     */
    const deleteInternOccupation = async () => {
      successDialog.value = false;
      try {
        const res = await store.dispatch(
          'INTERNSHIP_OCCUPATIONS_DELETE',
          temporaryDeleteId.value
        );
        if (res.status === 200) {
          dialog.value.delete = false;
          getDataFromApi();
          temporaryDeleteId.value = null;
        }
      } catch (error) {
        dialog.value.delete = false;
        // Show error message in Japanese: "This occupation is in use and cannot be deleted"
        alertText.value = [
          'インターンシップに使用されています。',
          '削除できません。',
        ];
        routeName.value = 'WorkCategories';
        successDialog.value = true;
      }
    };

    /**
     * Initiates deletion process
     * @param {number} id - ID of occupation to delete
     */
    const deleteInitiate = (id) => {
      dialog.value.delete = true;
      temporaryDeleteId.value = id;
    };

    /**
     * Default sorting configuration
     */
    const sort_by_order = ref('desc');
    const sort_by = ref('created_at');

    /**
     * Fetches occupation data from API with filtering and pagination
     * @param {Object} e - Optional table options for pagination
     * @param {Object} obj - Additional filter parameters
     */
    const getDataFromApi = async (e = undefined, obj = {}) => {
      let data = {
        sort_by_order: sort_by_order.value,
        sort_by: sort_by.value,
        page: typeof e === 'number' ? e : (e?.page ?? 1),
        paginate: e?.itemsPerPage || 25,
      };

      // Merge additional filters with base data
      data = Object.assign({}, obj, data);
      // Remove empty search parameter
      if (data.search === '') {
        delete data.search;
      }

      await store.dispatch('INTERNSHIP_OCCUPATIONS_GET_ALL', data);
      initialLoad.value = false;
      loading.value = false;
    };

    /**
     * Current page state for pagination
     */
    const page = ref(0);

    /**
     * Handles table updates (sorting, pagination)
     * @param {Object} e - Table update event containing sort and page information
     */
    const updateTable = (e) => {
      // Update sort order (asc/desc)
      sort_by_order.value =
        e && e.sortBy && e.sortBy.length && e?.sortBy[0]?.order
          ? e?.sortBy[0]?.order
          : 'desc';

      // Update sort field
      sort_by.value =
        e && e.sortBy && e.sortBy.length && e?.sortBy[0]?.key
          ? e?.sortBy[0]?.key
          : 'created_at';

      let option = {};
      if (!e.page) {
        option.page = e;
      } else {
        option;
      }

      // Update current page
      page.value = e.page;

      // Fetch data if not initial load
      if (!initialLoad.value) {
        getDataFromApi(option);
      }
    };

    /**
     * Placeholder for submit functionality
     * To be implemented based on requirements
     */
    const submit = () => {
      // Your submit logic
    };

    /**
     * Placeholder for update functionality
     * To be implemented based on requirements
     */
    const update = () => {
      // Your update logic
    };

    /**
     * Handles row click event
     * Opens edit dialog with selected item data
     * @param {Object} item - Selected occupation data
     */
    const handleRowClick = (item) => {
      launchEdit.value = true;
      editItem.value = item;
    };

    /**
     * Component initialization
     * Fetches initial occupation data
     */
    onMounted(() => {
      getDataFromApi();
    });

    return {
      alertText,
      successDialog,
      routeName,
      launchEdit,
      launchNew,
      editItem,
      toggleSearch,
      dialog,
      headers,
      totalRecords,
      totalPages,
      getAllInternshipOccupations,
      searchTable,
      deleteInternOccupation,
      deleteInitiate,
      getDataFromApi,
      updateTable,
      submit,
      update,
      handleRowClick,
      loading,
    };
  },
};
</script>

<template>
  <div class="">
    <PageTitle
      v-show="!isLoading && !$store.getters.getApiProcessingStatus"
      :items="{
        title: 'フィードバック管理',
        subTitle: '今月未対応',
        icon: 'QuestionIcon',
        tabs: [
          {
            title: 'すべて',
            count: getFeedbackCounts ? getFeedbackCounts.status_1_and_2 : 0,
            action: () => tabAction('1'),
          },
          {
            title: '下書き',
            count: getFeedbackCounts ? getFeedbackCounts.status_2 : 0,
            action: () => {
              tabAction('2');
            },
            selected: $route.query.tab == 'draft' ? true : false,
          },
        ],
        buttons: [
          {
            title: '詳細条件検索',
            variant: 'outlined',
            class: 'bg-white',
            action: () => {
              toggleSearch = !toggleSearch;
            },
          },
          {
            title: '新規作成',
            icon: 'mdi-plus-circle',
            action: () => {
              $router.push({
                name: 'AddressedFeedbackCreate',
              });
            },
          },
        ],
      }"
    ></PageTitle>
    <v-fade-transition>
      <SearchArea
        class="mb-4"
        v-if="toggleSearch"
        v-bind="{ toggleSearch, selectTypeOptions, searchFields }"
        @toggleSearch="updateSearchResults"
        @changedInputType="setChangedInputType"
        @searchSubmit="searchSubmit"
        @resetForm="getDataFromApi()"
      ></SearchArea>
    </v-fade-transition>
    <DataTable
      v-if="!isLoading && !$store.getters.getApiProcessingStatus"
      :headers="headers"
      :items="getAllFeedbacks"
      ref="pagination"
      :total-records="
        getFeedbackPagination ? getFeedbackPagination.records_total : 0
      "
      :number-of-pages="
        getFeedbackPagination ? getFeedbackPagination.total_pages : 0
      "
      @update:options="updateTable"
    >
      <template v-slot:item.created_at="{ item }">
        {{ dateFormat(item.created_at) }}
      </template>

      <template v-slot:item.student_full_name="{ item }">
        {{
          `${item.student ? item.student.family_name : ''} ${
            item.student ? item.student.first_name : ''
          }`
        }}
      </template>
      <!-- company -->
      <template v-slot:item.company_id="{ item }">
        <div class="font-12px fw-400">
          {{ item.companies?.internal_company_id }}
        </div>
        <div class="mouse-pointer">
          <router-link
            v-if="item?.companies?.internal_company_id"
            target="_blank"
            class="text-3f74c2 text-decoration-none truncate-lines lines-1"
            :to="{
              name: 'CorporateDetails',
              params: { id: item.company_id },
            }"
          >
            <v-tooltip
              :text="item?.companies?.name"
              location="top"
              color="white"
            >
              <template #activator="{ props }">
                <span v-bind="props">{{ item?.companies?.name }}</span>
              </template>
            </v-tooltip>
          </router-link>
        </div>
      </template>

      <!-- student -->
      <template v-slot:item.student_id="{ item }">
        <div class="font-12px fw-400">
          {{ item?.student?.student_internal_id }}
        </div>
        <div class="mouse-pointer">
          <router-link
            v-if="item?.student?.id"
            target="_blank"
            class="text-3f74c2 text-decoration-none text-no-wrap"
            :to="{
              name: 'StudentProfile',
              params: { id: item.student.id },
              query: { tab: 'student-feedback' },
            }"
          >
            <v-tooltip
              :text="`${item.student.family_name} ${item.student.first_name}`"
              location="top"
              color="white"
            >
              <template #activator="{ props }">
                <span v-bind="props">
                  {{ item.student.family_name }} {{ item.student.first_name }}
                </span>
              </template>
            </v-tooltip>
          </router-link>
          <div v-else>存在しない</div>
        </div>
      </template>

      <!-- university -->
      <template v-slot:item.university="{ item }">
        <div v-if="item?.student?.id">
          <div class="font-12px fw-400">
            {{ item?.student.education_facility.name }}
          </div>
          <div class="font-12px truncate-lines lines-1">
            {{ item?.student?.email_valid }}
          </div>
        </div>
        <div v-else>存在しない</div>
      </template>

      <!-- start date -->
      <template v-slot:item.date_intern_start="{ item }">
        <div class="font-12px fw-400">
          {{ item.date_intern_start ? dateFormat(item.date_intern_start) : '' }}
        </div>
      </template>

      <!-- end date -->
      <template v-slot:item.date_intern_end="{ item }">
        <div class="font-12px fw-400">
          {{ item.date_intern_end ? dateFormat(item.date_intern_end) : '' }}
        </div>
      </template>

      <!-- feedback target previous -->
      <template v-slot:item.flg_feedback_target_previous_month="{ item }">
        <span class="d-flex font-12px fw-400">
          <span v-if="item.flg_feedback_target_previous_month === 0">対象外</span>
          <span v-if="item.flg_feedback_target_previous_month">
            <span class="">
              {{ getStatusTarget(item.flg_feedback_target_previous_month) }}
            </span>
            <!-- feedback target -->
            <span v-if="item.feedback_status_previous_month">
              ({{ getStatus(item.feedback_status_previous_month) }})
            </span>
          </span>
        </span>
      </template>

      <!-- feedback target -->
      <template v-slot:[`item.flg_feedback_target`]="{ item }">
        <v-sheet color="transparent" class="d-flex align-center justify-left">
          <v-menu bottom :close-on-click="true" offset-y activator="parent">
            <template v-slot:activator="{ props }">
              <v-chip
                variant="flat"
                dense
                size="small"
                v-bind="props"
                :color="getStatusColor(item.flg_feedback_target)"
              >
                <div class="d-flex align-center justify-space-between">
                  <div class="text-truncate white--text font-12px">
                    {{ getStatusTarget(item.flg_feedback_target) }}
                  </div>
                  <v-icon size="18px" color="white">mdi-chevron-down</v-icon>
                </div>
              </v-chip>
            </template>
            <!-- Menu content -->
            <v-list>
              <v-list-item
                class="mouse-pointer font-12px fw-400"
                v-for="(option, index) in options"
                :key="index"
                @click="flgFeedbackChange(item, option)"
              >
                <v-list-item-title>{{
                  getStatusTarget(option)
                }}</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </v-sheet>
      </template>

      <!-- reason feedback -->
      <template v-slot:item.type_feedback_non_target="{ item }">
        <div class="font-12px fw-400 cursor-pointer text-no-wrap">
          {{ getFeedbackReasonTypeString(item.type_feedback_non_target) }}
        </div>
      </template>

      <!-- action -->
      <template v-slot:item.action="{ item }">
        <v-btn
          :disabled="item.flg_feedback_target === 0"
          icon
          variant="text"
          size="small"
          @click.stop="
            $router.push({
              name: 'UnaddressFeedbackEdit',
              params: { id: item.id },
            })
          "
        >
          <v-icon v-if="item.flg_feedback_target === 1">$edit</v-icon>
          <v-icon v-else>$editGray</v-icon>
        </v-btn>
      </template>
    </DataTable>
    <FeedbackReasonModel
      text="対象外の理由を選択してください"
      :dialog="dialog.reject"
      :reasons="feedbackTypes"
      @submitSuccess="setDecline"
      @closeModel="dialog.reject = false"
    ></FeedbackReasonModel>
  </div>
</template>
<script>
import { ref, computed, onMounted, defineAsyncComponent } from 'vue';
import { useRoute } from 'vue-router';
import { useStore } from 'vuex';
const FeedbackReasonModel = defineAsyncComponent(
  () => import('@/components/models/FeedbackReasonModel.vue')
);
const DataTable = defineAsyncComponent(
  () => import('@/components/ui/DataTable.vue')
);
const SearchArea = defineAsyncComponent(
  () => import('@/components/ui/SearchArea.vue')
);

import moment from 'moment';
import Encoding from 'encoding-japanese';

export default {
  components: { DataTable, SearchArea, FeedbackReasonModel },
  setup() {
    const route = useRoute();
    const store = useStore();
    const dateFormat = (date) => {
      return date ? moment(date).format('YYYY/MM/DD') : '---- / - / -';
    };
    const selectTypeOptions = ref([
      {
        id: 'keyword_search',
        name: 'キーワード検索',
      },
    ]);

    // Reactive state using `ref` and `reactive`
    const feedbackTypes = ref([
      { id: 1, name: '学業によって時間確保が難しい' },
      { id: 2, name: '就活によって時間確保が難しい' },
      { id: 3, name: '事業縮小に伴う業務内容の減少' },
      { id: 4, name: '定型業務のためフィードバック自体が難しい' },
      { id: 5, name: 'プロジェクトの節目まで待ってほしい' },
      { id: 6, name: '1on1で個別に振り返り済み' },
    ]);

    const selectedTab = ref('1,2');
    const options = ref([1, 0]);
    const toggleSearch = ref(false);
    const declineItem = ref([]);
    const searchFields = ref([]);
    const isLoading = ref(true);
    const dialog = ref({
      reject: false,
      detail: false,
    });
    const selectedItem = ref(null);
    const headers = ref([
      {
        title: '内部ID',
        subTitle: '企業名',
        align: 'left',
        class: ['py-3', 'px-0'],
        value: 'company_id',
        sortable: false,
        flex: '1 1 15%', // Slightly less width
      },
      {
        title: '学生ID',
        subTitle: '学生名',
        class: ['py-3', 'px-0'],
        value: 'student_id',
        align: 'left',
        sortable: false,
        flex: '1 1 15%', // Slightly less width
      },
      {
        title: '大学名',
        subTitle: '学生メールアドレス',
        value: 'university',
        class: ['py-3', 'px-0'],
        align: 'left',
        sortable: false,
        flex: '1 1 15%', // More width for possibly longer text
      },
      {
        title: '当初雇用開始日',
        sortable: false,
        align: 'left',
        class: ['py-3', 'px-0'],
        value: 'date_intern_start',
        flex: '1 1 15%',
      },
      {
        title: '最新雇用終了日',
        sortable: false,
        align: 'left',
        class: ['py-3', 'px-0'],
        value: 'date_intern_end',
        flex: '1 1 15%',
      },
      {
        title: `${
          new Date().getMonth() === 0 ? 12 : new Date().getMonth()
        }月対象`,
        subTitle: `（稼働月：${
          (new Date().getMonth() - 1 + 12) % 12 || 12
        }月）`,
        value: 'flg_feedback_target_previous_month',
        class: ['py-3', 'px-0'],
        align: 'left',
        sortable: false,
        flex: '1 1 15%', // More width for possibly longer text
      },
      {
        title: `${new Date().getMonth() + 1}月対象`, // Current month as a number (1-12)
        subTitle: `（稼働月：${
          new Date().getMonth() === 0 ? 12 : new Date().getMonth()
        }月）`, // Previous month (handle January edge case)
        value: 'flg_feedback_target',
        class: ['py-3', 'px-0'],
        align: 'left',
        sortable: false,
        flex: '1 1 15%', // More width for possibly longer text
      },
      {
        title: '対象外理由',
        value: 'type_feedback_non_target',
        class: ['py-3', 'px-0'],
        align: 'left',
        sortable: false,
        flex: '1 1 15%', // More width for possibly longer text
      },
      {
        title: '',
        value: 'action',
        flex: '1 1 15%', // Standard width
      },
    ]);

    /**
     * State management for initial load and search inputs
     */
    const initialLoad = ref(true);
    const userSearchedInput = ref({});

    /**
     * Computed properties for accessing Vuex store data
     * Provides reactive access to feedback-related data
     */
    const getAllFeedbacks = computed(() => store.getters.getAllFeedbacks);
    const getFeedbackPagination = computed(
      () => store.getters.getFeedbackPagination
    );
    const getFeedbackCounts = computed(() => store.getters.getFeedbackCounts);
    const getFeedbackCsvData = computed(() => store.getters.getFeedbackCsvData);

    /**
     * Returns color code for feedback target status
     * @param {number} flg_feedback_target - Target flag (1: target, 0: non-target)
     * @returns {string} Hex color code
     */
    const getStatusColor = (flg_feedback_target) => {
      switch (flg_feedback_target) {
        case 1:
          return '#60D1CB'; // Target - Turquoise
        case 0:
          return '#A7A7A7'; // Non-target - Gray
      }
    };

    /**
     * Converts feedback status code to Japanese text
     * @param {number|string} value - Status code
     * @returns {string} Status in Japanese
     */
    const getStatus = (value) => {
      switch (parseInt(value)) {
        case 1:
          return '未申請'; // Not submitted
        case 2:
          return '下書き'; // Draft
        case 3:
          return '申請中'; // Under review
        case 4:
          return '公開中'; // Published
      }
    };

    /**
     * Converts target status code to Japanese text
     * @param {number|string} value - Target status code
     * @returns {string} Target status in Japanese
     */
    const getStatusTarget = (value) => {
      switch (parseInt(value)) {
        case 1:
          return '対象'; // Target
        case 0:
          return '対象外'; // Non-target
      }
    };

    /**
     * Gets feedback reason text from type ID
     * @param {number} type - Feedback reason type ID
     * @returns {string} Reason text in Japanese
     */
    const getFeedbackReasonTypeString = (type) => {
      const feedback = feedbackTypes.value.find((f) => f.id === type);
      return feedback ? feedback.name : '';
    };

    /**
     * Handles tab change and refreshes data
     * @param {string} tab - Selected tab value
     */
    const tabAction = (tab) => {
      selectedTab.value = tab;
      getDataFromApi();
    };

    /**
     * Pagination state and reset functionality
     */
    const pagination = ref(true);
    const resetPagination = () => {
      if (pagination) {
        pagination.currentPage = 1;
        pagination.updatePaginate++;
      }
    };

    /**
     * Updates table data when options change (sort, page, etc.)
     * Skips update on initial load
     * @param {Object} e - Table options event
     */
    const updateTable = (e) => {
      if (!initialLoad.value) getDataFromApi(e);
    };

    /**
     * Sets decline reason and updates feedback target status
     * @param {number} reason - Reason ID for declining feedback
     */
    const setDecline = (reason) => {
      declineItem.value.type_feedback_non_target = reason;
      dialog.value.reject = false;
      updateApi(declineItem.value, 0);
    };

    /**
     * Handles changes to feedback target status
     * Shows reason dialog when setting to non-target
     * @param {Object} item - Feedback item
     * @param {number} option - New target status
     */
    const flgFeedbackChange = async (item, option) => {
      if (item.flg_feedback_target === option) return false;

      if (option === 0) {
        declineItem.value = item;
        dialog.value.reject = true;
        return false;
      }

      updateApi(item, option);
    };

    /**
     * Updates feedback data via API
     * @param {Object} item - Feedback item to update
     * @param {number} option - New target status
     */
    const updateApi = async (item, option) => {
      let params = {
        id: item.id,
        flg_feedback_target: option,
        super_power_review: item.super_power_review,
        super_power_comment: item.super_power_comment,
        student_id: item.student_id,
        is_draft_or_public: item.is_draft_or_public,
        growth_idea_review: item.growth_idea_review,
        growth_idea_comment: item.growth_idea_comment,
        date_contract_employment_start: item.date_contract_employment_start,
        date_contract_employment_end: item.date_contract_employment_end,
        contract_id: item.contract_id,
        company_id: item.company_id,
        type_feedback_non_target: item?.type_feedback_non_target,
        feedback_status: item?.feedback_status,
        is_read: 1,
      };
      // Clear non-target reason when setting to target
      if (params.flg_feedback_target == 1) {
        params.type_feedback_non_target = null;
      }
      await store.dispatch('FEEDBACK_UPDATE', params)
      .then(response => {
        const updatedItem = response.data.data.data;

        if (updatedItem) {
          store.commit('FEEDBACK_ITEM_UPDATED', updatedItem);
        }
      })
      .catch(error => {
      });
    };

    /**
     * Fetches feedback data with filtering and pagination
     * @param {Object} e - Optional table options for sorting and pagination
     */
    const getDataFromApi = async (e = undefined) => {
      // Set status filter based on selected tab (2: drafts, 1,2: all)
      let status = selectedTab.value === '2' ? '2' : '1,2';

      // Prepare API request parameters
      const data = {
        sort_by_order:
          e && e.sortDesc && e.sortDesc.length > 0
            ? e.sortDesc[0]
              ? 'asc'
              : 'desc'
            : 'desc',
        sort_by:
          e && e.sortBy && e.sortBy.length > 0 ? e.sortBy[0] : 'created_at',
        page: typeof e === 'number' ? e : e && e.page ? e.page : 1,
        paginate: e && e.itemsPerPage ? e.itemsPerPage : 25,
        feedback_status: status,
        month_feedback_target: moment(new Date()).format('YYYY-MM'),
        ...userSearchedInput.value,
      };

      // Prevent unnecessary API calls if the parameters haven't changed
      const currentParams = JSON.stringify(data);
      if (getDataFromApi.lastParams === currentParams) {
        return;
      }

      // Store current parameters for future comparison
      getDataFromApi.lastParams = currentParams;

      try {
        await store.dispatch('FEEDBACK_GET_ALL', data);
      } catch (error) {
        console.error('Error fetching feedback data:', error);
      } finally {
        initialLoad.value = false;
        isLoading.value = false;
      }
    };

    /**
     * Handles search form submission and updates results
     */
    const updateSearchResults = async () => {
      toggleSearch.value = false;
      await getDataFromApi();
      resetPagination();
    };

    /**
     * Updates search fields based on selected search type
     * @param {string} inputSearchType - Type of search (keyword or date range)
     */
    const setChangedInputType = (inputSearchType) => {
      if (inputSearchType === 'keyword_search') {
        // Set up keyword search field
        searchFields.value = [
          {
            label: 'Search text',
            name: 'search',
            type: 'text',
            value: null,
            placeholder: '企業内部ID、企業名、学生ID、学生名、大学名',
          },
        ];
      } else if (inputSearchType === 'posted_month') {
        // Set up date range search fields
        searchFields.value = [
          {
            label: 'Label',
            name: 'posted_month_start',
            type: 'date',
            show_after_approx: true,
            rules: 'required',
            value: moment().format('YYYY-MM-DD'),
            menu: false,
            locale: 'ja',
            date_format: 'YYYY-MM-DD',
          },
          {
            label: 'Label',
            name: 'posted_month_end',
            type: 'date',
            show_after_approx: false,
            rules: 'required',
            value: moment().format('YYYY-MM-DD'),
            menu: false,
            locale: 'ja',
            range: true,
            range_input: 'date_from',
            date_format: 'YYYY-MM-DD',
          },
        ];
      } else {
        searchFields.value = {};
      }
    };

    /**
     * Processes search form submission and updates search criteria
     * @param {Object} $event - Form submission event with field values
     */
    const searchSubmit = async ($event) => {
      let obj = {};
      if ($event.fields.length > 0) {
        $event.fields.forEach((field) => {
          obj[field.name] = field.value;
        });
      }
      userSearchedInput.value = { ...obj };
      getDataFromApi();
    };

    /**
     * Handles CSV export functionality
     * Downloads feedback data as CSV file
     */
    // const downloadCsv = async () => {
    //   await store.dispatch('FEEDBACK_EXPORT_CSV');

    //   // Ensure data exists
    //   if (!getFeedbackCsvData.value) {
    //     console.error('No CSV data available');
    //     return;
    //   }

    //   // Convert UTF-8 CSV to Shift-JIS
    //   const sjisArray = Encoding.convert(
    //     Encoding.stringToCode(getFeedbackCsvData.value),
    //     {
    //       to: 'SJIS',
    //       from: 'UNICODE',
    //     }
    //   );

    //   const uint8Array = new Uint8Array(sjisArray);
    //   const blob = new Blob([uint8Array], { type: 'text/csv' });

    //   // Create and trigger file download
    //   const fileUrl = window.URL.createObjectURL(blob);
    //   const fileLink = document.createElement('a');
    //   fileLink.href = fileUrl;
    //   fileLink.setAttribute(
    //     'download',
    //     `フィードバック管理_${moment().format('YYYYMMDD')}.csv`
    //   );
    //   document.body.appendChild(fileLink);
    //   fileLink.click();
    //   document.body.removeChild(fileLink);
    // };

    /**
     * Component initialization
     * Fetches initial feedback data
     */
    onMounted(async () => {
      if (route.query.tab == 'draft') {
        selectedTab.value = '2';
      }
      await getDataFromApi();
    });

    return {
      feedbackTypes,
      selectedTab,
      options,
      toggleSearch,
      declineItem,
      searchFields,
      dialog,
      selectedItem,
      headers,
      initialLoad,
      userSearchedInput,
      getAllFeedbacks,
      getFeedbackPagination,
      getFeedbackCounts,
      getFeedbackCsvData,
      getStatusColor,
      getStatus,
      getFeedbackReasonTypeString,
      flgFeedbackChange,
      getStatusTarget,
      tabAction,
      resetPagination,
      updateTable,
      updateApi,
      getDataFromApi,
      setChangedInputType,
      searchSubmit,
      // downloadCsv,
      dateFormat,
      selectTypeOptions,
      updateSearchResults,
      setDecline,
      isLoading,
    };
  },
};
</script>
<style lang="scss" scoped></style>

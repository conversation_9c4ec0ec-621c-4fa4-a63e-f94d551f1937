<template>
  <!-- ApplicationNoContactDialog.vue Dialog -->
  <v-dialog
    v-model="selfDialog"
    :max-width="790"
    :style="{
      maxHeight: display.mdAndUp ? 'auto' : '432px',
    }"
    class="no-shadow"
  >
    <v-card class="pa-15">
      <v-card-text class="pb-5">
        <template v-if="Array.isArray(props.text)">
          <div
            v-for="(tt, index) in props.text"
            :key="index"
            class="my-1 text-center d-flex justify-center align-center text-333"
            :class="[index == 0 ? 'font-20px mb-7' : 'font-14px']"
            v-html="tt"
          ></div>
        </template>
        <template v-else>
          <div
            class="my-1 d-flex justify-center align-center text-333 font-20px head-text"
            v-html="props.text"
          ></div>
        </template>

        <!-- Email header -->
        <div class="email-header my-4 pa-4">
          <v-row>
            <v-col>
              <div>
                <strong>To:</strong>
                {{ props.selectedItem?.student?.email_valid }}
              </div>
              <div><strong>From:</strong> <EMAIL></div>
              <div>
                <strong>BCC:</strong>
                {{
                  props.selectedItem?.internship_post?.contactable_user1_email
                }}
                {{
                  props.selectedItem?.internship_post?.contactable_user2_email
                    ? ','
                    : ''
                }}
                {{
                  props.selectedItem?.internship_post?.contactable_user2_email
                }}
                {{
                  props.selectedItem?.internship_post?.contactable_user3_email
                    ? ','
                    : ''
                }}
                {{
                  props.selectedItem?.internship_post?.contactable_user3_email
                }}
                {{
                  props.selectedItem?.internship_post?.contactable_user4_email
                    ? ','
                    : ''
                }}
                {{
                  props.selectedItem?.internship_post?.contactable_user4_email
                }}
                {{
                  props.selectedItem?.internship_post?.contactable_user5_email
                    ? ','
                    : ''
                }}
                {{
                  props.selectedItem?.internship_post?.contactable_user5_email
                }}
              </div>
              <div>
                <strong>件名:</strong> {{ props.selectedItem?.company?.name }} /
                返信がないことによる選考終了のお知らせ
              </div>
            </v-col>
          </v-row>
        </div>

        <!-- Message body -->
        <div class="mb-4 email-body pa-4">
          <v-row>
            <v-col>
              <div class="my-1">
                {{ props.selectedItem?.student?.education_facility?.name }}
                {{ props.selectedItem?.student?.family_name }}
                {{ props.selectedItem?.student?.first_name }} 様 <br /><br />

                こんにちは。コトナル事務局です。<br />
                コトナルをご利用いただき、誠にありがとうございます。<br /><br />

                応募されたインターンの選考に関わる連絡に、{{
                  props.selectedItem?.student?.family_name
                }}さんから返信がないため選考終了にせざるを得ませんでした。<br /><br />
                会社名：{{ props.selectedItem?.company?.name }}<br />
                求人名：{{ props.selectedItem?.internship_post?.title
                }}<br /><br />
                万が一返されていた場合は、お手数ですが本メールの全員返信にて企業様に再度ご連絡をお願いします。
              </div>
            </v-col>
          </v-row>
        </div>
      </v-card-text>

      <v-card-actions class="justify-center">
        <v-btn
          v-if="!props.buttonOption.hideCancel"
          variant="outlined"
          elevation="5"
          color="primary"
          min-width="188"
          class="mr-6"
          @click.prevent="notSent"
        >
          {{ props.sentButtonText }}
        </v-btn>
        <v-btn
          v-if="!props.buttonOption.hideSubmit"
          color="primary"
          class="white--text modal-submit btn-sub"
          min-width="188"
          @click.prevent="sent"
          :loading="props.loading"
        >
          {{ props.notSendButtonText }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { computed } from 'vue';
import { useDisplay } from 'vuetify';

const display = useDisplay();

const props = defineProps({
  dialog: {
    type: Boolean,
    default: false,
    required: true,
  },
  text: {
    type: [String, Array],
    default: '',
    required: true,
  },
  selectedItem: {
    type: Object,
    default: null,
    required: true,
  },
  sentButtonText: {
    type: String,
    default: '戻る',
    required: false,
  },
  notSendButtonText: {
    type: String,
    default: 'メールを送信する',
    required: false,
  },
  loading: {
    type: Boolean,
    default: false,
    required: false,
  },
  status: {
    type: Number,
    default: 0,
    required: true,
  },
  buttonOption: {
    type: Object,
    default: () => ({
      hideSubmit: false,
      hideCancel: false,
    }),
    required: false,
  },
});

const emit = defineEmits(['closeModel', 'update', 'back']);

const selfDialog = computed({
  get: () => props.dialog,
  set: () => emit('closeModel', true),
});

const sent = () => {
  emit('update', props.status);
};

const notSent = () => {
  emit('back');
};
</script>
<style lang="css" scoped>
.head-text {
  font-size: 19px;
}

.btn-sub {
  background: #13aca4;
  color: white !important;
  border: none;
}

.modal-submit {
  box-shadow: 0px 4px 4px rgb(0 0 0 / 25%);
  border-radius: 3px;
}
.email-header {
  border: 1px dashed gray;
}
.email-body {
  border: 1px solid gray;
}

.container {
  padding: 5% 5%;
}

/* CSS talk bubble */
.talk-bubble {
  margin: 60px;
  display: inline-block;
  position: relative;
  width: 270px;
  height: auto;
  background-color: #fef445;
  color: black;
}
.border-bubble {
  border: 1px solid #666;
}
.round {
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
}
talk-bubble tri-right border-bubble round btm-left-in
/*Right triangle, placed bottom left side slightly in*/
.tri-right.border-bubble.btm-left:before {
  content: ' ';
  position: absolute;
  width: 0;
  height: 0;
  left: -8px;
  right: auto;
  top: auto;
  bottom: -40px;
  border-width: 20px 30px 0 0; /* Shorten the height and widen the width */
  border-style: solid;
  border-color: #000000 transparent transparent transparent;
}

.tri-right.btm-left:after {
  content: ' ';
  position: absolute;
  width: 0;
  height: 0;
  left: 0px;
  right: auto;
  top: auto;
  bottom: -20px;
  border-width: 18px 40px 0 0; /* Keep proportional width and height */
  border-style: solid;
  border-color: #fef445 transparent transparent transparent;
}

/*Right triangle, placed bottom left side slightly in*/
.tri-right.border-bubble.btm-left-in:before {
  content: ' ';
  position: absolute;
  width: 0;
  height: 0;
  left: 37px;
  right: auto;
  top: auto;
  bottom: -18px;
  border-width: 18px 40px 0 0; /* Adjusted to make the tail wider and shorter */
  border-style: solid;
  border-color: #000000 transparent transparent transparent;
}

.tri-right.btm-left-in:after {
  content: ' ';
  position: absolute;
  width: 0;
  height: 0;
  left: 38px;
  right: auto;
  top: auto;
  bottom: -17px;
  border-width: 18px 39px 0 0; /* Proportional change to inner triangle */
  border-style: solid;
  border-color: #fef445 transparent transparent transparent;
}

/* talk bubble contents */
.talktext {
  padding: 1em;
  text-align: left;
  line-height: 1.5em;
}
.talktext p {
  /* remove webkit p margins */
  -webkit-margin-before: 0em;
  -webkit-margin-after: 0em;
}
</style>

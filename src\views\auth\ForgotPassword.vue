<template>
  <v-app>
    <section class="auth-main-section font-Noto-Sans text-center">
      <v-container fill-height>
        <v-row>
          <v-col cols="12">
            <div class="login-main-blk">
              <div class="login-top-blk mb-10 pb-7">
                <div class="logo-circle-top">
                  <v-img class="mx-auto" :src="logoImage" lazy></v-img>
                </div>
              </div>
              <form @submit.prevent="handleSubmit">
                <div class="hint-text text-left">
                  <p class="mb-3 font-14px fw-400">
                    登録されたメールアドレスに<br />
                    パスワードの再設定メールを送信します
                  </p>
                </div>
                <v-text-field
                  v-model="email"
                  :error-messages="errors.email"
                  :error="!!errors.email"
                  placeholder="メールアドレス"
                  variant="outlined"
                  density="comfortable"
                  class="input-text"
                  hide-details
                ></v-text-field>
                <div class="mb-1 mt-n1 text-left font-12px" v-if="error">
                  <FlashMessage :error="error" />
                </div>
                <v-btn
                  block
                  class="text-capitalize white--text font-14px fw-500 btn-primary"
                  type="submit"
                  color="primary"
                  :disabled="apiProcessing"
                  :loading="apiProcessing"
                >
                  メールを送信
                </v-btn>

                <div class="forget-blk pt-8">
                  <router-link
                    class="forget-link fw-500 font-12px"
                    :to="{ name: 'Login' }"
                  >
                    ログイン画面へ戻る
                  </router-link>
                </div>
              </form>
            </div>
          </v-col>
          <v-col cols="12" v-if="emailSent">
            <div class="login-main-blk">
              <v-alert type="success" color="#13aba3">
                メールを送信しました。
              </v-alert>
            </div>
          </v-col>
        </v-row>
      </v-container>
    </section>
  </v-app>
</template>

<script setup>
// Import necessary Vue and third-party dependencies
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
// Import custom components and assets
import FlashMessage from '@/components/FlashMessage.vue';
import logoImage from '@/assets/images/logo-big.svg';

// Initialize reactive component state variables
const email = ref(''); // Stores email input value
const error = ref(''); // Stores general error messages
const emailSent = ref(false); // Tracks if reset email was sent successfully
const errors = ref({}); // Stores validation errors for form fields

// Initialize Vuex store and Vue Router
const store = useStore();
const router = useRouter();

// Computed property to track API processing state
// Used to disable form submission while processing
const apiProcessing = computed(() => store.getters.getApiProcessingStatus);

// Set initial API processing state to false
store.dispatch('API_PROCESSING', false);

/**
 * Validates email input
 * Checks for:
 * 1. Empty email field (必須/Required)
 * 2. Valid email format
 * @returns {boolean} - true if validation passes, false otherwise
 */
const validateEmail = () => {
  errors.value = {};
  // Check if email is empty
  if (!email.value) {
    errors.value.email = ['メールアドレスは必須です']; // Email is required
    return false;
  }
  // Validate email format using regex
  if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.value)) {
    errors.value.email = ['有効なメールアドレスを入力してください']; // Please enter a valid email
    return false;
  }
  return true;
};

/**
 * Handles form submission for password reset
 * 1. Validates email
 * 2. Sends reset password request to API
 * 3. Handles success/error responses
 */
const handleSubmit = async () => {
  if (!validateEmail()) return;

  // Construct reset URL using environment variable
  const reset_url = `${import.meta.env.VITE_APP_DEV_URL}update-password`;

  try {
    // Set processing state to true
    store.dispatch('API_PROCESSING', true);
    // Dispatch password reset action to Vuex store
    const response = await store.dispatch('AUTH_FORGOT', {
      email: email.value,
      reset_url,
    });

    // Handle successful response
    if (response.status === 200) {
      emailSent.value = true; // Show success message
      error.value = ''; // Clear any previous errors
      await router.push({ name: 'ForgotPassword' }); // Refresh the page
    }
  } catch (err) {
    console.error('Password reset error:', err);
    // Set error message from API or fallback to generic error
    // 'An error occurred. Please try again later.'
    error.value =
      err.data?.error ||
      'エラーが発生しました。しばらく経ってからもう一度お試しください。';
  } finally {
    // Reset processing state
    store.dispatch('API_PROCESSING', false);
  }
};
</script>

<style src="./__auth.scss" lang="scss"></style>

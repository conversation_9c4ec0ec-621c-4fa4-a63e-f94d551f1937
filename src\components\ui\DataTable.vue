<template>
  <div class="w-100">
    <v-data-table
      color="'primary'"
      :headers="getHeaders"
      :items="items"
      :search="search"
      :items-per-page="itemsPerPage"
      :no-data-text="'表示できるデータはありません'"
      :footer-props="{
        itemsPerPageOptions: [25, 50],
      }"
      :server-items-length="totalRecords"
      :loading="loading"
      :hide-default-footer="!footer"
      @click:row="handleClick"
      @update:options="pageUpdateFunction"
      v-model:page="currentPage.value"
      class="datatable"
    >
      <template
        v-slot:[`header.${header.value}`]="{
          column,
          isSorted,
          sortBy,
          getSortIcon,
        }"
        v-for="header in getHeaders"
        :key="header.value"
      >
        <th
          class="white--text fw-500 font-14px bg-default"
          :class="[
            getAlignClass(header.align, header.isNoWrap),
            header.class || '',
            isSorted(header) ? (sortBy[0].desc ? 'desc' : 'asc') : '',
          ]"
          :style="{ width: column.width || 'auto' }"
        >
          <span v-if="column.helpIcon">
            {{ column.title }}
            <v-tooltip location="top">
              <template #activator="{ props: tooltipProps }">
                <v-icon v-bind="tooltipProps" color="white" size="20">
                  mdi-help-circle
                </v-icon>
              </template>
              <span>{{ column.helpText }}</span>
            </v-tooltip>
          </span>
          <span v-else-if="column.heartIcon">
            <v-icon color="white">$HeartIcon</v-icon>
          </span>
          <span v-else-if="column.paperPlaneIcon">
            <v-icon color="white">$PaperPlaneIcon</v-icon>
          </span>
          <span v-else-if="header.subTitle">
            {{ header.title }}
            <br />
            {{ header.subTitle }}
          </span>
          <span v-else>
            {{ column.title }}
          </span>
          <v-icon
            v-if="
              column.sortable && !column.heartIcon && !column.paperPlaneIcon
            "
            :icon="getSortIcon(column)"
            size="15"
            class="ml-1"
            color="white"
          />
        </th>
      </template>
      <template v-slot:item="{ item, index }">
        <tr
          :class="getItemClass(item)"
          @click="handleClick(item)"
          :style="customHeight"
        >
          <!-- Pass the item data when the row is clicked -->
          <td
            v-for="header in getHeaders"
            :key="header.value"
            :class="[getAlignClass(header.align, header.isNoWrap)]"
          >
            <slot :name="`item.${header.value}`" :item="item">
              <span v-if="header.value === 'serial_number'">{{
                index + 1
              }}</span>
              {{ item[header.value] }}
              <!-- Default display if no slot provided -->
            </slot>
          </td>
        </tr>
      </template>
    </v-data-table>
    <div class="text-center mt-5 pagination-main-outer" v-if="totalRecords > 0">
      <paginate
        :key="updatePaginate"
        :initial-page="getCurrentPage"
        :page-count="numberOfPages"
        :click-handler="pageUpdateFunction"
        prev-text="<"
        :next-text="'>'"
        :container-class="'pagination'"
      >
        <span slot="prevContent"><v-icon size="10px">$LeftArrow</v-icon></span>
        <span slot="nextContent"><v-icon size="10px">$RightArrow</v-icon></span>
      </paginate>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue';
import Paginate from 'vuejs-paginate-next';

export default {
  components: {
    Paginate,
  },
  props: {
    footer: {
      type: Boolean,
      default: false,
    },
    headers: {
      type: Array,
      default: () => [],
    },
    items: {
      type: Array,
      default: () => [],
    },
    search: {
      type: String,
      default: '',
    },
    numberOfPages: {
      type: Number,
      default: 0,
    },
    totalRecords: {
      type: Number,
      default: 0,
    },
    itemsPerPage: {
      type: Number,
      default: 25,
    },
    page: {
      type: Number,
      default: 1,
    },
    disablePagination: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    customHeight: {
      type: String,
      default: '48px',
    },
  },
  setup(props, { emit, attrs }) {
    const currentPage = ref(props.page || 1);
    const updatePaginate = ref(0);

    const getHeaders = computed(() => [
      {
        text: '',
        sortable: false,
        width: '28px',
      },
      ...props.headers,
      {
        text: '',
        sortable: false,
        width: '28px',
      },
    ]);

    const getCurrentPage = computed(
      () => currentPage.value.page || currentPage.value
    );

    const handleClick = (value) => {
      emit('row-clicked', value);
    };

    const getAlignClass = (align, isNoWrap = false) => {
      return align ? `text-${align} justify-${align} text-no-wrap` : 'text-left text-no-wrap';
    };

    const pageUpdateFunction = (newPageNumber) => {
      currentPage.value = newPageNumber;
      emit('update:options', newPageNumber);
    };

    // color for contract
    const getItemClass = (item) => {
      if (item.internal_contract_id) {
        if (item.status === 4) {
          return '';
        }

        const date = item.date_employment_end;
        if (!date) return '';

        const today = new Date();
        const oneMonthLater = new Date();
        oneMonthLater.setMonth(today.getMonth() + 1);

        const employmentEndDate = new Date(date);
        const val =
          employmentEndDate <= oneMonthLater && employmentEndDate >= today
            ? 'row-pink'
            : '';
        return val;
      }
    };

    return {
      currentPage,
      getCurrentPage,
      updatePaginate,
      getHeaders,
      handleClick,
      getAlignClass,
      pageUpdateFunction,
      getItemClass,
    };
  },
};
</script>

<style lang="scss">
tr.row-pink {
  background: #ee6c9b26 !important;
}
.bg-default {
  background: #13aba3 !important;
}

.datatable {
  .v-data-table-header__content {
    color: white !important;
  }
  .pagination-main-outer {
    max-width: 308px;
    margin: auto;
  }

  .pagination-main-outer .v-pagination__more {
    align-items: center !important;
  }

  .white--text {
    color: white;
  }

  .fw-500 {
    font-weight: 500;
  }

  .font-14px {
    font-size: 14px;
  }
}
.pagination {
  display: flex;
  justify-content: center;
  list-style: none;
  padding: 0;
  margin: 0;
  .page-item {
    margin: 0 4px; /* Space between each page item */
  }

  .page-link {
    cursor: pointer;
    font-size: 12px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px; /* Adjust size as needed */
    height: 20px; /* Adjust size as needed */
    border-radius: 50%;
    background-color: transparent;
    color: $color-light-dark;
    transition:
      background-color 0.3s,
      color 0.3s;
  }

  .page-item.active .page-link {
    background-color: #13aba3;
    color: #ffffff;
    font-weight: bold;
  }

  .page-item.disabled .page-link {
    background-color: transparent; /* Slightly different for disabled */
    color: $color-light-dark;
    pointer-events: none;
  }

  .page-item .page-link:hover {
    background-color: #e0f2f1; /* Light teal on hover */
    color: #13aba3;
    border-color: #13aba3;
  }

  .page-item .page-link:focus {
    box-shadow: none; /* Remove Bootstrap's default focus ring */
  }

  tr.row-pink {
    background: #ee6c9b26 !important;
  }
}
th.v-data-table__td {
  background: #13aca4 !important;
}
</style>

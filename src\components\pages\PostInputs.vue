<template>
  <div>
    <v-card>
      <div class="mx-16 py-12">
        <div class="d-flex justify-end">
          <span v-if="singleInternship" class="font-14px text-b8b8b8 mr-3">
            {{ formatDate(singleInternship.created_at) }} 公開
          </span>
          <span v-if="singleInternship" class="font-14px text-b8b8b8">
            {{ formatDate(singleInternship.updated_at) }} 更新
          </span>
        </div>
        <div v-if="sectionTitle" class="font-20px font-400">
          {{ sectionTitle }}
        </div>

        <v-row
          no-gutters
          class="my-3"
          :class="field.row_class"
          v-for="(field, index) in basicInformation"
          :key="field.name || index"
        >
          <v-col
            cols="12"
            :md="field.additional_field ? '6' : '12'"
            :class="field.col_class_left"
            class="mt-1"
          >
            <template v-if="field.type == 'text'">
              <div class="full-width">
                <div class="font-14px">
                  {{ field.label }}
                  <span
                    v-if="field.rules && field.rules.includes('required')"
                    class="font-12px error--text ml-2"
                    >必須</span
                  >
                </div>
                <Field
                  v-slot="{ field: { value, ...fieldWithoutValue }, errors }"
                  :name="field.name"
                  :rules="field.rules"
                  :validateOnMount="false"
                  :value="field.value"
                >
                  <v-text-field
                    v-bind="fieldWithoutValue"
                    :error-messages="errors"
                    :error="errors.length > 0"
                    :hide-details="errors.length <= 0"
                    v-model="field.value"
                    single-line
                    color="#13ABA3"
                    class="mt-1"
                    :placeholder="field.placeholder"
                    density="compact"
                    variant="outlined"
                  />
                  <div
                    v-if="field.counter"
                    class="d-flex justify-end font-14px text-grey"
                  >
                    {{ field.value ? field.value.length : 0 }} /
                    {{ field.counterValue }}
                  </div>
                </Field>
              </div>
            </template>

            <template v-if="field.type == 'textarea'">
              <div class="full-width">
                <div class="font-14px">
                  {{ field.label }}
                  <span
                    v-if="field.rules && field.rules.includes('required')"
                    class="font-12px error--text ml-2"
                    >必須</span
                  >
                </div>
                <Field
                  v-slot="{ field: { value, ...fieldWithoutValue }, errors }"
                  :name="field.name"
                  :rules="field.rules"
                  :value="field.value"
                >
                  <v-textarea
                    v-bind="fieldWithoutValue"
                    :error-messages="errors"
                    :error="errors.length > 0"
                    :hide-details="errors.length <= 0"
                    v-model="field.value"
                    single-line
                    color="#13ABA3"
                    class="font-16px mt-1"
                    variant="outlined"
                    :rows="4"
                    :placeholder="field.placeholder"
                  />
                  <div
                    v-if="field.counter"
                    class="d-flex justify-end font-14px text-grey"
                  >
                    {{ field.value ? field.value.length : 0 }} /
                    {{ field.counterValue }}
                  </div>
                </Field>
              </div>
            </template>

            <template v-if="field.type == 'dropdown'">
              <div class="full-width">
                <div class="font-14px">
                  {{ field.label }}
                  <span
                    v-if="field.rules && field.rules.includes('required')"
                    class="font-12px error--text ml-2"
                    >必須</span
                  >
                </div>
                <Field
                  v-slot="{ field: { value, ...fieldWithoutValue }, errors }"
                  :name="field.name"
                  :rules="field.rules"
                  :value="field.value"
                >
                  <template v-if="field.multiple">
                    <v-select
                      density="compact"
                      variant="outlined"
                      color="#13ABA3"
                      class="font-14px mt-1 select-label"
                      :items="field.items"
                      :item-title="field.item_text"
                      :error-messages="errors"
                      :error="errors.length > 0"
                      :hide-details="errors.length <= 0"
                      :item-value="[field.item_value]"
                      :multiple="field.multiple"
                      :placeholder="field.placeholder"
                      v-model="field.value"
                      :no-data-text="field.no_data_text"
                    >
                      <template v-if="field.append_outer_text" #append>
                        <span class="text-no-wrap font-14px">
                          {{ field.append_outer_text }}
                        </span>
                      </template>
                    </v-select>
                  </template>
                  <template v-else>
                    <v-select
                      v-bind="fieldWithoutValue"
                      :items="field.items"
                      :item-title="field.item_text"
                      :item-value="field.item_value"
                      :error-messages="errors"
                      :error="errors.length > 0"
                      :hide-details="errors.length <= 0"
                      class="mt-1 select-label"
                      density="compact"
                      variant="outlined"
                      :placeholder="field?.placeholder"
                      v-model="field.value"
                      :no-data-text="field.no_data_text"
                    >
                      <template v-if="field.append_outer_text" #append>
                        <span class="text-no-wrap font-18px">
                          {{ field.append_outer_text }}
                        </span>
                      </template>
                    </v-select>
                  </template>
                </Field>
              </div>
            </template>

            <template v-if="field.type == 'autocomplete'">
              <div class="full-width">
                <div class="font-14px">
                  {{ field.label }}
                  <span
                    v-if="field.rules && field.rules.includes('required')"
                    class="font-12px error--text ml-2"
                    >必須</span
                  >
                </div>
                <Field
                  v-slot="{ field: { value, ...fieldWithoutValue }, errors }"
                  :name="field.name"
                  :rules="field.rules"
                  :value="field.value"
                >
                  <v-autocomplete
                    color="#13ABA3"
                    v-bind="fieldWithoutValue"
                    :error-messages="errors"
                    :error="errors.length > 0"
                    :hide-details="errors.length <= 0"
                    :items="field.items"
                    :item-title="field.item_text"
                    :item-value="field.item_value"
                    :placeholder="field.placeholder"
                    :loading="field.is_loading"
                    v-model="field.value"
                    :no-data-text="field.no_data_text"
                    :hide-no-data="true"
                    v-model:search="field.searched_text"
                    @keyup="field.searchable ? field.search_api(field) : false"
                    class="font-16px mt-1"
                    density="compact"
                    variant="outlined"
                    autocomplete="new-password"
                  >
                    <!-- Custom Item Slot -->
                    <template #item="{ item, props }">
                      <div class="hover-item" v-bind="props">
                        <span class="font-14px">{{ getItem(item) }}</span>
                        <span class="font-16px text-b8b8b8 ml-1">{{
                          getBusinessIndustry(item)
                        }}</span>
                      </div>
                    </template>

                    <!-- Custom Selection Slot -->
                    <template #selection="{ item, props }">
                      <div v-bind="props">
                        <span class="font-14px">{{ getItem(item) }}</span>
                        <span class="font-16px text-b8b8b8 ml-1">{{
                          getBusinessIndustry(item)
                        }}</span>
                      </div>
                    </template>
                  </v-autocomplete>
                </Field>
              </div>
            </template>

            <template v-if="field.type == 'richbox'">
              <div class="full-width">
                <div class="font-14px mb-2">
                  {{ field.label }}
                  <span
                    v-if="field.rules && field.rules.includes('required')"
                    class="font-12px error--text ml-2"
                    >必須</span
                  >
                </div>
                <Field
                  v-slot="{ field: { value, ...fieldWithoutValue }, errors }"
                  :name="field.name"
                  :rules="field.rules"
                  :value="field.value"
                >
                  <rich-text-editor
                    :id="`rich-text-editor-${index}`"
                    :error-messages="errors"
                    v-model:content="field.value"
                    :height="editorHeight"
                    :headings="headings ? headings : {}"
                  />
                  <template v-if="errors.length > 0">
                    <span class="font-12px text-red">{{ errors[0] }}</span>
                  </template>
                </Field>
              </div>
            </template>

            <template v-if="field.type == 'checkbox'">
              <div class="full-width" style="height: 30px">
                <div class="font-14px">
                  <span
                    v-if="field.rules && field.rules.includes('required')"
                    class="font-12px error--text ml-2"
                    >必須</span
                  >
                </div>
                <Field
                  v-slot="{ field: { value, ...fieldWithoutValue }, errors }"
                  :name="field.name"
                  :rules="field.rules"
                  :value="field.value"
                >
                  <v-switch
                    v-bind="fieldWithoutValue"
                    :error-messages="errors"
                    :error="errors.length !== 0"
                    :hide-details="errors.length <= 0"
                    density="compact"
                    variant="outlined"
                    v-model="field.value"
                    :label="field.label"
                    color="#13ABA3"
                    class="mt-0"
                    :disabled="field.isCheckBoxDisabled"
                  />
                </Field>
              </div>
            </template>

            <template v-if="field.type == 'number-currency'">
              <div class="full-width">
                <div class="font-14px">
                  {{ field.label }}
                  <span
                    v-if="field.rules && field.rules.includes('required')"
                    class="font-12px error--text ml-2"
                    >必須</span
                  >
                </div>
                <Field
                  v-slot="{ field: { value, ...fieldWithoutValue }, errors }"
                  :name="field.name"
                  :rules="field.rules"
                  :validateOnMount="false"
                  :value="field.value"
                >
                  <v-text-field
                    v-bind="fieldWithoutValue"
                    :error-messages="errors"
                    :error="errors.length > 0"
                    :hide-details="errors.length <= 0"
                    v-model="field.value"
                    single-line
                    color="#13ABA3"
                    class="mt-1"
                    :placeholder="field.placeholder"
                    density="compact"
                    variant="outlined"
                    @input="formatCurrency"
                    @blur="formatOnBlur"
                  />
                </Field>
              </div>
            </template>
          </v-col>

          <template v-if="field.additional_field">
            <v-col
              v-if="
                field.additional_field.visibility_check &&
                field.value == field.additional_field.visible_value
                  ? false
                  : true
              "
              cols="12"
              class="mt-1"
            >
              <div>
                <div class="full-width">
                  <div class="font-14px visibility-hidden">
                    {{ field.label }}
                    <span
                      v-if="field.rules && field.rules.includes('required')"
                      class="font-12px error--text ml-2"
                      >必須</span
                    >
                  </div>
                  <div class="d-flex full-width">
                    <div
                      v-if="field.additional_field.relative_text"
                      class="d-flex align-center mr-1"
                    >
                      ～
                    </div>
                    <Field
                      v-slot="{
                        field: { value, ...fieldWithoutValue },
                        errors,
                      }"
                      :name="field.additional_field.name"
                      :rules="field.additional_field.rules"
                      :value="field.additional_field.value"
                    >
                      <template v-if="field.additional_field.type == 'text'">
                        <v-text-field
                          v-bind="fieldWithoutValue"
                          :error-messages="errors"
                          :error="errors.length !== 0"
                          :hide-details="errors.length <= 0"
                          v-model="field.additional_field.value"
                          single-line
                          color="#13ABA3"
                          class="font-16px mt-1"
                          density="compact"
                          variant="outlined"
                          :placeholder="field.additional_field.placeholder"
                        />
                      </template>

                      <template
                        v-if="field.additional_field.type == 'dropdown'"
                      >
                        <v-select
                          v-bind="fieldWithoutValue"
                          density="compact"
                          variant="outlined"
                          class="mt-1 select-label"
                          v-model="field.additional_field.value"
                          :placeholder="field.additional_field.placeholder"
                          :items="field.items"
                          :item-title="field.item_text"
                          :item-value="field.item_value"
                          :error-messages="errors"
                          :error="errors.length !== 0"
                          :hide-details="errors.length <= 0"
                          autocomplete="chrome-off"
                          :no-data-text="field.no_data_text"
                        >
                          <template v-if="field.append_outer_text" #append>
                            <span class="text-no-wrap font-14px">
                              {{ field.append_outer_text }}
                            </span>
                          </template>
                        </v-select>
                      </template>
                    </Field>
                  </div>
                </div>
              </div>
            </v-col>
          </template>
        </v-row>
      </div>
    </v-card>
  </div>
</template>

<script>
import { ref, computed } from 'vue';
import { Field } from 'vee-validate';
import RichTextEditor from '@/components/ui/RichTextEditor.vue';
import moment from 'moment';

export default {
  name: 'InternshipBasicInformation',
  components: {
    RichTextEditor,
    Field,
  },
  props: {
    basicInformation: {
      type: Array,
      required: true,
      default: () => [],
    },
    singleInternship: {
      type: Object,
      required: false,
    },
    editorHeight: {
      type: String,
      default: '400px',
    },
    sectionTitle: {
      type: [String, Boolean],
      default: '基本情報',
    },
    headings: {
      type: Object,
      required: false,
    },
  },
  setup(props, { emit }) {
    const formFields = ref(props.basicInformation);
    const currencyValue = ref('');
    const searchTimeout = ref(null);

    const formattedCreatedDate = computed(() => {
      return props.singleInternship?.created_at
        ? moment(props.singleInternship.created_at).format('YYYY/MM/DD')
        : '';
    });

    const formattedUpdatedDate = computed(() => {
      return props.singleInternship?.updated_at
        ? moment(props.singleInternship.updated_at).format('YYYY/MM/DD')
        : '';
    });

    const formatDate = (date) => moment(date).format('YYYY/MM/DD');

    const getItem = (item) => {
      return `${item.raw.internal_company_id} / ${item.raw.name}`;
    };

    const getBusinessIndustry = (item) => {
      return `${item.raw.business_industry?.name ?? ''}`;
    };

    const formatCurrency = (event) => {
      const rawValue = event.target.value.replace(/[^\d]/g, '');
      const formattedValue = rawValue.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      currencyValue.value = formattedValue;

      emit('update:currencyValue', formattedValue);
    };

    const formatOnBlur = () => {
      if (!currencyValue.value) return;

      const formattedValue = currencyValue.value.replace(
        /\B(?=(\d{3})+(?!\d))/g,
        ','
      );
      currencyValue.value = formattedValue;
      emit('update:currencyValue', formattedValue);
    };

    const handleAutocompleteSearch = (field) => {
      if (searchTimeout.value) {
        clearTimeout(searchTimeout.value);
      }

      searchTimeout.value = setTimeout(() => {
        if (field.searchable && typeof field.search_api === 'function') {
          field.search_api(field);
        }
      }, 300);
    };

    const isAdditionalFieldVisible = (field) => {
      if (!field.additional_field?.visibility_check) return true;
      return field.value !== field.additional_field.visible_value;
    };

    const handleEditorContentChange = (content, fieldName) => {
      const field = formFields.value.find((f) => f.name === fieldName);
      if (field) {
        field.value = content;
        emit('field-updated', { name: fieldName, value: content });
      }
    };

    return {
      formFields,
      currencyValue,
      formatDate,
      getItem,
      getBusinessIndustry,
      formatCurrency,
      formatOnBlur,
      handleAutocompleteSearch,
      isAdditionalFieldVisible,
      handleEditorContentChange,
      formattedCreatedDate,
      formattedUpdatedDate,
    };
  },
};
</script>

<style scoped lang="scss">
.hover-item {
  padding: 10px 12px;
  font-size: 13px;
  transition: background-color 0.2s ease;
}

.hover-item:hover {
  background-color: #f0f0f0;
}
</style>

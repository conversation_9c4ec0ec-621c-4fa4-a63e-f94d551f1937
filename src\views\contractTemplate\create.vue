<template>
  <div>
    <PageTitle
      :items="{
        title: title,
        subTitle: subTitle,
        icon: 'QuestionIcon',
        back: {
          action: () => {
            $router.push({
              name: 'TemplateManagement',
            });
          },
        },
      }"
    ></PageTitle>
    <Form @submit="submitContractTemplate()">
      <v-row v-if="fields">
        <v-col cols="8">
          <!--        <FlashMessage :error="error" />-->
          <v-card class="pa-5 rounded-sm">
            <v-container class="container-main">
              <!-- contract data row1 -->
              <v-row>
                <v-col cols="12">
                  <v-row>
                    <!-- uni name -->
                    <v-col cols="12" md="12" class="mb-n5">
                      <label class="d-block font-14px mb-1">
                        <span>企業名</span>
                        <span class="error--text ml-2 font-12px">必須</span>
                      </label>
                      <Field
                        v-slot="{
                          field: { value, ...fieldWithoutValue },
                          errors,
                        }"
                        name="企業名"
                        rules="required"
                      >
                        <v-autocomplete
                          v-bind="fieldWithoutValue"
                          density="compact"
                          variant="outlined"
                          color="#13ABA3"
                          class="font-16px mt-1"
                          :error-messages="errors"
                          :error="errors.length !== 0"
                          :hide-details="true"
                          :search-input.sync="company.searched_text"
                          @keyup="
                            company.searchable
                              ? company.search_api(company)
                              : false
                          "
                          :loading="company.is_loading"
                          hide-no-data
                          hide-selected
                          :items="company.items"
                          :item-title="company.item_text"
                          :item-value="company.item_value"
                          :placeholder="company.placeholder"
                          v-model="company.value"
                          @update:modelValue="getCompanyAddress(company)"
                          autocomplete="new-password"
                        >
                        </v-autocomplete>
                      </Field>
                    </v-col>

                    <v-col cols="12" md="12" class="mb-n5">
                      <label class="d-block font-14px mb-1">
                        <span>テンプレート名</span>
                        <span class="error--text ml-2 font-12px">必須</span>
                      </label>
                      <Field
                        v-slot="{
                          field: { value, ...fieldWithoutValue },
                          errors,
                        }"
                        name="企画①"
                        rules="required"
                      >
                        <v-text-field
                          v-bind="fieldWithoutValue"
                          class=""
                          :error-messages="errors"
                          :error="errors.length !== 0"
                          :hide-details="true"
                          variant="outlined"
                          v-model="fields.contract_template_name"
                          density="compact"
                          placeholder="企画①"
                          color="grey"
                          id="name"
                          name="name"
                        >
                        </v-text-field>
                      </Field>
                    </v-col>

                    <!-- contract date -->
                    <v-col cols="12" md="12" class="mb-n5">
                      <label class="d-block font-14px mb-1">
                        <span>契約締結日</span>
                      </label>
                      <Field
                        v-slot="{
                          field: { value, ...fieldWithoutValue },
                          errors,
                        }"
                        name="契約締結日"
                      >
                        <DatePicker
                          v-bind="fieldWithoutValue"
                          :field="date_contract_signing"
                          :errors="errors"
                          v-model="date_contract_signing.value"
                          :separator="'/'"
                        >
                        </DatePicker>
                      </Field>
                    </v-col>
                    <!-- company location -->
                    <v-col cols="12" md="12" class="mb-n5">
                      <label class="d-block font-14px mb-1">
                        <span>事業所所在地</span>
                        <span class="error--text ml-2 font-12px">必須</span>
                      </label>
                      <Field
                        v-if="isInitBusinesAddress"
                        v-slot="{
                          field: { value, ...fieldWithoutValue },
                          errors,
                        }"
                        name="事業所所在地"
                        rules="required:事業所所在地"
                        :value="fields.business_address"
                      >
                        <v-text-field
                          v-bind="fieldWithoutValue"
                          class=""
                          :error-messages="errors"
                          :error="errors.length !== 0"
                          :hide-details="true"
                          variant="outlined"
                          v-model="fields.business_address"
                          density="compact"
                          placeholder="事業所所在地"
                          color="grey"
                          id="name"
                          name="name"
                        >
                        </v-text-field>
                      </Field>
                      <v-text-field
                        v-if="!isInitBusinesAddress"
                        v-bind="fieldWithoutValue"
                        class=""
                        :error-messages="errors"
                        :error="errors.length !== 0"
                        :hide-details="true"
                        variant="outlined"
                        v-model="fields.business_address"
                        density="compact"
                        placeholder="事業所所在地"
                        color="grey"
                        id="name"
                        name="name"
                      >
                      </v-text-field>
                    </v-col>
                    <!-- company name -->
                    <v-col cols="12" md="12" class="mb-n5">
                      <label class="d-block font-14px mb-1">
                        <span>名称</span>
                      </label>
                      <Field
                        v-slot="{
                          field: { value, ...fieldWithoutValue },
                          errors,
                        }"
                        name="名称"
                        rules=""
                      >
                        <v-text-field
                          v-bind="fieldWithoutValue"
                          class="bg-input-disabled"
                          :error-messages="errors"
                          :error="errors.length !== 0"
                          :hide-details="true"
                          variant="outlined"
                          :value="getCompanyName(company)"
                          density="compact"
                          placeholder="名称"
                          color="grey"
                          id="name"
                          name="name"
                        >
                        </v-text-field>
                      </Field>
                    </v-col>
                    <!-- name employee -->
                    <v-col cols="12" md="12" class="mb-n5">
                      <label class="d-block font-14px mb-1">
                        <span>使用者職氏名</span>
                        <span class="error--text ml-2 font-12px">必須</span>
                      </label>
                      <Field
                        v-slot="{
                          field: { value, ...fieldWithoutValue },
                          errors,
                        }"
                        name="使用者職氏名"
                        rules="required:使用者職氏名"
                      >
                        <v-text-field
                          v-bind="fieldWithoutValue"
                          class=""
                          :error-messages="errors"
                          :error="errors.length !== 0"
                          :hide-details="true"
                          variant="outlined"
                          v-model="fields.employee_title_name"
                          density="compact"
                          placeholder="代表取締役　○○ ○○"
                          color="grey"
                          id="name"
                          name="name"
                        >
                        </v-text-field>
                      </Field>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
              <!-- 雇用期間 -->
              <v-row class="mt-8">
                <v-col cols="12">
                  <div class="d-flex justify-space-between">
                    <div class="font-18px font-weight-medium">雇用期間</div>
                    <v-icon>$QuestionIcon</v-icon>
                  </div>
                  <v-row>
                    <!-- student name -->
                    <v-col cols="12" md="12" class="mt-4 mb-n5">
                      <Field
                        v-slot="{
                          field: { value, ...fieldWithoutValue },
                          errors,
                        }"
                        name="学生名"
                        ref="observer"
                        rules=""
                      >
                        <v-radio-group
                          v-model="fields.type_employment_period"
                          density="compact"
                          column
                        >
                          <v-radio :value="2" color="grey">
                            <template #label>
                              <span
                                style="opacity: 1; color: #000000de"
                                class="font-14px"
                                >期間の定めあり</span
                              >
                            </template>
                          </v-radio>
                          <label
                            v-if="fields.type_employment_period === 2"
                            class="ml-8 d-block font-14px mb-1"
                          >
                            <span class="text-black">契約締結日</span>
                          </label>
                          <div
                            v-if="fields.type_employment_period === 2"
                            align="center"
                            class="ml-8 mr-1 my-1 d-flex align-center"
                          >
                            <div class="py-0 px-0 full-width">
                              <Field
                                v-slot="{
                                  field: { value, ...fieldWithoutValue },
                                  errors,
                                }"
                                name="date_employment_start"
                                v-model="date_employment_start.value"
                                :rules="{
                                  employmentDateAfterContract:
                                    date_contract_signing.value,
                                }"
                              >
                                <DatePicker
                                  v-bind="fieldWithoutValue"
                                  :field="date_employment_start"
                                  :errors="errors || []"
                                  v-model="fields.date_employment_start"
                                  :separator="'/'"
                                  :isShowMessage="errors[0] === '入力されていない項目があります' ? false : true"
                                >
                                </DatePicker>
                              </Field>
                            </div>
                            <div class="text-center py-0 px-0 mx-4">〜</div>
                            <div class="py-0 px-0 full-width">
                              <Field
                                v-slot="{
                                  field: { value, ...fieldWithoutValue },
                                  errors,
                                }"
                                v-model="date_employment_end.value"
                                name="date_employment_end"
                              >
                                <DatePicker
                                  v-bind="fieldWithoutValue"
                                  :field="date_employment_end"
                                  :errors="errors"
                                  v-model="fields.date_employment_end"
                                  :min="minEndDate"
                                  :separator="'/'"
                                  :isShowMessage="false"
                                >
                                </DatePicker>
                              </Field>
                            </div>
                          </div>
                          <v-radio disabled :value="1" color="primary">
                            <template #label>
                              <span class="font-14px">期間の定めなし</span>
                            </template>
                          </v-radio>
                        </v-radio-group>
                      </Field>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>

              <!-- 更新の有無 -->
              <v-row class="mt-8">
                <v-col cols="12" class="font-14px">
                  <v-row>
                    <v-col cols="12" class="py-1">
                      <div class="font-18px font-weight-medium">更新の有無</div>
                      <!-- updated renew -->
                      <div class="font-14px mt-2">1. 契約の更新の有無</div>
                      <div class="ml-3">
                        <v-radio-group
                          density="compact"
                          class="custom-radio"
                          v-model="fields.type_update_procedure"
                          column
                        >
                          <v-radio :value="1" color="grey">
                            <template #label>
                              <span class="font-14px text-black"
                                >更新する場合があり得る</span
                              >
                            </template>
                          </v-radio>
                          <v-radio disabled :value="2" color="primary">
                            <template #label>
                              <span class="font-14px text-black"
                                >契約の更新はしない</span
                              >
                            </template>
                          </v-radio>
                        </v-radio-group>
                      </div>
                    </v-col>
                  </v-row>
                  <!-- 2. Renewal Judgement Criteria -->
                  <v-row>
                    <v-col cols="12" class="py-1">
                      <div class="font-14px">
                        2. 契約の更新は、次のいずれかにより判断する
                      </div>
                      <ul class="ml-3 small-dot">
                        <li>契約期間満了時の業務量</li>
                        <li>従事している業務の進捗状況</li>
                        <li>本人の能力、業務成績、勤務態度</li>
                        <li>会社の経営状況</li>
                      </ul>
                    </v-col>
                  </v-row>
                  <!-- 3. Renewal Limit -->
                  <v-row>
                    <v-col cols="12" class="py-1">
                      <div class="font-14px">
                        3. 更新上限　有（通算契約期間５年まで）
                      </div>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>

              <!-- 就業の場所 -->
              <v-row class="mt-8">
                <v-col cols="12">
                  <v-row>
                    <v-col cols="12" class="py-1">
                      <div class="font-18px font-weight-medium">就業の場所</div>
                      <label class="d-block font-14px mt-2">
                        <span class="text-black">就業の場所</span>
                        <span class="error--text ml-2 font-12px">必須</span>
                      </label>
                      <!-- Place of employment -->
                      <Field
                        v-slot="{
                          field: { value, ...fieldWithoutValue },
                          errors,
                        }"
                        name="更新の有無"
                        ref="observer"
                        rules="required"
                        :value="fields.work_place"
                      >
                        <v-textarea
                          v-bind="fieldWithoutValue"
                          :error="errors.length !== 0"
                          :hide-details="true"
                          :error-messages="errors"
                          class="text-area mt-1"
                          variant="outlined"
                          v-model="fields.work_place"
                          placeholder="就業の場所"
                          :rows="4"
                        >
                        </v-textarea>
                      </Field>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12" class="py-1 mt-6">
                      <div class="font-18px font-weight-medium">
                        従事すべき業務の内容
                      </div>
                      <label class="d-block font-14px mt-2">
                        <span class="text-black">業務内容</span>
                        <span class="error--text ml-2 font-12px">必須</span>
                      </label>
                      <!-- Content of work to be engaged in -->
                      <Field
                        v-slot="{
                          field: { value, ...fieldWithoutValue },
                          errors,
                        }"
                        name="学生名"
                        ref="observer"
                        rules="required"
                        :value="fields.job_detail"
                      >
                        <v-textarea
                          v-bind="fieldWithoutValue"
                          :error="errors.length !== 0"
                          :hide-details="true"
                          :error-messages="errors"
                          class="text-area mt-1"
                          :rows="4"
                          variant="outlined"
                          v-model="fields.job_detail"
                          :placeholder="defaultDetail"
                        >
                        </v-textarea>
                      </Field>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>

              <!-- 始業・終業の時刻、勤務時間、休憩時間、所定時間外労働、休日労働の有無に関する事項 -->
              <v-row class="mt-8">
                <v-col cols="12">
                  <v-row>
                    <v-col cols="12" class="py-1 font-14px">
                      <div class="d-flex justify-space-between">
                        <div class="font-18px font-weight-medium">
                          始業・終業の時刻、勤務時間、休憩時間、所定時間外労働、休日労働の有無に関する事項
                        </div>
                        <v-icon>$QuestionIcon</v-icon>
                      </div>
                      <div class="mt-4 mb-2">
                        <div class="section-number mb-1">
                          1 始業・終業の時刻
                        </div>
                        始業および終業の時間は本人の決定に委ねる。<br />
                        ただし、５：００～２２：００の時間内とする。
                      </div>

                      <div class="mb-2">
                        <div class="section-number mb-1">2 勤務時間</div>
                        勤務時間は本人の決定に委ねる。<br />
                        ただし、５：００～２２：００の時間内で以下を上限とする。<br />
                        <span class="indent">・1日について８時間</span><br />
                        <span class="indent"
                          >・1週間について４０時間（毎週日曜日を起算日とする）</span
                        ><br />
                        <span class="indent"
                          >・1ヶ月について１１９時間（賃金締切日の翌日を起算日とする）</span
                        ><br />
                        <span class="note"
                          >副業・兼業を行う場合は当社と副業・兼業先で合算した勤務時間は1日について８時間、1週間について４０時間を上限とする。</span
                        >
                      </div>

                      <div class="mb-2">
                        <div class="section-number mb-1">3 休憩時間</div>
                        1日において勤務時間が６時間を超える場合　４５分以上<br />
                        1日において勤務時間が８時間を超える場合　６０分以上
                      </div>

                      <div>
                        <span class="section-number">4</span>
                        法定時間外労働・法定休日労働　無
                      </div>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>

              <!-- 労働時間の記録 -->
              <v-row class="mt-8">
                <v-col cols="12">
                  <v-row>
                    <v-col cols="12" class="py-1 font-14px">
                      <div class="d-flex justify-space-between">
                        <div class="font-18px font-weight-medium">
                          労働時間の記録
                        </div>
                        <v-icon>$QuestionIcon</v-icon>
                      </div>
                      <p class="mt-4">
                        労働時間の管理は自己申告制とし、以下の点に留意する。<br />
                        ・勤務を行った日ごとに当社指定の勤怠管理表に勤務時間・業務内容を入力すること。<br />
                        ・勤務時間は実際に勤務した適正な時間を申告すること。<br />
                        ・始業終業時刻、勤務時間についての上限を守ること。ただし、特別な理由があり上限時間を超えて勤務する場合は管理者に報告の上、承認を得ること。<br />
                        ・会社は申告された勤務時間と実際の労働時間との間に乖離がないか、定期的に確認することがある。<br />
                        ・労働時間の自己申告制は業務の効率化やワークライフバランスの実現の観点から導入しているものである。<br />
                      </p>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>

              <!-- 休日 -->
              <v-row class="mt-8">
                <v-col cols="12">
                  <v-row>
                    <v-col cols="12" class="py-1 font-14px">
                      <div class="d-flex justify-space-between">
                        <div class="font-18px font-weight-medium">休日</div>
                        <v-icon>$QuestionIcon</v-icon>
                      </div>
                      <p class="mt-4">
                        週休２日以上とし、法定休日は４週を通じて４日とする。
                      </p>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>

              <!-- 賃金 -->
              <v-row class="mt-8">
                <v-col cols="12">
                  <v-row class="align-center">
                    <v-col cols="12" class="py-1 font-14px">
                      <div class="d-flex justify-space-between">
                        <div class="font-18px font-weight-medium">賃金</div>
                        <v-icon>$QuestionIcon</v-icon>
                      </div>
                      <v-row class="align-center mt-1">
                        <v-col cols="12" sm="6" lg="2" class="py-1 font-14px">
                          <div style="min-width: 200px;" class="d-flex">
                            <div class="text-black">1 時給制</div>
                            <div class="error--text ml-2 font-12px">必須</div>
                          </div>
                        </v-col>
                        <v-col cols="9" sm="5" lg="2" class="pb-0">
                          <Field
                            v-slot="{
                              field: { value, ...fieldWithoutValue },
                              errors,
                            }"
                            name="時給制"
                            rules="required"
                          >
                            <v-text-field
                              v-bind="fieldWithoutValue"
                              v-model="fields.wage"
                              :error-messages="errors"
                              variant="outlined"
                              type="number"
                              class=""
                              :error="errors.length !== 0"
                              :hide-details="true"
                              density="compact"
                              placeholder="2,000"
                            ></v-text-field>
                          </Field>
                        </v-col>
                        <v-col cols="3" sm="1" lg="1" class="py-1 px-0">
                          <div>円</div>
                        </v-col>
                      </v-row>

                      <!-- Row 2: 時間外に対して支払われる割増賃金率 -->
                      <div class="mt-4"></div>
                      <v-row v-for="rate in rates" :key="rate.label">
                        <v-col cols="12" class="py-0 font-14px">
                          <div>
                            {{
                              rate.label === '法定時間外/月60時間以内'
                                ? '2 時間外に対して支払われる割増賃金率'
                                : ''
                            }}
                          </div>
                        </v-col>
                        <v-col cols="12">
                          <v-row class="align-center" style="min-width: 60px">
                            <v-col cols="12" sm="6" lg="4" class="py-0">
                              <div style="min-width: 200px;" class="d-flex">
                                {{ rate.label }}
                                <div class="error--text ml-2 font-12px"
                                  >必須</div
                                >
                              </div>
                            </v-col>
                            <div>(</div>
                            <!-- add symbol  -->
                            <div class="pt-1 py-0 mx-1" style="width: 80px">
                              <Field
                                v-slot="{
                                  field: { value, ...fieldWithoutValue },
                                  errors,
                                }"
                                :name="rate.label"
                                rules="required"
                                :value="rate.value"
                              >
                                <v-text-field
                                  v-bind="fieldWithoutValue"
                                  v-model="fields[rate.value]"
                                  :error-messages="
                                    errors.length > 0 ? ['必須'] : []
                                  "
                                  variant="outlined"
                                  type="number"
                                  class=""
                                  :error="errors.length !== 0"
                                  :hide-details="true"
                                  density="compact"
                                  placeholder=""
                                  :min="0"
                                  :max="100"
                                  maxlength="3"
                                ></v-text-field>
                              </Field>
                            </div>
                            <v-col sm="1" lg="1" class="py-0 px-0">
                              <div>%)</div>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-row>

                      <!-- Row 3: 賃金締切日 -->
                      <v-row class="align-center">
                        <v-col
                          cols="12"
                          sm="6"
                          lg="3"
                          class="py-0 pr-0 font-14px"
                        >
                          <label class="d-block font-14px">
                            <span class="text-black">3 賃金締切日</span>
                            <span class="error--text ml-2 font-12px">必須</span>
                          </label>
                        </v-col>
                        <v-col cols="1" class="px-0 d-flex justify-end">
                          毎月
                        </v-col>
                        <!-- add symbol -->
                        <div class="pt-1 py-1 mx-1" style="width: 80px">
                          <Field
                            v-slot="{
                              field: { value, ...fieldWithoutValue },
                              errors,
                            }"
                            name="賃金締切日"
                            rules="required"
                            :value="fields.wage_closing_date"
                          >
                            <v-select
                              v-bind="fieldWithoutValue"
                              v-model="fields.wage_closing_date"
                              :items="dateOptions"
                              :error-messages="
                                errors.length > 0 ? ['必須'] : []
                              "
                              variant="outlined"
                              density="compact"
                              :error="errors.length !== 0"
                              :hide-details="true"
                              placeholder=""
                              class="custom-select"
                            ></v-select>
                          </Field>
                        </div>
                        <v-col cols="1" class="py-0 px-0">
                          <div>日</div>
                        </v-col>
                      </v-row>

                      <!-- Row 4: 賃金支払日 -->
                      <v-row class="align-center">
                        <v-col
                          cols="12"
                          sm="6"
                          lg="3"
                          class="pt-0 pr-0 font-14px"
                        >
                          <label class="d-block font-14px">
                            <span class="text-black">4 賃金支払日</span>
                            <span class="error--text ml-2 font-12px">必須</span>
                          </label>
                        </v-col>
                        <v-col cols="1" class="px-0 pt-2 d-flex justify-end"
                          >毎月</v-col
                        >
                        <!-- add symbol  -->
                        <div class="mx-1" style="width: 80px">
                          <Field
                            v-slot="{
                              field: { value, ...fieldWithoutValue },
                              errors,
                            }"
                            name="賃金支払日"
                            rules="required"
                            :value="fields.wage_payment_date"
                          >
                            <v-select
                              v-bind="fieldWithoutValue"
                              v-model="fields.wage_payment_date"
                              :items="dateOptions"
                              :error-messages="
                                errors.length > 0 ? ['必須'] : []
                              "
                              variant="outlined"
                              density="compact"
                              :error="errors.length !== 0"
                              :hide-details="true"
                              placeholder=""
                              class="custom-select"
                            ></v-select>
                          </Field>
                        </div>
                        <v-col cols="1" class="pt-2 px-0">
                          <div>日</div>
                        </v-col>
                      </v-row>

                      <!-- Row 5: 支払方法 -->
                      <v-row>
                        <v-col cols="12" class="font-14px pb-0 py-0">
                          <div>5 支払方法</div>
                        </v-col>
                        <v-col cols="12" class="py-0">
                          本人の同意を得て直接銀行口座に振込にて全額支払う。ただし、法令等で定められているものは、支払の際に控除する。
                        </v-col>
                      </v-row>

                      <!-- Row 6-8: 昇給, 賞与, 退職金 -->
                      <v-row
                        class="align-center"
                        v-for="option in optionsSalary"
                        :key="option.label"
                      >
                        <v-col cols="12" sm="6" lg="4" class="py-0 font-14px">
                          <div>
                            {{ option.label }}
                            <span class="error--text ml-2 font-12px">必須</span>
                          </div>
                        </v-col>
                        <v-col
                          cols="12"
                          sm="6"
                          lg="4"
                          class="py-0 my-0"
                          style="margin-left: -10px"
                        >
                          <Field
                            :name="option.value"
                            :rules="(value) => value !== null && value !== undefined || 'この項目は必須です'"
                            v-slot="{ field: { value, ...fieldWithoutValue }, errors }"
                            :value="fields[option.value]"
                          >
                            <v-radio-group
                              v-bind="fieldWithoutValue"
                              v-model="fields[option.value]"
                              :error="errors.length !== 0"
                              :hide-details="true"
                              density="compact"
                              inline
                            >
                              <v-radio :value="true" color="primary">
                                <template #label>
                                  <span class="font-14px text-black">有</span>
                                </template>
                              </v-radio>
                              <v-radio :value="false" color="primary">
                                <template #label>
                                  <span class="font-14px text-black">無</span>
                                </template>
                              </v-radio>
                            </v-radio-group>
                          </Field>
                        </v-col>
                      </v-row>

                    </v-col>
                  </v-row>
                </v-col>
              </v-row>

              <!-- 退職に関する事項 -->
              <v-row class="mt-10">
                <v-col cols="12">
                  <v-row>
                    <v-col cols="12" class="py-1 font-14px">
                      <div class="font-18px font-weight-medium">
                        退職に関する事項
                      </div>
                      <p class="mt-4 mb-2">
                        1
                        自己都合退職の場合の手続については、原則として３０日前までに申し出なければならない。
                      </p>
                      <p class="my-0 mb-2">2 解雇の事由及び手続</p>
                      <p>
                        ①　身体、精神の障害により、業務に耐えられないとき
                        <br />
                        ②　勤務成績が不良で、就業に適さないと認められたとき<br />
                        ③　協調性がなく、注意および指導しても改善の見込みがないと認められるとき<br />
                        ④　事業の縮小等、やむを得ない業務の都合により必要のあるとき<br />
                        ⑤　事業の運営上、やむを得ない事情、または天災事変その他これに準ずるやむを得ない事情により事業の継続が困難になったとき
                        上記に該当する事由があった場合は、３０日前に予告するか解雇予告手当を支払って解雇する。
                      </p>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>

              <!-- 社会保険の加入状況 -->
              <v-row class="mt-8">
                <v-col cols="12">
                  <v-row>
                    <v-col cols="12" class="py-1 font-14px">
                      <div class="d-flex justify-space-between">
                        <div class="font-18px font-weight-medium">
                          社会保険の加入状況
                        </div>
                        <v-icon>$QuestionIcon</v-icon>
                      </div>
                    </v-col>
                    <v-col cols="9" class="table-header-right-2 py-1 my-0 mx-0">
                      <v-row>
                        <v-col cols="12">
                          <div class="d-flex align-center w-full">
                            <v-checkbox
                              disabled
                              v-model="
                                fields.flg_insurance_industrial_accident_compensation
                              "
                              :value="true"
                              :ripple="false"
                              density="compact"
                              :hide-details="true"
                              color="primary"
                              class="pa-0 ma-0 login-modal"
                            >
                              <template #label>
                                <div class="fw-400 pa-0 text-black font-14px">
                                  労災保険
                                </div>
                              </template>
                            </v-checkbox>
                          </div>
                          <div class="d-flex align-center">
                            <v-checkbox
                              v-model="fields.flg_insurance_unemployment"
                              :value="true"
                              :ripple="false"
                              density="compact"
                              :hide-details="true"
                              color="primary"
                              class="pa-0 ma-0 login-modal"
                            >
                              <template #label>
                                <div class="fw-400 pa-0 text-black font-14px">
                                  雇用保険
                                </div>
                              </template>
                            </v-checkbox>
                          </div>
                          <div class="d-flex align-center">
                            <v-checkbox
                              v-model="fields.flg_insurance_public_health"
                              :value="true"
                              :ripple="false"
                              density="compact"
                              :hide-details="true"
                              color="primary"
                              class="pa-0 ma-0 login-modal"
                            >
                              <template #label>
                                <div class="fw-400 pa-0 text-black font-14px">
                                  健康保険
                                </div>
                              </template>
                            </v-checkbox>
                          </div>
                          <div class="d-flex align-center">
                            <v-checkbox
                              v-model="fields.flg_insurance_employee_pension"
                              :value="true"
                              :ripple="false"
                              density="compact"
                              :hide-details="true"
                              color="primary"
                              class="pa-0 ma-0 login-modal"
                            >
                              <template #label>
                                <div class="fw-400 pa-0 text-black font-14px">
                                  厚生年金保険
                                </div>
                              </template>
                            </v-checkbox>
                          </div>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>

              <!-- 雇用管理の改善等に関する事項に係る相談窓口 -->
              <v-row class="my-10">
                <v-col cols="12">
                  <v-row>
                    <v-col cols="12" class="py-1 font-14px">
                      <div class="font-18px font-weight-medium">
                        雇用管理の改善等に関する事項に係る相談窓口
                      </div>
                      <v-row class="align-center mt-2">
                        <v-col cols="6" lg="2" class="py-1 font-14px">
                          <div style="min-width: 200px;" class="d-flex font-14px">
                            <span class="text-black">担当者職氏名</span>
                            <span class="error--text ml-2 font-12px">必須</span>
                          </div>
                        </v-col>
                        <v-col cols="5" class="pb-0 px-0">
                          <Field
                            v-slot="{
                              field: { value, ...fieldWithoutValue },
                              errors,
                            }"
                            name="担当者職氏名"
                            rules="required"
                          >
                            <v-text-field
                              v-model="fields.consultation_user_title_name"
                              v-bind="fieldWithoutValue"
                              :error-messages="errors"
                              variant="outlined"
                              class=""
                              :error="errors.length !== 0"
                              :hide-details="true"
                              density="compact"
                              placeholder="人事部人事担当　○○○○"
                            ></v-text-field>
                          </Field>
                        </v-col>
                      </v-row>
                      <v-row class="align-center mt-2">
                        <v-col cols="6" lg="2" class="py-1 font-14px">
                          <label class="d-block font-14px">
                            <span class="text-black">email</span>
                            <span class="error--text ml-2 font-12px">必須</span>
                          </label>
                        </v-col>
                        <v-col cols="5" class="pb-0 px-0">
                          <Field
                            v-slot="{
                              field: { value, ...fieldWithoutValue },
                              errors,
                            }"
                            name="email"
                            rules="email|required"
                          >
                            <v-text-field
                              v-bind="fieldWithoutValue"
                              v-model="fields.consultation_user_mail_address"
                              :error-messages="errors"
                              :error="errors.consultation_user_mail_address"
                              :hide-details="true"
                              variant="outlined"
                              class=""
                              density="compact"
                              placeholder="メールアドレス"
                            ></v-text-field>
                          </Field>
                        </v-col>
                      </v-row>
                      <v-row class="align-center mt-2">
                        <v-col cols="6" lg="2" class="py-1 font-14px">
                          <label class="d-block font-14px">
                            <span class="text-black">電話</span>
                            <span class="error--text ml-2 font-12px">必須</span>
                          </label>
                        </v-col>
                        <v-col cols="5" class="pb-0 px-0">
                          <Field
                            v-slot="{
                              field: { value, ...fieldWithoutValue },
                              errors,
                            }"
                            name="電話"
                            rules="required"
                          >
                            <v-text-field
                              v-model="fields.consultation_user_tel"
                              v-bind="fieldWithoutValue"
                              :error-messages="errors"
                              variant="outlined"
                              class=""
                              type="number"
                              :error="errors.length !== 0"
                              :hide-details="true"
                              density="compact"
                              placeholder="電話番号"
                            ></v-text-field>
                          </Field>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-container>
          </v-card>
        </v-col>
        <v-col cols="4">
          <v-card height="150px" class="text-center pt-14">
            <div class="button-width mx-auto">
              <div :class="{ 'px-2': smAndDown }" class="btn-container">
                <v-btn
                  block
                  color="primary"
                  class="white--text mt-1 button-width"
                  type="submit"
                  :disabled="fields?.status >= 2"
                  >保存</v-btn
                >
              </div>
            </div>
          </v-card>
        </v-col>
      </v-row>
    </Form>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { Field, defineRule } from 'vee-validate';
import DatePicker from '@/components/ui/DatePicker.vue';
import { debounce } from 'debounce';
import moment from 'moment';
import { useDisplay } from 'vuetify';
const { smAndDown } = useDisplay();

defineRule('employmentDateAfterContract', (value, [contractDate]) => {
  if (!contractDate) return true; // If contract date is not provided, skip validation

  const contractMoment = moment(contractDate);
  const employmentStartMoment = moment(value);

  if (employmentStartMoment.isBefore(contractMoment)) {
    return '雇用期間は契約締結日以降で設定してください';
  }

  console.log('Contract Date:', contractDate, 'Employment Start Date:', value);

  return true;
});

// Component imports
const store = useStore();
const router = useRouter();

// Component setup
const title = ref('契約管理');
const subTitle = ref('テンプレート新規作成');
const getAllCompany = computed(() => store.getters.getAllCompany);

const getAutoSuggestionText = (item) =>
  `${item.internal_company_id} / ${item.name} ${item.business_industry?.name ?? ''}`;
const searchCompany = debounce(function (field) {
  field.is_loading = true;

  store
    .dispatch('COMPANY_GET_ALL', {
      search: field.searched_text ?? null,
      silent_loading: true,
      page: 1,
      paginate: 10,
      showActive: 1,
    })
    .then(() => {
      // Ensure `items` is initialized before assigning a value
      if (Array.isArray(field.items)) {
        field.items = getAllCompany.value;
      } else {
        // If `items` is not defined or is not an array, initialize it as an array
        field.items = [...getAllCompany.value];
      }

      field.is_loading = false;
    })
    .catch((error) => {
      console.error('Error fetching company data:', error);
    });
}, 500);

const date_employment_start = ref({
  name: 'date_employment_start',
  menu: false,
  value: '',
  date: '',
  locale: 'ja',
});
const date_employment_end = ref({
  name: 'date_employment_end',
  menu: false,
  value: '',
  date: '',
  locale: 'ja',
});
const date_contract_signing = ref({
  name: 'date_contract_signing',
  menu: false,
  value: '',
  date: '',
  locale: 'ja',
  placeholder: '',
});
const dateOptions = ref(Array.from({ length: 31 }, (_, i) => i + 1));
const rates = ref([
  {
    label: '法定時間外/月60時間以内',
    value: 'overtime_pay_rate_under_60_hours',
  },
  { label: '法定時間外/月60時間超', value: 'overtime_pay_rate_over_60_hours' },
  { label: '法定休日', value: 'holiday_pay_rate' },
  { label: '深夜', value: 'night_shift_pay_rate' },
]);

const company = ref({
  label: '企業名',
  name: 'company_id',
  placeholder: '内部ID、または企業名フリガナを入力してください',
  row_class: '',
  item_value: 'id',
  item_text: getAutoSuggestionText,
  searchable: true,
  search_api: searchCompany,
  is_loading: false,
  searched_text: '',
  items: getAllCompany.value,
  value: null,
  rules: 'required',
  requiredChecks: true,
});
const optionsSalary = ref([
  { label: '6 昇給', value: 'flg_salary_increase', placeholder: '0' },
  { label: '7 賞与', value: 'flg_bonus', placeholder: '0' },
  { label: '8 退職金', value: 'flg_retirement_allowance', placeholder: '0' },
]);
const fields = ref(null);
const errors = ref('');
const isInitBusinesAddress = ref(true);

// Field definition for Vee-Validate

const defaultPlace = ref(`(雇入れ直後)
労働者の自宅もしくは当社指定のオンラインミーティング会議が行える場所
(変更の範囲)
労働者の自宅もしくは当社指定のオンラインミーティング会議が行える場所`);

const defaultDetail = ref(`(雇入れ直後) 企画業務
(変更の範囲) 全ての業務への配置転換あり or 企画業務のみ`);

// Initialize data
const initData = () => {
  fields.value = {
    flg_insurance_industrial_accident_compensation: true,
    flg_insurance_unemployment: false,
    flg_insurance_public_health: false,
    flg_insurance_employee_pension: false,
    contract_template_name: null,
    company_id: company?.value?.value,
    type_contract: null,
    original_contract_id: null,
    date_contract_signing: null,
    business_address: null,
    contract_name: '',
    employee_title_name: null,
    type_employment_period: 2,
    date_employment_start: null,
    date_employment_end: null,
    type_update_procedure: 1,
    job_detail: null,
    wage: null,
    status: null,
    internship_student_status: null,
    flg_draft: 0,
    flg_contract_sent: 0,
    datetime_contract_sent: null,
    flg_employment_check: 0,
    flg_hourlypay_check: 0,
    flg_personal_statement_check: 0,
    flg_contract_approved: 0,
    wage_closing_date: 31,
    wage_payment_date: 25,
    work_place: defaultPlace,
    created_at: null,
    updated_at: null,
    consultation_employee_title_name: null,
    consultation_employee_mail_address: null,
    consultation_user_tel: null,
    overtime_pay_rate_under_60_hours: 25,
    overtime_pay_rate_over_60_hours: 50,
    holiday_pay_rate: 35,
    night_shift_pay_rate: 25,
    flg_retirement_allowance: false,
    flg_bonus: false,
    flg_salary_increase: false,
  };
};

// Submit contract template
const submitContractTemplate = async () => {
  try {
    fields.value.company_id = company.value.value;
    if (fields.value.type_employment_period === 1) {
      fields.value.date_employment_start = moment().format('YYYY-MM-DD');
      fields.value.date_employment_end = moment().format('YYYY-MM-DD');
    } else {
      fields.value.date_employment_start = date_employment_start.value.value ? moment(
        date_employment_start.value.value
      ).format('YYYY-MM-DD') : '';
      fields.value.date_employment_end = date_employment_end.value.value ? moment(
        date_employment_end.value.value
      ).format('YYYY-MM-DD') : '';
    }
    fields.value.date_contract_signing = date_contract_signing.value.value
      ? moment(date_contract_signing.value.value).format('YYYY-MM-DD')
      : '';
    fields.value.type_creator = 2; // for admin

    await store.dispatch('CREATE_CONTRACT_TEMPLATES', fields.value);
    router.push({ name: 'TemplateManagement' });
    store.dispatch('updateStatusAlert', {
      showStatusAlert: true,
      statusAlertMessage: 'テンプレートを保存しました',
    });
  } catch (error) {
    errors.value = error.data?.errors || {};
    console.error(error.data.errors);
  }
};

watch(
  () => date_employment_start.value.value,
  (newStartDate) => {
    if (date_contract_signing.value.value) {
      const endDate = moment(date_contract_signing.value.value);
      const startDate = moment(newStartDate);

      // Check if "date_employment_start" is earlier than "date_contract_signing"
      if (startDate.isBefore(endDate)) {
        return true;
      }
    }

    if (newStartDate && date_employment_end.value.value) {
      const endDate = moment(date_employment_end.value.value);
      const startDate = moment(newStartDate);

      if (endDate.isSameOrBefore(startDate)) {
        date_employment_end.value.value = startDate
          .add(1, 'day')
          .format('YYYY/MM/DD');
      }
    }
  }
);

// Company search functionality
const getCompanyName = (item) => {
  if (item.items) {
    const company = item.items.find((company) => company.id === item.value);
    return company ? company.name : null;
  }
};

// Company search functionality
const getCompanyAddress = (item) => {
  if (item.items) {
    const company = item.items.find((company) => company.id === item.value);
    fields.value.business_address = company ? company.office_address : null;
      isInitBusinesAddress.value = false
    setTimeout(() => {
      isInitBusinesAddress.value = true
    }, 100);
  }
};

const minEndDate = computed(() =>
  date_employment_start.value.value
    ? moment(date_employment_start.value.value).format('YYYY-MM-DD')
    : moment().format('YYYY-MM-DD')
);

// Lifecycle hooks
onMounted(() => initData());
</script>

<style lang="scss">
.custom-radio {
  :deep(.v-input--radio-group--column .v-input--radio-group__input) {
    margin: 0 !important;
    padding: 0 !important;
    height: 36px;
    padding-top: 4px !important;
  }
}

:deep(.v-input--selection-controls__input + .v-label) {
  font-size: 14px !important;
}

:deep(.v-input--selection-controls__input) {
  margin-bottom: -3px !important;
}

.small-dot {
  list-style-type: none; /* Remove default bullet */
  padding-left: 17px; /* Add padding for custom bullet */
}

.small-dot li {
  position: relative;
}

.small-dot li::before {
  content: '•'; /* Custom bullet symbol */
  font-size: 0.8em; /* Smaller size for bullet */
  position: absolute;
  left: -10px; /* Position bullet to the left */
  top: 0;
  color: black; /* Optional: Set bullet color */
}

.button-width {
  max-width: 259px !important;
  .btn-container {
    width: 100% !important;
  }
}

:deep(.v-input--selection-controls__input + .v-label) {
  color: #000000de !important;
}

.mw-80 {
  width: 80px;
}
</style>

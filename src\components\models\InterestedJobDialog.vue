<template>
  <v-dialog v-model="getLaunch" max-width="775px">
    <v-card elevation="0" color="white" width="775px">
      <v-card-title class="d-flex justify-end pr-9 pb-2 pt-8">
        <v-btn variant="text" icon @click="closeModal">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-card-title
        v-if="edit"
        class="d-flex justify-center pt-0 pb-8 font-20px"
      >
        編集
      </v-card-title>
      <v-card-title v-else class="d-flex justify-center pt-0 pb-8 font-20px">
        新規登録
      </v-card-title>
      <div class="d-flex flex-column align-center justify-space-between">
        <Form @submit="submitForm" :initial-values="fields">
          <div class="d-flex flex-column align-center justify-center">
            <div class="input-width">
              <label class="d-block font-14px mb-1">プルダウン表示項目</label>
              <Field
                name="job_name"
                rules="required"
                v-slot="{ field, errors }"
              >
                <v-text-field
                  v-bind="field"
                  :error-messages="errors"
                  :error="errors.length > 0"
                  :hide-details="errors.length <= 0"
                  placeholder="入力してください"
                  density="compact"
                  variant="outlined"
                />
              </Field>
            </div>
            <div class="input-width pt-2">
              <label class="d-block font-14px mb-1">表示順位</label>
              <Field name="display_order" v-slot="{ field, errors }">
                <v-text-field
                  v-bind="field"
                  :error-messages="errors"
                  :error="errors.length > 0"
                  :hide-details="errors.length <= 0"
                  placeholder="半角数字を入力してください"
                  type="number"
                  density="compact"
                  variant="outlined"
                />
              </Field>
            </div>
          </div>
          <v-card-actions class="d-flex justify-center pt-7 pb-16">
            <v-btn
              v-if="edit"
              variant="outlined"
              color="primary"
              width="188px"
              height="35px"
              @click="closeModal"
            >
              戻る
            </v-btn>
            <v-btn
              type="submit"
              variant="elevated"
              color="primary"
              class="white--text ml-5"
              width="188px"
              height="35px"
            >
              {{ edit ? '保存' : '登録' }}
            </v-btn>
          </v-card-actions>
        </Form>
      </div>
    </v-card>
  </v-dialog>
</template>

<script>
import { ref, computed, watch } from 'vue';
import { useForm, Field, Form } from 'vee-validate';
import { useStore } from 'vuex';

export default {
  name: 'InterestedJobDialog',
  props: {
    edit: {
      type: Boolean,
      default: true,
    },
    launch: {
      type: Boolean,
      required: true,
    },
    item: {
      type: Object,
      default: null,
    },
  },
  /**
   * Component Setup
   * Handles the creation and editing of interested job entries
   * @param {Object} props - Component props containing dialog state and job data
   * @param {Function} emit - Event emitter for parent communication
   */
  setup(props, { emit }) {
    const store = useStore();

    /**
     * Dialog Control
     * Two-way binding for dialog visibility with parent component
     */
    const getLaunch = computed({
      get() {
        return props.launch;
      },
      set(value) {
        emit('update:launch', value);
      },
    });

    /**
     * Form Fields State
     * Manages job entry form data
     */
    const fields = ref({
      job_name: '', // Name of the job position (プルダウン表示項目)
      display_order: '', // Display order in dropdown (表示順位)
    });

    /**
     * Form Data Watcher
     * Handles form initialization and reset when dialog opens/closes
     */
    watch(
      () => props.launch,
      (val) => {
        if (val && props.item) {
          // Populate form with existing job data
          fields.value.job_name = props.item.job_name || '';
          fields.value.display_order = props.item.display_order || '';
        } else {
          // Reset form for new entry
          fields.value.job_name = '';
          fields.value.display_order = '';
        }
      }
    );

    /**
     * Form Utilities
     * Provides form reset functionality
     */
    const { resetForm } = useForm();

    /**
     * Dialog Actions
     */
    // Handles dialog closure and form reset
    const closeModal = () => {
      getLaunch.value = false; // Close dialog
      resetForm(); // Reset form state
    };

    /**
     * Form Submission Handler
     * Processes form submission for both create and update operations
     * @param {Object} values - Form field values
     */
    const submitForm = async (values) => {
      try {
        if (props.edit) {
          await updateInterestedJob(values); // Update existing job
        } else {
          await store.dispatch('INTERESTED_JOB_CREATE', values); // Create new job
        }
        resetForm(); // Clear form
        emit('refresh'); // Notify parent to refresh data
        store.dispatch('GET_MASTER_DATA'); // Update master data
        closeModal(); // Close dialog
      } catch (error) {
        console.error(error);
      }
    };

    /**
     * Job Update Handler
     * Updates existing job entry with new values
     * @param {Object} values - Updated form values
     */
    const updateInterestedJob = async (values) => {
      try {
        values.id = props.item.id; // Add ID to update request
        await store.dispatch('INTERESTED_JOB_EDIT', values);
      } catch (error) {
        console.error(error);
      }
    };

    // Expose necessary properties and methods
    return {
      getLaunch,
      fields,
      submitForm,
      closeModal,
    };
  },
};
</script>

<style lang="scss">
.input-width {
  width: 543px;
}
</style>

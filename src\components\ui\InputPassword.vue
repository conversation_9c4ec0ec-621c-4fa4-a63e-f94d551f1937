<template>
  <v-text-field
    v-model="_value"
    placeholder="パスワード"
    v-bind="$attrs"
    :append-inner-icon="fieldType ? 'mdi-eye-off' : 'mdi-eye'"
    @click:append-inner="toggleFieldType"
    :type="fieldType ? 'password' : 'text'"
    density="compact"
    variant="outlined"
  />
</template>

<script>
export default {
  name: 'InputPassword',
  props: {
    appendToggleEye: {
      type: Boolean,
      default: false,
    },
    value: {
      type: String,
      default: '',
      required: true,
    },
    placeholder: {
      type: String,
      default: 'パスワード', // Default placeholder
    },
  },
  data() {
    return {
      fieldType: true, // Default to 'password' input type
    };
  },
  computed: {
    _value: {
      get() {
        return this.value;
      },
      set(newValue) {
        this.$emit('update:modelValue', newValue); // Emit Vue 3 'v-model' event
      },
    },
    computedPlaceholder() {
      return this.$t ? this.$t('password') : this.placeholder; // Use translation if available
    },
  },
  methods: {
    toggleFieldType() {
      this.fieldType = !this.fieldType; // Toggle between 'password' and 'text' input type
    },
  },
};
</script>

<style scoped></style>

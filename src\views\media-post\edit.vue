<template>
  <div>
    <PageTitle
      :items="{
        title: 'コラム',
        subTitle: '編集',
        back: {
          action: handleBack,
        },
      }"
    />
    <v-form v-if="!isLoading" @submit.prevent="submitMediaPost()">
      <v-row>
        <v-col cols="8">
          <ColumnInformation
            :sectionTitle="false"
            :basicInformation="columnInformation"
          />
        </v-col>
        <v-col cols="4">
          <v-card height="314px" class="text-center pt-14">
            <div class="button-width mx-auto">
              <div :class="{ 'px-2': smAndDown }" class="btn-container">
                <v-btn
                  @click="openDraftPopup()"
                  variant="outlined"
                  color="#13ABA3"
                  min-width="150px"
                  width="259px"
                >
                  下書き保存
                </v-btn>
                <v-btn
                  @click="submitMediaPost('P')"
                  variant="outlined"
                  class="mt-6"
                  color="#13ABA3"
                  min-width="150px"
                  width="259px"
                >
                  プレビュー
                </v-btn>
                <v-btn
                  @click="submitMediaPost()"
                  color="#13ABA3"
                  class="white--text mt-6"
                  min-width="150px"
                  width="259px"
                >
                  公開
                </v-btn>
                <br />
                <v-btn
                  @click="handleDelete"
                  class="mt-8"
                  color="#E14D56"
                  variant="text"
                >
                  削除
                </v-btn>
              </div>
            </div>
          </v-card>
          <v-card height="106px" class="mt-4">
            <div class="mx-10 pt-10 d-flex justify-center">
              <v-icon color="#8A8A8A">$eyeFill</v-icon>
              <div class="font-18px text-7d ml-2 mr-3">{{ t('views') }}</div>
              <div class="font-18px text-7d">{{ numberOfViews }}</div>
            </div>
          </v-card>
          <ImageUpload :data="imageDetails" />
          <ColumnTag :mediaTag="mediaTags" />
        </v-col>
      </v-row>
    </v-form>

    <SimpleModel
      text="このコラムを下書きとして保存しますか？"
      :dialog="dialog.saveAsDraft"
      @submitSuccess="saveDraft"
      @closeModel="closeDialog('saveAsDraft')"
    />
    <SimpleModel
      text="このコラムを削除しますか？"
      :dialog="dialog.deletePost"
      @submitSuccess="deleteInformation"
      @closeModel="closeDialog('deletePost')"
      submitButtonText="削除する"
    />
  </div>
</template>

<script setup>
/**
 * Media Post Edit Component
 * Handles editing of blog/column posts with draft, preview, and publish functionality
 */
import { ref, computed, onMounted, defineAsyncComponent } from 'vue';
import { useStore } from 'vuex';
import { useRouter, useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
const ColumnInformation = defineAsyncComponent(
  () => import('@/components/pages/PostInputs.vue')
);
const ImageUpload = defineAsyncComponent(
  () => import('@/components/ui/ImageUpload.vue')
);
const ColumnTag = defineAsyncComponent(
  () => import('@/components/pages/column/ColumnTag.vue')
);
const SimpleModel = defineAsyncComponent(
  () => import('@/components/models/SimpleModel.vue')
);
let isLoading = ref(false);
import { useDisplay } from 'vuetify';
import MediaMixin from './media.mixin';

// Display setup
const { smAndDown } = useDisplay();

// Store and router setup
const store = useStore();
const router = useRouter();
const route = useRoute();
const { t } = useI18n();
const {
  imageDetails,
  mediaTags,
  columnInformation,
  isDraftOrPublic,
  dialog,
  isSaved,
  openDraftPopup,
  savePageData,
  saveAction,
  getPageFields,
} = MediaMixin();
/**
 * State Management
 */
const numberOfViews = ref(0);

/**
 * Computed Properties
 * Access to Vuex store getters
 */
const getSingleMedia = computed(() => store.getters.getSingleMedia);
const getPreviewPageURL = computed(() => store.getters.getPreviewPageURL);

/**
 * Navigation Handlers
 */
const handleBack = () => {
  router.push({ name: 'Media' });
};

const closeDialog = (type) => {
  dialog[type] = false;
};

const submitMediaPost = (isDraftCheck = 'N') => {
  isDraftOrPublic.value = '1'; // draft post by default
  let action = saveAction;

  if (isDraftCheck === 'N') {
    isDraftOrPublic.value = '0'; // public post
  } else if (isDraftCheck === 'P') {
    action = (result) => {
      isSaved.value = result.data.data.data.id;
      store.commit('UPDATE_PREVIEW_URL', {
        preview_url: `post-detail/${result.data.data.data.seo_slug}?preview=true`,
      });
      window.open(getPreviewPageURL.value, '_blank');
    };
  }

  savePageData('update', action);
  dialog.saveAsDraft = false;
};

/**
 * Populates form with existing post data
 * Maps API response to form fields
 */
const setPageData = () => {
  // Set column information
  columnInformation.value.forEach((item) => {
    if (item.name === 'display_order_select') {
      if (getSingleMedia.value?.display_order) {
        item.value = 1;
        item.additional_field.value = getSingleMedia.value.display_order;
      }
    } else if (getSingleMedia.value?.[item.name]) {
      item.value = getSingleMedia.value[item.name];
    }
  });

  // Set media tags
  mediaTags.value = getSingleMedia.value.media_tags
    ? getSingleMedia.value.media_tags.map((i) => i.id)
    : [];

  // Set view count
  numberOfViews.value = getSingleMedia.value?.media_views_count;

  // Set image and SEO details
  imageDetails.value.previewImageURL = getSingleMedia.value?.seo_featured_image;
  imageDetails.value.seo_ogp.value = getSingleMedia.value?.seo_ogp;
  imageDetails.value.seo_slug.value = getSingleMedia.value?.seo_slug;
  imageDetails.value.seo_meta_description.value =
    getSingleMedia.value?.seo_meta_description;
};

/**
 * Fetches post data from API
 */
const getDataFromApi = async () => {
  isLoading.value = true;
  try {
    await store.dispatch('MEDIA_POST_GET', { id: route.params.id });
    setPageData();
    setTimeout(() => {
      isLoading.value = false;
    }, 200);
  } catch (error) {
    console.error('Error fetching data:', error);
  }
};

/**
 * Initiates post deletion process
 */
const handleDelete = () => {
  dialog.deletePost = true;
};

/**
 * Saves post as draft
 */
const saveDraft = async () => {
  isDraftOrPublic.value = '1';
  await savePageData('update', () => {
    dialog.saveAsDraft = false;
  });
};

/**
 * Handles post deletion
 * Deletes post and redirects to media list
 */
const deleteInformation = async () => {
  try {
    await store.dispatch('MEDIA_POST_DELETE', { id: route.params.id });
    dialog.deletePost = false;
    router.push({ name: 'Media' });
  } catch (error) {
    console.error('Error deleting post:', error);
  }
};

/**
 * Component initialization
 */
onMounted(() => {
  isLoading.value = true;
  getPageFields();
  getDataFromApi();
});
</script>

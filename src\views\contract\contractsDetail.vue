<template>
  <div>
    <PageTitle
      :items="{
        title: title,
        subTitle: subTitle,
        icon: 'QuestionIcon',
        iconAction: iconAction,
        back: {
          action: () => {
            $router.push({
              name:
                $route.name !== 'UncontractDetail'
                  ? 'Contract'
                  : 'Uncontracted',
            });
          },
        },
      }"
    ></PageTitle>
    <v-row v-if="fields">
      <v-col cols="8">
        <div>
          <v-form>
            <v-card class="pa-5 rounded-sm">
              <p class="bg-input-disabled text-right mt-[-10px]">
                {{ fields.internal_contract_id }}
              </p>
              <v-container class="container-main">
                <!-- contract data row1 -->
                <v-row>
                  <v-col cols="12">
                    <v-row>
                      <!-- student name -->
                      <v-col cols="7" md="7" class="mb-n5">
                        <label class="d-block font-14px mb-1">
                          <span>学生名</span>
                          <span class="error--text ml-2 font-12px"></span>
                        </label>
                        <Field
                          v-slot="{
                            field: { value, ...fieldWithoutValue },
                            errors,
                          }"
                          name="学生名"
                          ref="observer"
                          rules=""
                        >
                          <v-text-field
                            class="bg-input-disabled"
                            :error-messages="errors"
                            :error="errors.length !== 0"
                            :hide-details="true"
                            density="compact"
                            variant="outlined"
                            v-model="fields.student_name"
                            placeholder=""
                          >
                          </v-text-field>
                        </Field>
                      </v-col>
                      <!-- uni name -->
                      <v-col cols="5" md="5" class="mb-n5">
                        <label class="d-block font-14px mb-1">
                          <span>大学名</span>
                        </label>
                        <Field
                          v-slot="{
                            field: { value, ...fieldWithoutValue },
                            errors,
                          }"
                          name="大学名"
                          rules=""
                        >
                          <v-text-field
                            class="bg-input-disabled"
                            :error-messages="errors"
                            :error="errors.length !== 0"
                            :hide-details="true"
                            density="compact"
                            variant="outlined"
                            v-model="fields.education_facility"
                            placeholder="大学名"
                            color="grey"
                          >
                          </v-text-field>
                        </Field>
                      </v-col>
                      <!-- contract type -->
                      <v-col cols="3" md="3" class="mb-n5">
                        <label class="d-block font-14px mb-1">
                          <span>契約種別</span>
                        </label>
                        <Field
                          v-slot="{
                            field: { value, ...fieldWithoutValue },
                            errors,
                          }"
                          name="契約種別"
                          rules=""
                        >
                          <v-text-field
                            class="bg-input-disabled"
                            :error-messages="errors"
                            :error="errors.length !== 0"
                            :hide-details="true"
                            density="compact"
                            variant="outlined"
                            :value="
                              fields.type_contract_value === 1 ? '新規' : '更新'
                            "
                            id="契約種別"
                            name="契約種別"
                            placeholder="契約種別"
                          >
                          </v-text-field>
                        </Field>
                      </v-col>
                      <!-- template -->
                      <v-col cols="9" md="9" class="mb-n5">
                        <label class="d-block font-14px mb-1">
                          <span>テンプレート</span>
                        </label>
                        <Field
                          v-slot="{
                            field: { value, ...fieldWithoutValue },
                            errors,
                          }"
                          name="テンプレート"
                          rules=""
                        >
                          <v-text-field
                            :value="
                              fields.contract_template?.contract_template_name
                            "
                            class="bg-input-disabled"
                            :error-messages="errors"
                            :error="errors.length !== 0"
                            :hide-details="true"
                            density="compact"
                            variant="outlined"
                            placeholder="テンプレート"
                          >
                          </v-text-field>
                        </Field>
                      </v-col>
                      <!-- contract date -->
                      <v-col cols="12" md="12" class="mb-n5">
                        <label class="d-block font-14px mb-1">
                          <span>契約締結日</span>
                          <span class="error--text ml-2 font-12px">必須</span>
                        </label>
                        <Field
                          v-slot="{
                            field: { value, ...fieldWithoutValue },
                            errors,
                          }"
                          name="契約締結日"
                          rules="required:契約締結日"
                        >
                          <v-text-field
                            class="bg-input-disabled"
                            :error-messages="errors"
                            :error="errors.length !== 0"
                            :hide-details="true"
                            density="compact"
                            variant="outlined"
                            :value="
                              formattedDate1(fields.date_contract_signing)
                            "
                            placeholder="雇用開始日以前の日付を記入してください"
                            color="grey"
                            id="契約締結日"
                            name="契約締結日"
                          >
                          </v-text-field>
                        </Field>
                      </v-col>
                      <!-- company location -->
                      <v-col cols="12" md="12" class="mb-n5">
                        <label class="d-block font-14px mb-1">
                          <span>事業所所在地</span>
                          <span class="error--text ml-2 font-12px">必須</span>
                        </label>
                        <Field
                          v-slot="{
                            field: { value, ...fieldWithoutValue },
                            errors,
                          }"
                          name="事業所所在地"
                          rules="required:事業所所在地"
                        >
                          <v-text-field
                            class="bg-input-disabled"
                            :error-messages="errors"
                            :error="errors.length !== 0"
                            :hide-details="true"
                            density="compact"
                            variant="outlined"
                            v-model="fields.business_address"
                            placeholder="事業所所在地"
                            color="grey"
                            id="事業所所在地"
                            name="事業所所在地"
                          >
                          </v-text-field>
                        </Field>
                      </v-col>
                      <!-- company name -->
                      <v-col cols="12" md="12" class="mb-n5">
                        <label class="d-block font-14px mb-1">
                          <span>名称</span>
                        </label>
                        <Field
                          v-slot="{
                            field: { value, ...fieldWithoutValue },
                            errors,
                          }"
                          name="名称"
                          rules=""
                        >
                          <v-text-field
                            class="bg-input-disabled"
                            :error-messages="errors"
                            :error="errors.length !== 0"
                            :hide-details="true"
                            density="compact"
                            variant="outlined"
                            :value="fields?.company?.name"
                            placeholder="名称"
                            color="grey"
                            id="name"
                            name="name"
                          >
                          </v-text-field>
                        </Field>
                      </v-col>
                      <!-- name employee -->
                      <v-col cols="12" md="12" class="mb-n5">
                        <label class="d-block font-14px mb-1">
                          <span>使用者職氏名</span>
                          <span class="error--text ml-2 font-12px">必須</span>
                        </label>
                        <Field
                          v-slot="{
                            field: { value, ...fieldWithoutValue },
                            errors,
                          }"
                          name="使用者職氏名"
                          rules="required:使用者職氏名"
                        >
                          <v-text-field
                            class="bg-input-disabled"
                            :error-messages="errors"
                            :error="errors.length !== 0"
                            :hide-details="true"
                            density="compact"
                            variant="outlined"
                            v-model="fields.employee_title_name"
                            placeholder="使用者職氏名"
                            color="grey"
                            id="使用者職氏名"
                            name="使用者職氏名"
                          >
                          </v-text-field>
                        </Field>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
                <!-- 雇用期間 -->
                <v-row class="mt-8">
                  <v-col cols="12">
                    <div class="d-flex justify-space-between">
                      <div class="font-18px font-weight-medium">雇用期間</div>
                      <v-icon>$QuestionIcon</v-icon>
                    </div>
                    <v-row>
                      <!-- student name -->
                      <v-col cols="12" md="12" class="mb-n5">
                        <Field
                          v-slot="{
                            field: { value, ...fieldWithoutValue },
                            errors,
                          }"
                          name="学生名"
                          ref="observer"
                          rules=""
                        >
                          <v-radio-group
                            :hide-details="true"
                            density="compact"
                            disabled
                            v-model="fields.type_employment_period"
                            column
                            style="margin-left: -6px"
                          >
                            <v-radio :value="1">
                              <template #label>
                                <span class="font-14px">期間の定めなし</span>
                              </template>
                            </v-radio>
                            <v-radio :value="2">
                              <template #label>
                                <span class="font-14px text-black"
                                  >期間の定めあり</span
                                >
                              </template>
                            </v-radio>
                            <div style="margin-left: -4px">
                              <label class="ml-8 d-block font-14px mb-1">
                                <span class="text-black">雇用期間</span>
                                <span class="error--text ml-2 font-12px"
                                  >必須</span
                                >
                              </label>
                              <div
                                v-show="fields.type_employment_period === 2"
                                align="center"
                                class="ml-8 mr-1 my-1 d-flex align-center"
                              >
                                <div
                                  class="py-0 px-0 bg-input-disabled full-width"
                                >
                                  <DatePicker
                                    :field="date_employment_start"
                                    :errors="errors"
                                    disabled
                                    v-model="date_employment_start.value"
                                    :error-messages="errors"
                                    :error="errors.length !== 0"
                                    :hide-details="errors.length <= 0"
                                    @input="error = null"
                                    :separator="'/'"
                                  >
                                  </DatePicker>
                                </div>
                                <div class="text-center py-0 px-0 mx-4">〜</div>
                                <div
                                  class="py-0 px-0 bg-input-disabled full-width"
                                >
                                  <DatePicker
                                    :field="date_employment_end"
                                    :errors="errors"
                                    disabled
                                    v-model="date_employment_end.value"
                                    :error-messages="errors"
                                    :error="errors.length !== 0"
                                    :hide-details="true"
                                    @input="error = null"
                                    :separator="'/'"
                                  >
                                  </DatePicker>
                                </div>
                              </div>
                            </div>
                          </v-radio-group>
                        </Field>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>

                <!-- 更新の有無 -->
                <v-row class="mt-8">
                  <v-col cols="12" class="font-14px">
                    <v-row>
                      <v-col cols="12" class="py-1">
                        <div class="font-18px font-weight-medium">
                          更新の有無
                        </div>
                        <!-- updated renew -->
                        <div class="font-14px mt-2">1. 契約の更新の有無</div>
                        <div>
                          <v-radio-group
                            :hide-details="true"
                            disabled
                            v-model="fields.type_update_procedure"
                            column
                            class="ml-2"
                            density="compact"
                          >
                            <v-radio :value="1">
                              <template #label>
                                <span class="font-14px text-black"
                                  >更新する場合があり得る</span
                                >
                              </template>
                            </v-radio>
                            <v-radio :value="2">
                              <template #label>
                                <span class="font-14px text-black"
                                  >契約の更新はしない</span
                                >
                              </template>
                            </v-radio>
                          </v-radio-group>
                        </div>
                      </v-col>
                    </v-row>
                    <!-- 2. Renewal Judgement Criteria -->
                    <v-row>
                      <v-col cols="12" class="py-1">
                        <div class="font-14px">
                          2. 契約の更新は、次のいずれかにより判断する
                        </div>
                        <ul class="ml-3 small-dot mt-2">
                          <li>契約期間満了時の業務量</li>
                          <li>従事している業務の進捗状況</li>
                          <li>本人の能力、業務成績、勤務態度</li>
                          <li>会社の経営状況</li>
                        </ul>
                      </v-col>
                    </v-row>
                    <!-- 3. Renewal Limit -->
                    <v-row>
                      <v-col cols="12" class="py-1">
                        <div class="font-14px">
                          3. 更新上限　有（通算契約期間５年まで）
                        </div>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>

                <!-- 就業の場所 -->
                <v-row class="mt-8">
                  <v-col cols="12">
                    <v-row>
                      <v-col cols="12" class="py-1">
                        <div class="font-18px font-weight-medium">
                          就業の場所
                        </div>
                        <label class="d-block font-14px mt-2">
                          <span class="text-black">就業の場所</span>
                          <span class="error--text ml-2 font-12px">必須</span>
                        </label>
                        <!-- Place of employment -->
                        <v-textarea
                          class="text-area bg-input-disabled mt-1"
                          height="104px"
                          hide-details
                          :rows="4"
                          variant="outlined"
                          v-model="fields.work_place"
                          placeholder="就業の場所"
                        >
                        </v-textarea>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col cols="12" class="py-1 mt-6">
                        <div class="font-18px font-weight-medium">
                          従事すべき業務の内容
                        </div>
                        <label class="d-block font-14px mt-2">
                          <span class="text-black">業務内容</span>
                          <span class="error--text ml-2 font-12px">必須</span>
                        </label>
                        <!-- Content of work to be engaged in -->
                        <v-textarea
                          class="text-area bg-input-disabled mt-1"
                          height="104px"
                          hide-details
                          :rows="4"
                          variant="outlined"
                          v-model="fields.job_detail"
                          placeholder="業務内容"
                        >
                        </v-textarea>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>

                <!-- 始業・終業の時刻、勤務時間、休憩時間、所定時間外労働、休日労働の有無に関する事項 -->
                <v-row class="mt-8">
                  <v-col cols="12">
                    <v-row>
                      <v-col cols="12" class="py-1 font-14px">
                        <div class="d-flex justify-space-between">
                          <div class="font-18px font-weight-medium">
                            始業・終業の時刻、勤務時間、休憩時間、所定時間外労働、休日労働の有無に関する事項
                          </div>
                          <v-icon>$QuestionIcon</v-icon>
                        </div>
                        <div class="mt-4 mb-2">
                          <div class="section-number mb-1">
                            1 始業・終業の時刻
                          </div>
                          始業および終業の時間は本人の決定に委ねる。<br />
                          ただし、５：００～２２：００の時間内とする。
                        </div>

                        <div class="mb-2">
                          <div class="section-number mb-1">2 勤務時間</div>
                          勤務時間は本人の決定に委ねる。<br />
                          ただし、５：００～２２：００の時間内で以下を上限とする。<br />
                          <span class="indent">・1日について８時間</span><br />
                          <span class="indent"
                            >・1週間について４０時間（毎週日曜日を起算日とする）</span
                          ><br />
                          <span class="indent"
                            >・1ヶ月について１１９時間（賃金締切日の翌日を起算日とする）</span
                          ><br />
                          <span class="note"
                            >副業・兼業を行う場合は当社と副業・兼業先で合算した勤務時間は1日について８時間、1週間について４０時間を上限とする。</span
                          >
                        </div>

                        <div class="mb-2">
                          <div class="section-number mb-1">3 休憩時間</div>
                          1日において勤務時間が６時間を超える場合　４５分以上<br />
                          1日において勤務時間が８時間を超える場合　６０分以上
                        </div>

                        <div>
                          <span class="section-number">4</span>
                          法定時間外労働・法定休日労働　無
                        </div>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>

                <!-- 労働時間の記録 -->
                <v-row class="mt-8">
                  <v-col cols="12">
                    <v-row>
                      <v-col cols="12" class="py-1 font-14px">
                        <div class="d-flex justify-space-between">
                          <div class="font-18px font-weight-medium">
                            労働時間の記録
                          </div>
                          <v-icon>$QuestionIcon</v-icon>
                        </div>
                        <p class="mt-4">
                          労働時間の管理は自己申告制とし、以下の点に留意する。<br />
                          ・勤務を行った日ごとに当社指定の勤怠管理表に勤務時間・業務内容を入力すること。<br />
                          ・勤務時間は実際に勤務した適正な時間を申告すること。<br />
                          ・始業終業時刻、勤務時間についての上限を守ること。ただし、特別な理由があり上限時間を超えて勤務する場合は管理者に報告の上、承認を得ること。<br />
                          ・会社は申告された勤務時間と実際の労働時間との間に乖離がないか、定期的に確認することがある。<br />
                          ・労働時間の自己申告制は業務の効率化やワークライフバランスの実現の観点から導入しているものである。<br />
                        </p>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>

                <!-- 休日 -->
                <v-row class="mt-8">
                  <v-col cols="12">
                    <v-row>
                      <v-col cols="12" class="py-1 font-14px">
                        <div class="d-flex justify-space-between">
                          <div class="font-18px font-weight-medium">休日</div>
                          <v-icon>$QuestionIcon</v-icon>
                        </div>
                        <p class="mt-4">
                          週休２日以上とし、法定休日は４週を通じて４日とする。
                        </p>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>

                <!-- 賃金 -->
                <v-row class="mt-8">
                  <v-col cols="12">
                    <v-row class="align-center">
                      <v-col cols="12" class="py-1 font-14px">
                        <div class="d-flex justify-space-between">
                          <div class="font-18px font-weight-medium">賃金</div>
                        </div>
                        <v-row class="align-center mt-1">
                          <v-col cols="12" sm="6" lg="2" class="py-1 font-14px">
                            <div style="min-width: 200px;" class="d-flex">
                              <div class="text-black">1 時給制</div>
                              <div class="error--text ml-2 font-12px">必須</div>
                            </div>
                          </v-col>
                          <v-col cols="9" sm="5" lg="2" class="pb-0">
                            <Field
                              v-slot="{
                                field: { value, ...fieldWithoutValue },
                                errors,
                              }"
                              name="時給制"
                            >
                              <v-text-field
                                v-model="fields.wage"
                                :error-messages="errors"
                                density="compact"
                                variant="outlined"
                                class="bg-input-disabled"
                                :error="errors.length !== 0"
                                :hide-details="true"
                                placeholder=""
                              ></v-text-field>
                            </Field>
                          </v-col>
                          <v-col cols="3" sm="1" lg="1" class="py-1 px-0">
                            <div>円</div>
                          </v-col>
                        </v-row>

                        <!-- Row 2: 時間外に対して支払われる割増賃金率 -->
                        <div class="mt-4"></div>
                        <v-row v-for="rate in rates" :key="rate.label">
                          <v-col
                            style="min-width: 230px"
                            cols="12"
                            class="py-0 font-14px"
                          >
                            <div>
                              {{
                                rate.label === '法定時間外/月60時間以内'
                                  ? '2 時間外に対して支払われる割増賃金率'
                                  : ''
                              }}
                            </div>
                          </v-col>
                          <v-col cols="12">
                            <v-row class="align-center">
                              <v-col
                                style="min-width: 230px"
                                cols="12"
                                sm="6"
                                lg="3"
                                class="py-0"
                              >
                                <div>
                                  {{ rate.label }}
                                  <span class="error--text ml-2 font-12px"
                                    >必須</span
                                  >
                                </div>
                              </v-col>
                              <div>(</div>
                              <!-- add symbol  -->
                              <div class="pt-1 py-0 mx-1 mw-80">
                                <Field
                                  v-slot="{
                                    field: { value, ...fieldWithoutValue },
                                    errors,
                                  }"
                                  :name="rate.label"
                                >
                                  <v-text-field
                                    v-model="fields[rate.value]"
                                    :error-messages="errors"
                                    density="compact"
                                    variant="outlined"
                                    class="bg-input-disabled"
                                    :error="errors.length !== 0"
                                    :hide-details="true"
                                    placeholder=""
                                  ></v-text-field>
                                </Field>
                              </div>
                              <v-col sm="1" lg="1" class="py-0 px-0">
                                <div>%)</div>
                              </v-col>
                            </v-row>
                          </v-col>
                        </v-row>

                        <!-- Row 3: 賃金締切日 -->
                        <v-row class="align-center">
                          <v-col
                            cols="12"
                            sm="6"
                            lg="2"
                            class="py-0 pr-0 font-14px"
                          >
                            <div style="min-width: 200px;" class="d-flex font-14px">
                              <div class="text-black">3 賃金締切日</div>
                              <div class="error--text ml-2 font-12px"
                                >必須</div
                              >
                            </div>
                          </v-col>
                          <v-col cols="1" class="px-0 d-flex justify-end"
                            >毎月</v-col
                          >
                          <!-- add symbol  -->
                          <div class="pt-1 py-0 mx-1 mw-80">
                            <Field
                              v-slot="{
                                field: { value, ...fieldWithoutValue },
                                errors,
                              }"
                              name="賃金締切日"
                            >
                              <v-text-field
                                v-model="fields.wage_closing_date"
                                :error-messages="errors"
                                density="compact"
                                variant="outlined"
                                class="bg-input-disabled"
                                :error="errors.length !== 0"
                                :hide-details="true"
                                placeholder=""
                              ></v-text-field>
                            </Field>
                          </div>
                          <v-col cols="1" class="py-0 px-0">
                            <div>日</div>
                          </v-col>
                        </v-row>

                        <!-- Row 4: 賃金支払日 -->
                        <v-row class="align-center">
                          <v-col
                            cols="12"
                            sm="6"
                            lg="2"
                            class="pt-0 pr-0 font-14px"
                          >
                            <div style="min-width: 200px;" class="d-flex font-14px">
                              <div class="text-black">4 賃金支払日</div>
                              <div class="error--text ml-2 font-12px"
                                >必須</div
                              >
                            </div>
                          </v-col>
                          <v-col cols="1" class="px-0 pt-0 d-flex justify-end"
                            >毎月</v-col
                          >
                          <!-- add symbol  -->
                          <div class="pt-1 py-0 mx-1 mw-80">
                            <Field
                              v-slot="{
                                field: { value, ...fieldWithoutValue },
                                errors,
                              }"
                              name="賃金締切日"
                            >
                              <v-text-field
                                v-model="fields.wage_payment_date"
                                :error-messages="errors"
                                density="compact"
                                variant="outlined"
                                class="bg-input-disabled"
                                :error="errors.length !== 0"
                                :hide-details="true"
                                placeholder=""
                              ></v-text-field>
                            </Field>
                          </div>
                          <v-col cols="1" class="pt-0 px-0">
                            <div>日</div>
                          </v-col>
                        </v-row>

                        <!-- Row 5: 支払方法 -->
                        <v-row>
                          <v-col cols="12" class="font-14px pb-0 py-0">
                            <div>5 支払方法</div>
                          </v-col>
                          <v-col cols="12" class="py-0">
                            本人の同意を得て直接銀行口座に振込にて全額支払う。ただし、法令等で定められているものは、支払の際に控除する。
                          </v-col>
                        </v-row>

                        <!-- Row 6-8: 昇給, 賞与, 退職金 -->
                        <v-row
                          class="align-center"
                          v-for="option in optionsSalary"
                          :key="option.label"
                        >
                          <v-col cols="12" sm="6" lg="3" class="py-0 font-14px">
                            <div>
                              {{ option.label }}
                              <span class="error--text ml-2 font-12px"
                                >必須</span
                              >
                            </div>
                          </v-col>
                          <v-col
                            cols="12"
                            sm="6"
                            lg="4"
                            class="py-0 my-0"
                            style="margin-left: -10px"
                          >
                            <v-radio-group
                              :hide-details="true"
                              disabled
                              v-model="fields[optionsSalary.value]"
                              density="compact"
                              inline
                            >
                              <v-radio value="有">
                                <template #label>
                                  <span class="font-14px text-black">有</span>
                                </template>
                              </v-radio>
                              <v-radio value="無">
                                <template #label>
                                  <span class="font-14px text-black">無</span>
                                </template>
                              </v-radio>
                            </v-radio-group>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>

                <!-- 退職に関する事項 -->
                <v-row class="mt-12">
                  <v-col cols="12">
                    <v-row>
                      <v-col cols="12" class="py-1 font-14px">
                        <div class="font-18px font-weight-medium">
                          退職に関する事項
                        </div>
                        <p class="mt-4 mb-2">
                          1
                          自己都合退職の場合の手続については、原則として３０日前までに申し出なければならない。
                        </p>
                        <p class="my-0 mb-2">2 解雇の事由及び手続</p>
                        <p>
                          ①　身体、精神の障害により、業務に耐えられないとき
                          <br />
                          ②　勤務成績が不良で、就業に適さないと認められたとき<br />
                          ③　協調性がなく、注意および指導しても改善の見込みがないと認められるとき<br />
                          ④　事業の縮小等、やむを得ない業務の都合により必要のあるとき<br />
                          ⑤　事業の運営上、やむを得ない事情、または天災事変その他これに準ずるやむを得ない事情により事業の継続が困難になったとき
                          上記に該当する事由があった場合は、３０日前に予告するか解雇予告手当を支払って解雇する。
                        </p>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>

                <!-- 社会保険の加入状況 -->
                <v-row class="mt-8">
                  <v-col cols="12">
                    <v-row>
                      <v-col cols="12" class="py-1 font-14px">
                        <div class="d-flex justify-space-between">
                          <div class="font-18px font-weight-medium">
                            社会保険の加入状況
                          </div>
                          <v-icon>$QuestionIcon</v-icon>
                        </div>
                      </v-col>
                      <v-col
                        cols="9"
                        class="table-header-right-2 py-1 my-0 mx-0"
                      >
                        <v-row>
                          <v-col cols="12">
                            <div class="d-flex align-center py-0 my-0">
                              <v-checkbox
                                disabled
                                v-model="
                                  fields.flg_insurance_industrial_accident_compensation
                                "
                                :value="1"
                                :ripple="false"
                                density="compact"
                                :hide-details="true"
                                color="#8E8E8E"
                                class="pa-0 ma-0 login-modal"
                              >
                                <template #label>
                                  <div class="fw-400 pa-0 text-black font-14px">
                                    労災保険
                                  </div>
                                </template>
                              </v-checkbox>
                            </div>
                            <div class="d-flex align-center py-0 my-0">
                              <v-checkbox
                                v-model="fields.flg_insurance_unemployment"
                                :value="1"
                                disabled
                                :ripple="false"
                                density="compact"
                                :hide-details="true"
                                color="#8E8E8E"
                                class="pa-0 ma-0 login-modal"
                              >
                                <template #label>
                                  <div class="fw-400 pa-0 text-black font-14px">
                                    雇用保険
                                  </div>
                                </template>
                              </v-checkbox>
                            </div>
                            <div class="d-flex align-center py-0 my-0">
                              <v-checkbox
                                v-model="fields.flg_insurance_public_health"
                                :value="1"
                                disabled
                                :ripple="false"
                                density="compact"
                                :hide-details="true"
                                color="#8E8E8E"
                                class="pa-0 ma-0 login-modal"
                              >
                                <template #label>
                                  <div class="fw-400 pa-0 text-black font-14px">
                                    健康保険
                                  </div>
                                </template>
                              </v-checkbox>
                            </div>
                            <div class="d-flex align-center py-0 my-0">
                              <v-checkbox
                                v-model="fields.flg_insurance_employee_pension"
                                :value="1"
                                disabled
                                :ripple="false"
                                density="compact"
                                :hide-details="true"
                                color="#8E8E8E"
                                class="pa-0 ma-0 login-modal"
                              >
                                <template #label>
                                  <div class="fw-400 pa-0 text-black font-14px">
                                    厚生年金保険
                                  </div>
                                </template>
                              </v-checkbox>
                            </div>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>

                <!-- 雇用管理の改善等に関する事項に係る相談窓口 -->
                <v-row class="my-10">
                  <v-col cols="12">
                    <v-row>
                      <v-col cols="12" class="py-1 font-14px">
                        <div class="font-18px font-weight-medium">
                          雇用管理の改善等に関する事項に係る相談窓口
                        </div>
                        <v-row class="align-center mt-2">
                          <v-col cols="6" lg="2" class="py-1 font-14px">
                            <label class="d-block font-14px">
                              <span class="text-black">担当者職氏名</span>
                              <span class="error--text ml-2 font-12px"
                                >必須</span
                              >
                            </label>
                          </v-col>
                          <v-col cols="5" class="pb-0 px-0">
                            <Field
                              v-slot="{
                                field: { value, ...fieldWithoutValue },
                                errors,
                              }"
                              name="担当者職氏名"
                              rules="required"
                            >
                              <v-text-field
                                v-model="fields.consultation_user_title_name"
                                :error-messages="errors"
                                density="compact"
                                variant="outlined"
                                class="bg-input-disabled"
                                :error="errors.length !== 0"
                                :hide-details="true"
                                placeholder="人事部人事担当　○○○○"
                              ></v-text-field>
                            </Field>
                          </v-col>
                        </v-row>
                        <v-row class="align-center mt-2">
                          <v-col cols="6" lg="2" class="py-1 font-14px">
                            <label class="d-block font-14px">
                              <span class="text-black">email</span>
                              <span class="error--text ml-2 font-12px"
                                >必須</span
                              >
                            </label>
                          </v-col>
                          <v-col cols="5" class="pb-0 px-0">
                            <Field
                              v-slot="{
                                field: { value, ...fieldWithoutValue },
                                errors,
                              }"
                              name="email"
                              rules="email|required"
                            >
                              <v-text-field
                                v-model="fields.consultation_user_mail_address"
                                :error-messages="errors"
                                density="compact"
                                variant="outlined"
                                class="bg-input-disabled"
                                :error="errors.length !== 0"
                                :hide-details="true"
                                placeholder="メールアドレス"
                              ></v-text-field>
                            </Field>
                          </v-col>
                        </v-row>
                        <v-row class="align-center mt-2">
                          <v-col cols="6" lg="2" class="py-1 font-14px">
                            <label class="d-block font-14px">
                              <span class="text-black">電話</span>
                              <span class="error--text ml-2 font-12px"
                                >必須</span
                              >
                            </label>
                          </v-col>
                          <v-col cols="5" class="pb-0 px-0">
                            <Field
                              v-slot="{
                                field: { value, ...fieldWithoutValue },
                                errors,
                              }"
                              name="電話"
                              rules="required"
                            >
                              <v-text-field
                                v-model="fields.consultation_user_tel"
                                :error-messages="errors"
                                density="compact"
                                variant="outlined"
                                class="bg-input-disabled"
                                :error="errors.length !== 0"
                                :hide-details="true"
                                placeholder="電話電話番号"
                              ></v-text-field>
                            </Field>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-container>
            </v-card>
          </v-form>
        </div>
      </v-col>
      <v-col cols="4">
        <v-card height="264px" class="text-center pt-14">
          <div class="button-width mx-auto">
            <div :class="{ 'px-2': smAndDown }" class="btn-container">
              <v-btn block class="full-width" variant="outlined" disabled
                >下書き保存</v-btn
              >
              <br />
              <v-btn
                @click="generatePdf()"
                block
                variant="outlined"
                type="button"
                color="#13ABA3"
                class="mt-6"
                >プレビュー</v-btn
              >
              <br />
              <v-btn
                block
                color="#B8B8B8"
                class="white--text mt-6 button-width"
                disabled
                >学生に送付</v-btn
              >
            </div>
          </div>
        </v-card>
      </v-col>
    </v-row>
    <ContractDetailPDF v-if="isDownloadPDF" ref="pdfDocumentContractAdmin" />
  </div>
</template>

<script>
import { Field } from 'vee-validate';
import {
  ref,
  reactive,
  computed,
  onMounted,
  nextTick,
  defineAsyncComponent,
} from 'vue';
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import ContractDetailPDF from '@/views/contract/contractDetailPdf.vue';
const DatePicker = defineAsyncComponent(
  () => import('@/components/ui/DatePicker.vue')
);
import ContractMixin from './contract.mixin';
import moment from 'moment';
import { useDisplay } from 'vuetify';

export default {
  name: 'ContractDetail',
  components: {
    DatePicker,
    ContractDetailPDF,
  },
  mixins: [ContractMixin],
  setup() {
    const store = useStore();
    const route = useRoute();

    const formattedDate1 = (item) => {
      return item ? moment(item).format('YYYY年M月D日') : '';
    };
    const minDate = moment().format('YYYY/MM/DD');
    const { mdAndUp, smAndDown } = useDisplay();

    const isDownloadPDF = ref(false);
    const pdfDocumentContractAdmin = ref(null);
    const selectedRadio = ref('fixed');
    const title = ref('契約管理');
    const subTitle = ref('労働条件通知書編集');

    const date_employment_start = reactive({
      name: 'date_employment_start',
      menu: false,
      value: '',
      date: '',
      locale: 'ja',
    });

    const date_employment_end = reactive({
      name: 'date_employment_end',
      menu: false,
      value: '',
      date: '',
      locale: 'ja',
    });
    const date_contract_signing = reactive({
      name: 'date_contract_signing',
      menu: false,
      value: '',
      date: '',
      locale: 'ja',
    });

    const rates = ref([
      {
        label: '法定時間外/月60時間以内',
        value: 'overtime_pay_rate_under_60_hours',
      },
      {
        label: '法定時間外/月60時間超',
        value: 'overtime_pay_rate_over_60_hours',
      },
      { label: '法定休日', value: 'holiday_pay_rate' },
      { label: '深夜', value: 'night_shift_pay_rate' },
    ]);

    const optionsSalary = ref([
      { label: '6 昇給', value: 'flg_salary_increase' },
      { label: '7 賞与', value: 'flg_bonus' },
      { label: '8 退職金', value: 'flg_retirement_allowance' },
    ]);

    const fields = ref(null);
    const paymentDate = ref(null);
    const closingDate = ref(null);

    const getSingleContract = computed(() => store.getters.getSingleContract);

    const generatePdf = async () => {
      store.dispatch('API_PROCESSING', true); // Start loading
      isDownloadPDF.value = true;

      // Wait for the component to render and get the ref
      await nextTick();

      // Verify if pdfDocumentContractAdmin is accessible
      if (
        pdfDocumentContractAdmin.value &&
        typeof pdfDocumentContractAdmin.value.generatePdf === 'function'
      ) {
        pdfDocumentContractAdmin.value.generatePdf();
      } else {
        console.error(
          'generatePdf is not a function or pdfDocumentContractAdmin is not set'
        );
      }
    };

    const iconAction = () => {
      const url = store.getters.getInformationURL[6].url;
      window.open(url, '_blank');
    };

    onMounted(async () => {
      // get contract after update from original to KN with status contract 3
      if (store.getters.getSingleGeneratedContractAdmin && store.getters.getSingleGeneratedContractAdmin?.id == route.params.id) {
        fields.value = store.getters.getSingleGeneratedContractAdmin
        addDefaultData();
        const start = fields.value?.date_employment_start
        ? moment(fields.value.date_employment_start)
        : '';
        const end = fields.value?.date_employment_end
          ? moment(fields.value.date_employment_end)
          : '';
        const approve = fields.value?.date_contract_signing
          ? moment(fields.value.date_contract_signing)
          : '';

        date_employment_start.value = start ? start.format('YYYY/MM/DD') : '';
        date_employment_end.value = end ? end.format('YYYY/MM/DD') : '';
        date_contract_signing.value = approve ? approve.format('YYYY/MM/DD') : '';
        store.dispatch('REMOVE_SINGLE_CONTRACT');
        return
      }

      //  get contract like ordinary
      await store.dispatch('CONTRACT_GET', route.params.id);
      fields.value = getSingleContract.value;
      addDefaultData();
      const start = fields.value?.date_employment_start
        ? moment(fields.value.date_employment_start)
        : '';
      const end = fields.value?.date_employment_end
        ? moment(fields.value.date_employment_end)
        : '';
      const approve = fields.value?.date_contract_signing
        ? moment(fields.value.date_contract_signing)
        : '';

      date_employment_start.value = start ? start.format('YYYY/MM/DD') : '';
      date_employment_end.value = end ? end.format('YYYY/MM/DD') : '';
      date_contract_signing.value = approve ? approve.format('YYYY/MM/DD') : '';
    });

    const addDefaultData = () => {
      // Compare created_at and updated_at
      const createdAt = fields.value.created_at;
      const updatedAt = fields.value.updated_at;
      // Parse the date-time strings using moment
      const momentCreatedAt = moment(createdAt);
      const momentUpdatedAt = moment(updatedAt);
      // Check if the moment objects are equal
      const areEqual = momentCreatedAt.isSame(momentUpdatedAt);
      if (fields.value.status == 1 && fields.value.type_format == 1 && areEqual) {
        // Get the current date
        const now = moment();
        // Calculate the start date (1st day of the next month)
        const nextMonth = now.clone().add(1, 'month').startOf('month');
        fields.value.date_employment_start = nextMonth.format('YYYY-MM-DD');
        // Calculate the end date (last day of the month after next next month)
        const endMonth = now.clone().add(3, 'month').endOf('month');
        fields.value.date_employment_end = endMonth.format('YYYY-MM-DD');
      }
    };

    return {
      isDownloadPDF,
      selectedRadio,
      title,
      subTitle,
      date_employment_start,
      date_employment_end,
      rates,
      optionsSalary,
      fields,
      paymentDate,
      closingDate,
      getSingleContract,
      generatePdf,
      iconAction,
      Field,
      formattedDate1,
      mdAndUp,
      smAndDown,
      minDate,
      pdfDocumentContractAdmin,
    };
  },
};
</script>

<style lang="scss">
.custom-radio {
  :deep(.v-input--radio-group--column .v-input--radio-group__input) {
    margin: 0 !important;
    padding: 0 !important;
    height: 36px;
    padding-top: 4px !important;
  }
}

:deep(.v-input--selection-controls__input + .v-label) {
  font-size: 14px !important;
}

:deep(.v-input--selection-controls__input) {
  margin-bottom: -3px !important;
}

.small-dot {
  list-style-type: none; /* Remove default bullet */
  padding-left: 17px; /* Add padding for custom bullet */
}

.small-dot li {
  position: relative;
}

.small-dot li::before {
  content: '•'; /* Custom bullet symbol */
  font-size: 0.8em; /* Smaller size for bullet */
  position: absolute;
  left: -10px; /* Position bullet to the left */
  top: 0;
  color: black; /* Optional: Set bullet color */
}

.button-width {
  max-width: 259px !important;
  .btn-container {
    width: 100% !important;
  }
}

:deep(.v-input--selection-controls__input + .v-label) {
  color: #000000de !important;
}

.mw-80 {
  width: 80px;
}
</style>

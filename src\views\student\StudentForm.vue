<template>
  <div class="full-width border-radius elevation bg-white">
    <div
      class="form white d-flex flex-column align-center justify-start h-1000 mh-1000"
    >
      <div>
        <Form v-if="form && form.family_name" @submit="submit">
          <div
            class="d-flex flex-column ma-4 align-center justify-start main mt-16 mb-16"
          >
            <div
              class="d-flex justify-space-between align-start test full-width"
            >
              <div id="details">
                <v-row justify="space-between" no-gutters>
                  <!-- surname -->
                  <v-col cols="auto">
                    <div class="input-width">
                      <label class="d-block font-14px mb-1">
                        <span>姓</span>
                      </label>
                      <Field
                        v-slot="{
                          field: { value, ...fieldWithoutValue },
                          errors,
                        }"
                        name="family_name"
                        rules="required"
                        :value="form.family_name"
                      >
                        <v-text-field
                          v-bind="fieldWithoutValue"
                          :error-messages="errors"
                          :error="errors.length !== 0"
                          :hide-details="errors.length <= 0"
                          variant="outlined"
                          density="compact"
                          bg-color="white"
                          class="v-field--variant-outlined"
                          v-model="form.family_name"
                        >
                        </v-text-field>
                      </Field>
                    </div>
                  </v-col>
                  <!-- name -->
                  <v-col cols="auto">
                    <div class="input-width">
                      <label class="d-block font-14px mb-1">
                        <span>名</span>
                      </label>
                      <Field
                        v-slot="{
                          field: { value, ...fieldWithoutValue },
                          errors,
                        }"
                        name="first_name"
                        rules="required"
                        :value="form.first_name"
                      >
                        <v-text-field
                          v-bind="fieldWithoutValue"
                          :error-messages="errors"
                          :error="errors.length !== 0"
                          :hide-details="errors.length <= 0"
                          variant="outlined"
                          density="compact"
                          bg-color="white"
                          class="v-field--variant-outlined"
                          v-model="form.first_name"
                        >
                        </v-text-field>
                      </Field>
                    </div>
                  </v-col>
                  <!-- surname -->
                  <v-col cols="auto" class="mt-2">
                    <div class="input-width">
                      <label class="d-block font-14px mb-1">
                        <span>セイ</span>
                      </label>
                      <Field
                        v-slot="{
                          field: { value, ...fieldWithoutValue },
                          errors,
                        }"
                        name="family_name_furigana"
                        rules="required|full_width_katakana"
                        :value="form.family_name_furigana"
                      >
                        <v-text-field
                          v-bind="fieldWithoutValue"
                          :error-messages="errors"
                          :error="errors.length !== 0"
                          :hide-details="errors.length <= 0"
                          variant="outlined"
                          density="compact"
                          bg-color="white"
                          class="v-field--variant-outlined"
                          v-model="form.family_name_furigana"
                        >
                        </v-text-field>
                      </Field>
                    </div>
                  </v-col>
                  <!-- name -->
                  <v-col cols="auto" class="mt-2">
                    <div class="input-width">
                      <label class="d-block font-14px mb-1">
                        <span>メイ</span>
                      </label>
                      <Field
                        v-slot="{
                          field: { value, ...fieldWithoutValue },
                          errors,
                        }"
                        name="first_name_furigana"
                        rules="required|full_width_katakana"
                        :value="form.first_name_furigana"
                      >
                        <v-text-field
                          v-bind="fieldWithoutValue"
                          :error-messages="errors"
                          :error="errors.length !== 0"
                          :hide-details="errors.length <= 0"
                          variant="outlined"
                          density="compact"
                          bg-color="white"
                          class="v-field--variant-outlined"
                          v-model="form.first_name_furigana"
                        >
                        </v-text-field>
                      </Field>
                    </div>
                  </v-col>
                  <!-- email -->
                  <v-col cols="12" class="mt-2">
                    <label class="d-block font-14px mb-1">
                      <span>メールアドレス</span>
                    </label>
                    <Field
                      v-slot="{
                        field: { value, ...fieldWithoutValue },
                        errors,
                      }"
                      name="email_valid"
                      rules="required|email"
                      :value="form.email_valid"
                    >
                      <v-text-field
                        v-bind="fieldWithoutValue"
                        :error-messages="errors"
                        :error="errors.length !== 0"
                        :hide-details="errors.length <= 0"
                        variant="outlined"
                        density="compact"
                        bg-color="white"
                        class="v-field--variant-outlined"
                        v-model="form.email_valid"
                      >
                      </v-text-field>
                    </Field>
                  </v-col>
                  <!-- school name -->
                  <v-col cols="12" class="mt-2">
                    <label class="d-block font-14px mb-1">
                      <span>学校名</span>
                    </label>
                    <Field
                      v-slot="{
                        field: { value, ...fieldWithoutValue },
                        errors,
                      }"
                      name="education_facility_id"
                      rules="required"
                      :value="form.education_facility_id"
                    >
                      <v-autocomplete
                        v-bind="fieldWithoutValue"
                        :error-messages="errors"
                        :error="errors.length !== 0"
                        :hide-details="errors.length <= 0"
                        variant="outlined"
                        density="compact"
                        bg-color="white"
                        class="v-field--variant-outlined"
                        v-model="form.education_facility_id"
                        :items="getEducationFacilities || []"
                        item-title="name"
                        item-value="id"
                        autocomplete="new-password"
                      >
                      </v-autocomplete>
                    </Field>
                  </v-col>
                  <!-- big field -->
                  <v-col cols="12" class="mt-2">
                    <Field
                      v-slot="{
                        field: { value, ...fieldWithoutValue },
                        errors,
                      }"
                      rules="required"
                      name="big_field_id"
                      :value="form.big_field_id"
                    >
                      <v-select
                        v-bind="fieldWithoutValue"
                        variant="outlined"
                        density="compact"
                        bg-color="white"
                        class="v-field--variant-outlined"
                        :error-messages="errors"
                        :error="errors.length !== 0"
                        :hide-details="errors.length <= 0"
                        :items="bigFieldItems || []"
                        :disabled="isBigFieldDisabled"
                        item-title="name"
                        item-value="id"
                        @update:modelValue="bigFieldChange"
                        v-model="form.big_field_id"
                        base-color="rgba(0, 0, 0, 0.87)"
                        menu-icon="mdi-chevron-down"
                      >
                      </v-select>
                    </Field>
                  </v-col>
                  <!-- small field -->
                  <v-col cols="12" class="mt-2">
                    <Field
                      v-slot="{
                        field: { value, ...fieldWithoutValue },
                        errors,
                      }"
                      rules="required"
                      name="small_field_id"
                      :value="form.small_field_id"
                    >
                      <v-select
                        v-bind="fieldWithoutValue"
                        :error-messages="errors"
                        :error="errors.length !== 0"
                        :hide-details="errors.length <= 0"
                        :items="smallFieldItems || []"
                        variant="outlined"
                        density="compact"
                        bg-color="white"
                        class="v-field--variant-outlined"
                        item-title="name"
                        item-value="id"
                        :disabled="isSmallFieldDisabled"
                        v-model="form.small_field_id"
                        base-color="rgba(0, 0, 0, 0.87)"
                        menu-icon="mdi-chevron-down"
                      >
                      </v-select>
                    </Field>
                  </v-col>
                  <!-- study detail -->
                  <v-col cols="12" class="mt-2">
                    <Field
                      v-slot="{
                        field: { value, ...fieldWithoutValue },
                        errors,
                      }"
                      rules="required"
                      name="study_detail"
                      :value="form.study_detail"
                    >
                      <v-text-field
                        v-bind="fieldWithoutValue"
                        variant="outlined"
                        density="compact"
                        bg-color="white"
                        class="v-field--variant-outlined"
                        v-model="form.study_detail"
                      >
                      </v-text-field>
                    </Field>
                  </v-col>
                  <!-- year to graduate  -->
                  <v-col cols="12" class="mt-2">
                    <label class="d-block font-14px mb-1">
                      <span>卒業予定年</span>
                    </label>
                    <div class="d-flex justify-center align-center">
                      <!-- year -->
                      <Field
                        v-slot="{
                          field: { value, ...fieldWithoutValue },
                          errors,
                        }"
                        name="graduate_year"
                        rules="required"
                        :value="form.graduate_year"
                      >
                        <v-select
                          v-bind="fieldWithoutValue"
                          :error-messages="errors"
                          :error="Array.isArray(errors) && errors.length > 0"
                          :hide-details="!errors || errors.length === 0"
                          :items="yearList || []"
                          variant="outlined"
                          density="compact"
                          bg-color="white"
                          class="v-field--variant-outlined"
                          v-model="form.graduate_year"
                          base-color="rgba(0, 0, 0, 0.87)"
                          menu-icon="mdi-chevron-down"
                        >
                        </v-select>
                      </Field>
                      <label class="font-14px ml-4 mr-3">
                        <span>年</span>
                      </label>
                      <Field
                        v-slot="{
                          field: { value, ...fieldWithoutValue },
                          errors,
                        }"
                        name="graduate_month"
                        rules="required"
                        :value="form.graduate_month"
                      >
                        <v-select
                          v-bind="fieldWithoutValue"
                          :error-messages="errors"
                          :error="errors.length !== 0"
                          :hide-details="errors.length <= 0"
                          variant="outlined"
                          density="compact"
                          bg-color="white"
                          class="v-field--variant-outlined"
                          v-model="form.graduate_month"
                          :items="monthList || []"
                          base-color="rgba(0, 0, 0, 0.87)"
                          menu-icon="mdi-chevron-down"
                        >
                        </v-select>
                      </Field>
                      <label class="font-14px ml-4">
                        <span>月</span>
                      </label>
                    </div>
                  </v-col>
                </v-row>
              </div>
              <div id="description">
                <label class="d-block font-14px mb-1">
                  <span>自己PR</span>
                </label>
                <Field
                  v-slot="{ field: { value, ...fieldWithoutValue }, errors }"
                  name="self_introduction"
                  :value="form.self_introduction"
                >
                  <v-textarea
                    v-bind="fieldWithoutValue"
                    class="text-area v-field--variant-outlined"
                    height="260px"
                    hide-details
                    variant="outlined"
                    density="compact"
                    bg-color="white"
                    placeholder="自己PR"
                    v-model="form.self_introduction"
                    :readonly="false"
                  >
                  </v-textarea>
                </Field>
                <!-- 履歴書URL  -->
                <div class="mt-2">
                  <label class="d-block font-14px mb-1">
                    <span>履歴書URL</span>
                  </label>
                  <Field
                    v-slot="{ field: { value, ...fieldWithoutValue }, errors }"
                    name="resume_url"
                    rules="required"
                    :value="form.resume_url"
                  >
                    <v-text-field
                      v-bind="fieldWithoutValue"
                      :error-messages="errors"
                      :error="errors.length !== 0"
                      :hide-details="errors.length <= 0"
                      variant="outlined"
                      density="compact"
                      bg-color="white"
                      class="v-field--variant-outlined"
                      v-model="form.resume_url"
                    >
                    </v-text-field>
                  </Field>
                </div>
                <!-- 資格・実績URL  -->
                <div class="mt-2">
                  <label class="d-block font-14px mb-1">
                    <span>資格・実績URL</span>
                  </label>
                  <Field
                    v-slot="{ field: { value, ...fieldWithoutValue }, errors }"
                    name="qualification_url"
                    :value="form.qualification_url"
                  >
                    <v-text-field
                      v-bind="fieldWithoutValue"
                      :error-messages="errors"
                      :error="errors.length !== 0"
                      :hide-details="errors.length <= 0"
                      variant="outlined"
                      density="compact"
                      bg-color="white"
                      class="v-field--variant-outlined"
                      v-model="form.qualification_url"
                    >
                    </v-text-field>
                  </Field>
                </div>
                <!-- interested job -->
                <div class="mt-2">
                  <label class="d-block font-14px mb-1">
                    <span>興味のある仕事</span>
                  </label>
                  <Field
                    v-slot="{ field: { value, ...fieldWithoutValue }, errors }"
                    name="interested_job_id_1"
                    :value="form.interested_job_id_1"
                  >
                    <v-select
                      v-bind="fieldWithoutValue"
                      :error-messages="errors"
                      :error="errors.length !== 0"
                      :hide-details="errors.length <= 0"
                      variant="outlined"
                      density="compact"
                      bg-color="white"
                      class="v-field--variant-outlined"
                      item-title="text"
                      item-value="id"
                      v-model="form.interested_job_id_1"
                      :items="jobList || []"
                      no-data-text="データがありません"
                      base-color="rgba(0, 0, 0, 0.87)"
                      menu-icon="mdi-chevron-down"
                    >
                    </v-select>
                  </Field>
                  <div class="mt-2"></div>
                  <Field
                    v-slot="{ field: { value, ...fieldWithoutValue }, errors }"
                    name="text_interested_job_1"
                    :value="form.text_interested_job_1"
                  >
                    <v-text-field
                      v-bind="fieldWithoutValue"
                      :error-messages="errors"
                      :error="errors.length !== 0"
                      :hide-details="errors.length <= 0"
                      variant="outlined"
                      density="compact"
                      bg-color="white"
                      class="v-field--variant-outlined"
                      v-model="form.text_interested_job_1"
                    >
                    </v-text-field>
                  </Field>
                  <div class="mt-2"></div>
                  <Field
                    v-slot="{ field: { value, ...fieldWithoutValue }, errors }"
                    name="interested_job_id_2"
                    :value="form.interested_job_id_2"
                  >
                    <v-select
                      v-bind="fieldWithoutValue"
                      :error-messages="errors"
                      :error="errors.length !== 0"
                      :hide-details="errors.length <= 0"
                      variant="outlined"
                      density="compact"
                      bg-color="white"
                      class="v-field--variant-outlined"
                      item-title="text"
                      item-value="id"
                      v-model="form.interested_job_id_2"
                      :items="jobList || []"
                      no-data-text="データがありません"
                      base-color="rgba(0, 0, 0, 0.87)"
                      menu-icon="mdi-chevron-down"
                    >
                    </v-select>
                  </Field>
                  <div class="mt-2"></div>
                  <Field
                    v-slot="{ field: { value, ...fieldWithoutValue }, errors }"
                    name="text_interested_job_2"
                    :value="form.text_interested_job_2"
                  >
                    <v-text-field
                      v-bind="fieldWithoutValue"
                      :error-messages="errors"
                      :error="errors.length !== 0"
                      :hide-details="errors.length <= 0"
                      variant="outlined"
                      density="compact"
                      bg-color="white"
                      class="v-field--variant-outlined"
                      v-model="form.text_interested_job_2"
                    >
                    </v-text-field>
                  </Field>
                  <div class="mt-2"></div>
                  <Field
                    v-slot="{ field: { value, ...fieldWithoutValue }, errors }"
                    name="interested_job_id_3"
                    :value="form.interested_job_id_3"
                  >
                    <v-select
                      v-bind="fieldWithoutValue"
                      :error-messages="errors"
                      :error="errors.length !== 0"
                      :hide-details="errors.length <= 0"
                      variant="outlined"
                      density="compact"
                      bg-color="white"
                      class="v-field--variant-outlined"
                      item-title="text"
                      item-value="id"
                      v-model="form.interested_job_id_3"
                      :items="jobList || []"
                      no-data-text="データがありません"
                      base-color="rgba(0, 0, 0, 0.87)"
                      menu-icon="mdi-chevron-down"
                    >
                    </v-select>
                  </Field>
                  <div class="mt-2"></div>
                  <Field
                    v-slot="{ field: { value, ...fieldWithoutValue }, errors }"
                    name="text_interested_job_3"
                    :value="form.text_interested_job_3"
                  >
                    <v-text-field
                      v-bind="fieldWithoutValue"
                      :error-messages="errors"
                      :error="errors.length !== 0"
                      :hide-details="errors.length <= 0"
                      variant="outlined"
                      density="compact"
                      bg-color="white"
                      class="v-field--variant-outlined"
                      v-model="form.text_interested_job_3"
                    >
                    </v-text-field>
                  </Field>
                </div>
              </div>
            </div>
            <!-- active switch -->
            <div class="d-flex align-center justify-center full-width">
              <v-switch
                v-if="form.status != 3"
                color="primary"
                :label="switchLabel"
                class="font-14px pt-0 mt-9 fw-400"
                :model-value="form.status"
                @update:modelValue="onSwitchChange"
                hide-details
              ></v-switch>
            </div>
            <v-text-field v-if="form.status === 3">
              <v-sheet
                color="transparent mt-5 d-flex flex-column align-center justify-center"
              >
                <p class="font-18px mb-2 color-violet">退会済のユーザーです</p>
                <p class="text-333 font-14px">
                  退会理由:
                  <span class="text-light-dark">{{
                    getWithdrawlReason(form.withdrawal_reason)
                  }}</span>
                </p>
              </v-sheet>
            </v-text-field>
            <!-- submit -->
            <v-row no-gutters class="full-width mt-4">
              <v-col class="">
                <v-btn
                  class="px-0 justify-start no-shadow no-border"
                  @click="dialog.submit = true"
                  flat
                  color="transparent"
                >
                  <v-icon>$WarningRed</v-icon>
                  <span class="ml-2 text-red">削除する</span>
                </v-btn>
              </v-col>
              <v-col offset="1" class="font-14px">
                <v-btn
                  v-if="form.status != 3"
                  width="148px"
                  height="35px"
                  color="primary"
                  type="submit"
                  >更新する</v-btn
                >
              </v-col>
              <v-col></v-col>
            </v-row>
          </div>
        </Form>
      </div>
    </div>
    <SuccessModel
      :text="alertText"
      :buttonText="`一覧へ戻る`"
      :routeName="routeName"
      :dialog="successDialog"
      @closeModel="successDialog = false"
    >
    </SuccessModel>
    <SimpleModel
      :text="`このユーザーを削除しますか？`"
      :dialog="dialog.submit"
      :submitButtonText="`削除する`"
      @submitSuccess="deleteInformation"
      @closeModel="dialog.submit = false"
    >
    </SimpleModel>
  </div>
</template>
<script>
import {
  ref,
  reactive,
  computed,
  watch,
  onMounted,
  defineAsyncComponent,
} from 'vue';
import { useStore } from 'vuex';
const SimpleModel = defineAsyncComponent(
  () => import('@/components/models/SimpleModel.vue')
);
const SuccessModel = defineAsyncComponent(
  () => import('@/components/models/SuccessModel.vue')
);
import moment from 'moment';
import { useRouter, useRoute } from 'vue-router';

export default {
  components: {
    SimpleModel,
    SuccessModel,
  },
  props: {
    form: {
      required: true,
      type: Object,
      default: () => ({}),
    },
  },
  /**
   * Component Setup
   * Handles student form data management and interactions
   * @param {Object} props - Component props containing form data
   * @param {Function} emit - Event emitter for parent communication
   */
  setup(props, { emit }) {
    // Core Vue utilities
    const store = useStore();
    const router = useRouter();
    const route = useRoute();

    /**
     * Reactive State Management
     */
    const dialog = reactive({
      submit: false, // Controls delete confirmation dialog
    });
    const membership = ref({});
    // Generate year list for graduation (current year + 10 years)
    const yearList = ref(
      Array.from({ length: 11 }, (_, i) => moment().year() + i)
    );
    // Generate month list (1-12)
    const monthList = ref(Array.from({ length: 12 }, (_, i) => i + 1));

    // Form state management
    const switchStatus = ref(false); // Active/Inactive switch state
    const alertText = ref(''); // Alert message text
    const successDialog = ref(false); // Success dialog visibility
    const routeName = ref(''); // Navigation route name
    const alert = ref(true); // Alert visibility

    // Category and field management
    const categoryList = ref(null); // List of all categories
    const currentCategoryid = ref(null); // Selected category ID
    const bigFieldItems = ref([]); // Major field options
    const smallFieldItems = ref([]); // Minor field options
    const isBigFieldDisabled = ref(true); // Major field select state
    const isSmallFieldDisabled = ref(true); // Minor field select state
    const jobList = ref([]); // Available job options

    /**
     * Computed Properties
     */
    // Active/Inactive status label (Japanese)
    const switchLabel = computed(() => {
      return props.form.status ? 'アクティブ' : 'インアクティブ';
    });

    // Education facilities from Vuex store
    const getEducationFacilities = computed(
      () => store.getters.getEducationFacilities
    );

    // Selected major field tracking
    const selectedBigField = computed(() => props.form.big_field_id);

    /**
     * Category and Domain Data Processing
     * Combines categories with their respective domains
     */
    const categoryAndDomainData = computed(() => {
      const domains = store.getters.getDomains || [];
      const categories = store.getters.getCategories || [];

      return categories.map((category) => {
        const matchedDomains = domains.filter(
          (domain) => domain.category_id === category.id
        );

        return {
          ...category,
          domains: matchedDomains,
        };
      });
    });

    /**
     * Field Management Methods
     */
    // Fetches and formats major field options
    const getBigFieldLocal = () => {
      const data = store.getters.getBigFieldData || [];
      bigFieldItems.value = data.map((item) => ({
        id: item.id,
        name: item.big_field_name,
      }));
      isBigFieldDisabled.value = false;
    };

    // Fetches and filters minor field options based on selected major field
    const getSmallFieldLocal = () => {
      const data = store.getters.getSmallFieldData || [];
      smallFieldItems.value = data
        .filter((item) => item.big_field_id === props.form.big_field_id)
        .map((item) => ({
          id: item.id,
          name: item.small_field_name,
        }));
      isSmallFieldDisabled.value = false;
    };

    // Resets minor field selection when major field changes
    const bigFieldChange = () => {
      props.form.small_field_id = null;
    };

    /**
     * Form Action Handlers
     */
    // Emits form submission event
    const submit = () => {
      emit('updateStudentDetails', alert.value);
    };

    // Retrieves withdrawal reason text from master data
    const getWithdrawlReason = (val) => {
      return store.getters.getMasterData.withdrawl_reasons.find(
        (reason) => reason.id == val
      ).name;
    };

    // Handles student record deletion
    const deleteInformation = () => {
      store
        .dispatch('DELETE_STUDENT_RECORD', props.form.student_id)
        .then(() => {
          alertText.value = `学生情報を削除しました。`; // Student information deleted
          routeName.value = `Students`;
          successDialog.value = true;
        });
    };

    // Updates student active status
    const onSwitchChange = (value) => {
      props.form.status = value;
    };

    /**
     * Lifecycle Hooks and Watchers
     */
    onMounted(async () => {
      getBigFieldLocal();
      await store.dispatch('GET_EDUCATION__FACILITY_DATA');
      if (props.form.big_field_id) {
        getSmallFieldLocal();
      }
      categoryList.value = categoryAndDomainData.value;
      // Initialize job list from master data
      jobList.value = store.getters.getMasterData?.interested_jobs?.data.map(
        (job) => ({
          id: job.id,
          text: job.job_name,
        })
      );
    });

    // Update minor fields when major field changes
    watch(selectedBigField, getSmallFieldLocal);

    // Sync form status with props
    watch(
      () => props.form,
      (newVal) => {
        props.form.status = newVal.status ? true : false;
      },
      { immediate: true, deep: true }
    );

    // Expose necessary properties and methods to template
    return {
      dialog,
      membership,
      yearList,
      monthList,
      switchStatus,
      alertText,
      successDialog,
      routeName,
      alert,
      categoryList,
      currentCategoryid,
      bigFieldItems,
      smallFieldItems,
      isBigFieldDisabled,
      isSmallFieldDisabled,
      jobList,
      switchLabel,
      selectedBigField,
      bigFieldChange,
      submit,
      getWithdrawlReason,
      deleteInformation,
      getBigFieldLocal,
      getSmallFieldLocal,
      getEducationFacilities,
      onSwitchChange,
    };
  },
};
</script>

<style src="./index.scss" lang="scss"></style>
<style src="@/styles/forms.scss" lang="scss"></style>

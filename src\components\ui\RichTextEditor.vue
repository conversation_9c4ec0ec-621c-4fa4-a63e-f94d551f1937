<template>
  <div class="pos-relative" :style="cssVars" :id="id">
    <QuillEditor
      v-model:content="text"
      theme="snow"
      :modules="modules"
      :toolbar="customToolbar"
      ref="quillEditor"
      @update:content="handleTextChange"
      contentType="html"
      class="ql-editor-scroll"
    />
    <div v-if="isCounter" class="char-counter">
      {{ currentInputNumber }} / 5,000文字
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, nextTick } from 'vue';
import { QuillEditor } from '@vueup/vue-quill';
import Quill from 'quill'; // Import Quill
import QuillResizeImage from 'quill-resize-image'; // Import the new image resize module
import '@vueup/vue-quill/dist/vue-quill.snow.css';

// Register the new module
Quill.register('modules/resize', QuillResizeImage);

export default {
  name: 'PostEditor',
  components: {
    QuillEditor,
  },
  props: {
    id: {
      type: String,
      default: '',
      required: true,
    },
    content: {
      type: String,
      default: '',
      required: true,
    },
    height: {
      type: String,
      default: '400px',
    },
    headings: {
      type: Object,
      required: false,
    },
    isCounter: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, { emit }) {
    // Reactive state
    const quillEditor = ref(null);
    const text = ref(props.content);
    const cssGenerationTimeout = ref(null);

    // Computed properties for dynamic styles
    const cssVars = computed(() => ({
      'min-height': props.height,
    }));

    // Custom toolbar configuration
    const customToolbar = [
      ['bold', 'underline'], // Bold, Underline
      [{ header: 3 }, { header: 4 }], // h3, h4 headers
      [{ 'indent': '-1'}, { 'indent': '+1' }], 
      [{ list: 'ordered' }, { list: 'bullet' }], // Ordered and unordered list
      [{ align: [] }], // Align left, center, right, justify
      [{ font: [] }], // Font selection
      [{ size: [] }], // Font size
      [{ color: [] }, { background: [] }], // Text color, background color
      ['link', 'image'], // Link and image
    ];

    const maxCharacters = 5000;
    const currentInputNumber = ref(0);

    // Text change handler with debounce-like behavior
    const handleTextChange = () => {
      if (quillEditor.value) {
        const quill = quillEditor.value.getQuill(); // Get Quill instance
        const plainText = quill.getText().trim(); // Get text without HTML tags

        if (props.isCounter) {
          if (plainText.length > maxCharacters) {
            quill.deleteText(maxCharacters, plainText.length); // Remove excess text immediately
          }

          currentInputNumber.value = quill.getText().trim().length; // Ensure correct count
        }
      }

      emit('update:content', text.value); // Emit updated content
    };

    // Function to dynamically generate styles for list items (OL/UL)
    const generateDynamicStyles = () => {
      nextTick(() => {
        const stylesToBeGenerated = [];
        const children =
          document.querySelector(`#${props.id} .ql-editor`)?.children || [];

        let ulIndex = 1;
        let olIndex = 1;

        for (let i = 0; i < children.length; i++) {
          const child = children[i];
          if (child.tagName === 'UL' || child.tagName === 'OL') {
            const isUL = child.tagName.toLowerCase() === 'ul';
            for (let j = 0; j < child.children.length; j++) {
              const listItem = child.children[j];
              const cssProps = {
                bulletColor: null,
                listIndex: j + 1,
                parentType: isUL ? 'ul' : 'ol',
                parentIndex: isUL ? ulIndex : olIndex,
              };

              const firstChild = listItem.firstElementChild;
              if (firstChild) {
                cssProps.bulletColor = firstChild.style.color;
                stylesToBeGenerated.push(cssProps);
              }
            }

            if (isUL) ulIndex++;
            else olIndex++;
          }
        }

        applyGeneratedStyles(stylesToBeGenerated);
      });
    };

    // Apply dynamically generated styles to the document head
    const applyGeneratedStyles = (styles) => {
      let css = '';
      styles.forEach((item) => {
        css += `
        #${props.id} .ql-editor ${item.parentType}:nth-of-type(${item.parentIndex}) li:nth-child(${item.listIndex}):before {
          color: ${item.bulletColor} !important;
        }
        `;
      });

      const existingStyle = document.getElementById(`${props.id}-style`);
      if (existingStyle) existingStyle.remove();

      const customStyle = document.createElement('style');
      customStyle.setAttribute('id', `${props.id}-style`);
      customStyle.setAttribute('type', 'text/css');
      customStyle.innerHTML = css;
      document.head.appendChild(customStyle);
    };

    // Setup logic to run on component mount
    onMounted(() => {
      if (quillEditor.value && props.isCounter) {
        const quill = quillEditor.value.getQuill();

        quill.on('text-change', (delta, oldDelta, source) => {
          let plainText = quill.getText();

          // Remove spaces and newlines from count
          const filteredText = plainText.replace(/[\s\n]/g, '');

          if (filteredText.length > maxCharacters) {
            quill.deleteText(maxCharacters, plainText.length); // Immediately delete excess text
          }

          // Update the count excluding spaces and newlines
          currentInputNumber.value = filteredText.length;
        });

        quill.root.addEventListener('paste', (event) => {
          event.preventDefault();
          let text = (event.clipboardData || window.clipboardData).getData(
            'text'
          );

          // Remove spaces and newlines from pasted text count
          const currentText = quill.getText().replace(/[\s\n]/g, '');
          text = text.replace(/[\s\n]/g, '');

          if (currentText.length + text.length > maxCharacters) {
            text = text.substring(0, maxCharacters - currentText.length);
          }

          quill.insertText(quill.getLength(), text);
        });
      }

      // Update heading buttons on load
      const updateHeadingButtons = () => {
        const headingButtons = document.querySelectorAll('button.ql-header');
        headingButtons.forEach((item) => {
          const attribute = item.getAttribute('value');
          if (attribute == 3) item.innerText = 'h3';
          if (attribute == 4) item.innerText = 'h4';
        });
      };

      updateHeadingButtons(); // Call function on mounted
    });

    const modules = {
      name: 'quillResizeImage',
      module: QuillResizeImage,
    };

    return {
      quillEditor,
      text,
      cssVars,
      customToolbar,
      handleTextChange,
      modules,
      currentInputNumber,
    };
  },
};
</script>

<style lang="scss">
.ql-editor-scroll .ql-editor {
  height: 450px !important;
  overflow-y: auto !important;
  padding-right: 10px !important;
}
.char-counter {
  position: absolute;
  right: 0;
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.pos-relative {
  position: relative;
}
</style>

<template>
  <div class="px-5" :class="[items.length > 0 ? '' : 'h-585']">
    <div class="bg-white py-5">
      <h5 class="px-5 font-weight-medium">結果受け取り済み</h5>
      <DataTable
        :headers="headers"
        :items="items"
        :page="1"
        flat
        elevation="0"
        :disablePagination="true"
        class="application-table"
        :footer="false"
      >
        <template v-slot:item.company_name="{ item }">
          <div
            class="text-dark-blue font-14px fw-400 mouse-pointer"
            @click.stop="
              $router.push({
                name: 'CorporateDetails',
                params: { id: item.company.id },
              })
            "
          >
            {{ item.company.name }}
          </div>
        </template>
        <template v-slot:item.title="{ item }">
          <div
            class="cursor-pointer"
            @click.stop="
              $router.push({
                name: 'InternshipPostEdit',
                params: {
                  id: item.internship_post_id,
                },
              })
            "
          >
            <v-sheet
              :elevation="0"
              height="20"
              color="transparent"
              class="text-dark-blue font-14px fw-400 text-truncate mouse-pointer"
              >{{ item.internship_post.title }}</v-sheet
            >
          </div>
        </template>
        <template v-slot:item.application_details="{ item }">
          <div class="position-relative pl-5" @click="showDetailApp(item)">
            <fileSVG />
          </div>
        </template>
        <template v-slot:item.created_at="{ item }">
          <div class="font-14px fw-400">
            {{ dateFormat(item.created_at, 'YYYY/MM/DD') }}
          </div>
        </template>
        <template v-slot:item.status="{ item }">
          <v-chip
            variant="flat"
            dense
            size="small"
            v-if="item.status === 1"
            color="#5E94E4"
            class="font-12px white--text"
            ><div class="px-4 white--text">応募済</div></v-chip
          >
          <v-chip
            variant="flat"
            dense
            size="small"
            v-if="item.status === 2"
            color="#EE6C9B"
            class="font-12px white--text"
            ><div class="px-4 white--text">応募返信済</div></v-chip
          >
          <v-chip
            variant="flat"
            dense
            size="small"
            v-if="item.status === 3"
            color="#60D1CB"
            class="font-12px white--text"
            ><div class="px-4 white--text">合格</div></v-chip
          >
          <v-chip
            variant="flat"
            dense
            size="small"
            v-if="item.status === 4"
            color="#A7A7A7"
            class="font-12px white--text"
            ><div class="px-4 white--text">不合格</div>
          </v-chip>
          <v-chip
            variant="flat"
            dense
            size="small"
            v-if="item.status === 5"
            color="#C771B5"
            class="font-12px white--text"
            ><div class="px-4 white--text">辞退</div></v-chip
          >
          <v-chip
            variant="flat"
            dense
            size="small"
            v-if="item.status === 6"
            color="#DC8360"
            class="font-12px white--text"
            ><div class="px-4 white--text">連絡とれず</div></v-chip
          >
          <v-chip
            variant="flat"
            dense
            size="small"
            v-if="item.status === 99"
            color="#A7A7A7"
            class="font-12px white--text"
            ><div class="px-4 white--text">その他</div></v-chip
          >
        </template>
      </DataTable>
      <ApplicationDetailDialog
        text="応募内容"
        :dialog="dialog.isShowDetailApp"
        :application="application"
        @submitSuccess="dialog.isShowDetailApp = false"
        @closeModel="dialog.isShowDetailApp = false"
      ></ApplicationDetailDialog>
    </div>
  </div>
</template>
<script>
import ApplicationDetailDialog from '@/components/models/ApplicationDetailDialog.vue';
import DataTable from '@/components/ui/DataTable.vue';
import fileSVG from '@/views/student/applicationTableComponent/fileSVG.vue';
export default {
  components: {
    DataTable,
    fileSVG,
    ApplicationDetailDialog,
  },
  data() {
    return {
      headers: [
        {
          title: '企業名',
          value: 'company_name',
          align: 'left',
          width: '20%',
        },
        {
          title: '求人タイトル',
          align: 'center',
          value: 'title',
          width: '20%',
        },
        {
          title: '応募日 ',
          align: 'left',
          value: 'created_at',
          width: '20%',
        },
        {
          title: '応募内容',
          align: 'center',
          value: 'application_details',
          width: '20%',
        },
        {
          title: 'ステータス',
          align: 'center',
          value: 'status',
          width: '20%',
        },
      ],
      dialog: {
        reject: false,
        isShowDetailApp: false,
      },
      application: {},
      items: [],
    };
  },
  created() {
    this.getApplication();
  },
  methods: {
    showDetailApp(item) {
      this.application = item;
      this.dialog.isShowDetailApp = true;
    },
    async getApplication() {
      this.loading = true;

      let data = {};
      data.sort_by = 'created_at';
      let response = await this.$store.dispatch('APPLICATION_STUDENT_GET', {
        id: this.$route.params.id,
        status: 'all-others',
      });
      response.data.data.data.forEach((item) => {
        this.items.push(item);
      });
      this.total = response.data.total;
      this.loading = false;
    },
  },
};
</script>
<style src="../index.scss" lang="scss"></style>

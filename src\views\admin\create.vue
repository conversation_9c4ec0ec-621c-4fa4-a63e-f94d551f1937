<template>
  <div class="admin-create-page">
    <PageTitle
      :items="{
        title: '管理者',
        subTitle: '管理者登録',
        back: {
          action: () => {
            $router.push({ name: 'AdminListing' });
          },
        },
      }"
    ></PageTitle>

    <v-card class="pa-5">
      <Form @submit="handleFormSubmit" :validation-schema="schema">
        <div class="my-6">
          <v-row>
            <v-col cols="12" md="8" class="mx-auto">
              <v-col cols="12" md="12" class="mb-2">
                <label class="d-block font-14px">
                  <span>管理者名</span>
                  <span class="error--text ml-2 font-12px">必須</span>
                </label>
                <Field name="name" v-slot="{ field, errors }">
                  <v-text-field
                    v-bind="field"
                    :error-messages="errors"
                    :error="errors.length !== 0"
                    :hide-details="errors.length <= 0"
                    density="compact"
                    variant="outlined"
                    placeholder="管理者名"
                  ></v-text-field>
                </Field>
              </v-col>

              <v-col cols="12" md="12" class="mb-2">
                <label class="d-block font-14px">
                  <span>メールアドレス</span>
                  <span class="error--text ml-2 font-12px">必須</span>
                </label>
                <Field name="email" v-slot="{ field, errors }">
                  <v-text-field
                    v-bind="field"
                    :error-messages="errors"
                    :error="errors.length !== 0"
                    :hide-details="errors.length <= 0"
                    density="compact"
                    variant="outlined"
                    placeholder="メールアドレス"
                    autocomplete="new-email"
                  ></v-text-field>
                </Field>
              </v-col>

              <v-col cols="12" md="12" class="mb-2">
                <label class="d-block font-14px">
                  <span>パスワード 英数字8文字以上</span>
                  <span class="error--text ml-2 font-12px">必須</span>
                </label>
                <Field name="password" v-slot="{ field, errors }">
                  <v-text-field
                    v-bind="field"
                    :error-messages="errors"
                    :error="errors.length !== 0"
                    :hide-details="errors.length <= 0"
                    density="compact"
                    variant="outlined"
                    type="password"
                    placeholder="パスワード"
                    autocomplete="new-password"
                  ></v-text-field>
                </Field>
              </v-col>

              <v-col cols="12" md="12" class="mb-n3">
                <label class="d-block font-14px">
                  <span class="ml-2">パスワード(確認用)</span>
                  <span class="error--text ml-2 font-12px">必須</span>
                </label>
                <Field name="passwordConfirmation" v-slot="{ field, errors }">
                  <v-text-field
                    v-bind="field"
                    :error-messages="errors"
                    :error="errors.length !== 0"
                    :hide-details="errors.length <= 0"
                    density="compact"
                    variant="outlined"
                    type="password"
                    placeholder="パスワード(確認用)"
                    autocomplete="new-password"
                  ></v-text-field>
                </Field>
              </v-col>

              <v-col cols="12" md="12" class="-mt-1">
                <div class="admin-footer-action text-center mt-10">
                  <v-btn
                    type="submit"
                    :loading="loading"
                    max-width="188px"
                    width="100%"
                    height="35"
                    class="btn-primary white--text"
                  >
                    登録
                  </v-btn>
                </div>
              </v-col>
            </v-col>
          </v-row>
        </div>
      </Form>
    </v-card>

    <SuccessModel
      :text="`管理者を登録しました。`"
      :buttonText="`管理者一覧へ戻る`"
      :routeName="`AdminListing`"
      :dialog="dialog.success"
      @closeModel="dialog.success = false"
    ></SuccessModel>

    <SimpleModel
      :text="`この管理者情報を登録してよろしいですか？`"
      :dialog="dialog.submit"
      :submitButtonText="`登録する`"
      @submitSuccess="handleFormSubmit"
      @closeModel="dialog.submit = false"
    ></SimpleModel>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useStore } from 'vuex';
import { Form, Field } from 'vee-validate';
import SuccessModel from '@/components/models/SuccessModel.vue';
import SimpleModel from '@/components/models/SimpleModel.vue';
import * as yup from 'yup';
import { useI18n } from 'vue-i18n';

// Utility function to get the translated message from Vee-Validate config
const { t } = useI18n();
const getMessage = (key, params = {}) => {
  return t(key, params);
};

// Custom validation function for 'only_english_lang_allowed'
const onlyEnglishLangAllowed = (value) => {
  const englishRegex = /^[\u0000-\u007F]*$/; // Allows only ASCII characters (English letters, numbers, and symbols)
  // If value is empty, we don't want to fail the validation
  if (!value || value.trim() === '') {
    return true;
  }
  // Return true if validation passes, otherwise false
  return englishRegex.test(value);
};

// Create the Yup validation schema using the existing Vee-Validate messages
const schema = yup.object().shape({
  name: yup
    .string()
    .required(getMessage('field_required_message', { field: 'name' }))
    .test(
      'only-english-lang-allowed',
      getMessage('field_allowed_only_en_lange_message'), // Message when validation fails
      onlyEnglishLangAllowed
    ),
  email: yup
    .string()
    .email(getMessage('field_email_message'))
    .required(getMessage('field_required_message', { field: 'email' }))
    .test(
      'only-english-lang-allowed',
      getMessage('field_allowed_only_en_lange_message'), // Message when validation fails
      onlyEnglishLangAllowed
    ),
  password: yup
    .string()
    .min(8, getMessage('field_min_message', { field: 'password', length: 8 }))
    .required(getMessage('field_required_message', { field: 'password' }))
    .test(
      'verify_password',
      getMessage('field_verify_password_message'),
      (value) => {
        // Add custom password criteria here, for example:
        const hasNumber = /\d/.test(value);
        const hasUpperCase = /[A-Z]/.test(value);
        const hasLowerCase = /[a-z]/.test(value);
        return hasNumber && hasUpperCase && hasLowerCase;
      }
    )
    .test(
      'only-english-lang-allowed',
      getMessage('field_allowed_only_en_lange_message'), // Message when validation fails
      onlyEnglishLangAllowed
    ),
  passwordConfirmation: yup
    .string()
    .required(
      getMessage('field_required_message', { field: 'password confirmation' })
    )
    .oneOf(
      [yup.ref('password')],
      getMessage('field_password_confirmed_message')
    ),
});

// State and variables
const store = useStore();
const loading = ref(false);
const dialog = ref({
  success: false,
  submit: false,
});

// Submit form handler
const handleFormSubmit = async (values) => {
  loading.value = true;
  try {
    await store.dispatch('ADMINS_CREATE', values);
    dialog.value.success = true;
  } catch (error) {
    if (error.response && error.response.status === 422) {
      // Handle validation errors from the backend
      console.error(error);
    }
  } finally {
    loading.value = false;
  }
};
</script>

<style lang="scss" scoped src="./styles.scss"></style>

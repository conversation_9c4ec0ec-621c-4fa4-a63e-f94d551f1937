@use '@/styles/app';

.v-application {
  .auth-main-section {
    background: #f7f9f9 !important;
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    height: 100%;

    .login-main-blk {
      max-width: 340px;
      width: 100%;
      margin: auto;

      .input-text {
        margin-bottom: 15px;
      }

      .forget-blk {
        .forget-link {
          color: #7d7d7d;
          text-decoration: none !important;
        }
      }
    }

    .v-icon__component {
      width: auto !important;
      height: auto !important;
    }

    .v-field.v-field--variant-outlined {
      border-color: transparent !important;
      box-shadow: 0px 4px 4px rgb(232, 232, 232) !important;
      .v-field__outline {
        border: solid 1px $light-grey;
      }
    }
  }
}

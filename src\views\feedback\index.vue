<template>
  <div class="">
    <PageTitle
      v-show="!isLoading && !$store.getters.getApiProcessingStatus"
      :items="{
        title: 'フィードバック管理',
        subTitle: '一覧',
        tabs: [
          {
            title: 'すべて',
            count: getFeedbackCounts ? getFeedbackCounts.status_3_and_4 : 0,
            action: () => tabAction('4'),
          },
          {
            title: '申請中',
            count: getFeedbackCounts ? getFeedbackCounts.status_3 : 0,
            action: () => {
              tabAction('3');
            },
          },
        ],
        buttons: [
          {
            title: 'CSVエクスポート',
            class: 'bg-white text-ff862f',
            color: 'text-ff862f',
            variant: 'outlined',
            action: () => this.downloadCsv(),
          },
          {
            title: '詳細条件検索',
            variant: 'outlined',
            class: 'bg-white',
            action: () => {
              toggleSearch = !toggleSearch;
            },
          },
          {
            title: '新規作成',
            icon: 'mdi-plus-circle',
            action: () => {
              $router.push({
                name: 'AddressedFeedbackCreate',
              });
            },
          },
        ],
      }"
    ></PageTitle>
    <v-fade-transition>
      <SearchArea
        class="mb-4"
        v-if="toggleSearch"
        v-bind="{ toggleSearch, selectTypeOptions, searchFields }"
        @toggleSearch="updateSearchResults"
        @changedInputType="setChangedInputType"
        @searchSubmit="searchSubmit"
        @resetForm="getDataFromApi()"
      ></SearchArea>
    </v-fade-transition>
    <DataTable
      v-if="!isLoading && !$store.getters.getApiProcessingStatus"
      :headers="headers"
      :items="getAllFeedbacks"
      ref="pagination"
      :total-records="
        getFeedbackPagination ? getFeedbackPagination.records_total : 0
      "
      :number-of-pages="
        getFeedbackPagination ? getFeedbackPagination.total_pages : 0
      "
      @update:options="updateTable"
    >
      <template v-slot:item.created_at="{ item }">
        {{ dateFormat(item.created_at) }}
      </template>
      <template v-slot:item.student_full_name="{ item }">
        {{
          `${item.student ? item.student.family_name : ''} ${
            item.student ? item.student.first_name : ''
          }`
        }}
      </template>
      <!-- company -->
      <template v-slot:item.company_id="{ item }">
        <div class="font-12px fw-400">
          {{ item.companies?.internal_company_id }}
        </div>
        <div class="mouse-pointer">
          <router-link
            v-if="item?.companies?.internal_company_id"
            target="_blank"
            class="text-3f74c2 text-decoration-none truncate-lines lines-1"
            :to="{
              name: 'CorporateDetails',
              params: { id: item.company_id },
            }"
          >
            <v-tooltip
              :text="item?.companies?.name"
              location="top"
              color="white"
            >
              <template #activator="{ props }">
                <span v-bind="props">{{ item?.companies?.name }}</span>
              </template>
            </v-tooltip>
          </router-link>
        </div>
      </template>

      <!-- student -->
      <template v-slot:item.student_id="{ item }">
        <div class="font-12px fw-400">
          {{ item?.student?.student_internal_id }}
        </div>
        <div class="mouse-pointer">
          <router-link
            v-if="item?.student?.id"
            target="_blank"
            class="text-3f74c2 text-decoration-none truncate-lines lines-1"
            :to="{
              name: 'StudentProfile',
              params: { id: item.student.id },
              query: { tab: 'student-feedback' },
            }"
          >
            <v-tooltip
              :text="`${item.student.family_name} ${item.student.first_name}`"
              location="top"
              color="white"
            >
              <template #activator="{ props }">
                <span v-bind="props">
                  {{ item.student.family_name }} {{ item.student.first_name }}
                </span>
              </template>
            </v-tooltip>
          </router-link>
          <div v-else>存在しない</div>
        </div>
      </template>

      <!-- university -->
      <template v-slot:item.university="{ item }">
        <div v-if="item?.student?.id">
          <div class="font-12px fw-400">
            {{ item?.student.education_facility.name }}
          </div>
          <div class="truncate-lines font-12px lines-1">
            {{ item?.student?.email_valid }}
          </div>
        </div>
        <div v-else>存在しない</div>
      </template>

      <!-- start date -->
      <template v-slot:item.month_feedback_target="{ item }">
        <div class="font-12px fw-400">
          {{
            item.month_feedback_target
              ? yearMonthFormat(item.month_feedback_target)
              : ''
          }}
        </div>
      </template>

      <!-- end date -->
      <template v-slot:item.datetime_public="{ item }">
        <div class="font-12px fw-400">
          {{ item.datetime_public ? dateFormat(item.datetime_public) : '' }}
        </div>
      </template>

      <!-- action -->
      <template v-slot:item.action="{ item }">
        <div
          class="font-12px fw-400 cursor-pointer"
          @click.stop="
            $router.push({
              name: 'AddressedFeedbackEdit',
              params: { id: item.id },
            })
          "
        >
          <v-icon color="red">$edit</v-icon>
        </div>
      </template>

      <!-- feedback target -->
      <template v-slot:item.feedback_status="{ item }">
        <v-sheet color="transparent" class="d-flex align-center">
          <v-menu :close-on-click="true" offset-y activator="parent">
            <!-- Activator slot for the menu -->
            <template v-slot:activator="{ props }">
              <v-chip
                :color="getStatusColor(item.feedback_status)"
                variant="flat"
                dense
                size="small"
                v-bind="props"
              >
                <div class="d-flex align-center justify-space-between">
                  <div class="text-truncate white--text font-12px">
                    {{ getStatus(item.feedback_status) }}
                  </div>
                </div>
              </v-chip>
            </template>
          </v-menu>
        </v-sheet>
      </template>
    </DataTable>
  </div>
  <SimpleModel
    :text="errorMessages"
    :dialog="dialog.errorDialog"
    :showCloseIcon="true"
    @closeModel="dialog.errorDialog = false"
    :buttonOption="{
      hideCancel: true,
      hideSubmit: true,
    }"
  ></SimpleModel>
</template>
<script>
import { ref, reactive, computed, onMounted, defineAsyncComponent } from 'vue';
import { useStore } from 'vuex';

const DataTable = defineAsyncComponent(
  () => import('@/components/ui/DataTable.vue')
);
const SearchArea = defineAsyncComponent(
  () => import('@/components/ui/SearchArea.vue')
);
const SimpleModel = defineAsyncComponent(
  () => import('@/components/models/SimpleModel.vue')
);

import moment from 'moment';
import Encoding from 'encoding-japanese';

export default {
  components: { DataTable, SearchArea, SimpleModel },

  setup() {
    const store = useStore();
    const dateFormat = (date) => {
      return date ? moment(date).format('YYYY/MM/DD') : '---- / - / -';
    };
    const selectTypeOptions = ref([
      {
        id: 'keyword_search',
        name: 'キーワード検索',
      },
      {
        id: 'month_feedback_target',
        name: 'フィードバック対象月',
      },
    ]);
    // Reactive state using `ref` and `reactive`
    const feedbackTypes = ref([
      { id: 1, name: '学業によって時間確保が難しい' },
      { id: 2, name: '就活によって時間確保が難しい' },
      { id: 3, name: '事業縮小に伴う業務内容の減少' },
      { id: 4, name: '定型業務のためフィードバック自体が難しい' },
      { id: 5, name: 'プロジェクトの節目まで待ってほしい' },
      { id: 6, name: '1on1で個別に振り返り済み' },
    ]);

    const selectedTab = ref('');
    const options = ref([1, 2, 3, 4]);
    const toggleSearch = ref(false);
    const declineItem = ref([]);
    const searchFields = ref([]);
    const dialog = reactive({
      reject: false,
      detail: false,
      errorDialog: false,
    });
    const errorMessages = ref(null);
    const selectedItem = ref(null);
    const isLoading = ref(true);
    const headers = ref([
      {
        title: '内部ID',
        subTitle: '企業名',
        align: 'left',
        class: ['py-3', 'px-0'],
        value: 'company_id',
        sortable: false,
        flex: '1 1 15%',
      },
      {
        title: '学生ID',
        subTitle: '学生名',
        class: ['py-3', 'px-0'],
        value: 'student_id',
        align: 'left',
        sortable: false,
        flex: '1 1 15%',
      },
      {
        title: '大学名',
        subTitle: '学生メールアドレス',
        value: 'university',
        class: ['py-3', 'px-0'],
        align: 'left',
        sortable: false,
        flex: '1 1 15%',
      },
      {
        title: '対象月',
        sortable: false,
        align: 'left',
        class: ['py-3', 'px-0'],
        value: 'month_feedback_target',
        flex: '1 1 15%',
      },
      {
        title: '公開日',
        sortable: false,
        align: 'left',
        class: ['py-3', 'px-0'],
        value: 'datetime_public',
        flex: '1 1 15%',
      },
      {
        title: '申請状況',
        value: 'feedback_status',
        class: ['py-3', 'px-0'],
        align: 'left',
        sortable: false,
        flex: '1 1 15%',
      },
      { title: '', value: 'action', flex: '1 1 15%' },
    ]);

    /**
     * State for tracking initial data load and search inputs
     */
    const initialLoad = ref(true);
    const userSearchedInput = ref({});

    /**
     * Computed properties for accessing Vuex store data
     * Provides reactive access to feedback-related data
     */
    const getAllFeedbacks = computed(() => store.getters.getAllFeedbacks);
    const getFeedbackPagination = computed(
      () => store.getters.getFeedbackPagination
    );
    const getFeedbackCounts = computed(() => store.getters.getFeedbackCounts);
    const getFeedbackCsvData = computed(() => store.getters.getFeedbackCsvData);

    /**
     * Returns color code for feedback status chip
     * @param {number} feedback_status - Status code (1: pending, 2: draft, 3: in review, 4: published)
     * @returns {string} Hex color code
     */
    const getStatusColor = (feedback_status) => {
      switch (feedback_status) {
        case 1:
          return '#60D1CB'; // Pending - Turquoise
        case 2:
          return '#A7A7A7'; // Draft - Gray
        case 3:
          return '#EE6C9B'; // In Review - Pink
        case 4:
          return '#A7A7A7'; // Published - Gray
      }
    };

    /**
     * Converts feedback status code to Japanese text
     * @param {number|string} value - Status code
     * @returns {string} Status in Japanese
     */
    const getStatus = (value) => {
      switch (parseInt(value)) {
        case 1:
          return '未申請'; // Not submitted
        case 2:
          return '下書き'; // Draft
        case 3:
          return '申請中'; // Under review
        case 4:
          return '公開中'; // Published
      }
    };

    /**
     * Gets feedback reason text from type ID
     * @param {number} type - Feedback reason type ID
     * @returns {string} Reason text in Japanese
     */
    const getFeedbackReasonTypeString = (type) => {
      const feedback = feedbackTypes.value.find((f) => f.id === type);
      return feedback ? feedback.name : '';
    };

    /**
     * Handles tab change and refreshes data
     * @param {string} tab - Selected tab value
     */
    const tabAction = (tab) => {
      selectedTab.value = tab;
      getDataFromApi();
    };

    /**
     * Pagination state and reset functionality
     */
    const pagination = ref(true);
    const resetPagination = () => {
      if (pagination) {
        pagination.currentPage = 1;
        pagination.updatePaginate++;
      }
    };

    /**
     * Updates table data when options change (sort, page, etc.)
     * Skips update on initial load
     * @param {Object} e - Table options event
     */
    const updateTable = (e) => {
      if (!initialLoad.value) getDataFromApi(e);
    };

    /**
     * Updates feedback status and related data
     * @param {Object} item - Feedback item to update
     * @param {number} option - New status value
     */
    const updateApi = async (item, option) => {
      const params = {
        id: item.id,
        feedback_status: option,
        super_power_review: item.super_power_review,
        super_power_comment: item.super_power_comment,
        student_id: item.student_id,
        is_draft_or_public: item.is_draft_or_public,
        growth_idea_review: item.growth_idea_review,
        growth_idea_comment: item.growth_idea_comment,
        date_contract_employment_start: item.date_contract_employment_start,
        date_contract_employment_end: item.date_contract_employment_end,
        contract_id: item.contract_id,
        company_id: item.company_id,
        type_feedback_non_target: item?.type_feedback_non_target,
      };
      await store.dispatch('FEEDBACK_UPDATE', params);
      getDataFromApi();
    };

    /**
     * Fetches feedback data with filtering and pagination
     * @param {Object} e - Optional table options for sorting and pagination
     */
    const getDataFromApi = async (e = undefined) => {
      // Set status filter based on selected tab
      let status = selectedTab.value == '3' ? '3' : '3,4';

      // Prepare API request parameters
      const data = {
        sort_by_order:
          e && e.sortDesc && e.sortDesc.length > 0
            ? e.sortDesc[0]
              ? 'asc'
              : 'desc'
            : 'desc',
        sort_by:
          e && e.sortBy && e.sortBy.length > 0 ? e.sortBy[0] : 'created_at',
        page: typeof e === 'number' ? e : e && e.page ? e.page : 1,
        paginate: e && e.itemsPerPage ? e.itemsPerPage : 25,
        feedback_status: status,
        ...userSearchedInput.value,
      };

      // Prevent unnecessary API calls if the parameters haven't changed
      const currentParams = JSON.stringify(data);
      if (getDataFromApi.lastParams === currentParams) {
        return; // Skip API call if parameters are identical
      }

      // Store current parameters for future comparison
      getDataFromApi.lastParams = currentParams;

      try {
        await store.dispatch('FEEDBACK_GET_ALL', data);
      } catch (error) {
        console.error('Error fetching feedback data:', error);
      } finally {
        initialLoad.value = false;
        isLoading.value = false;
      }
    };

    /**
     * Handles search form submission and updates results
     */
    const updateSearchResults = async () => {
      toggleSearch.value = false;
      await getDataFromApi();
      resetPagination();
    };

    /**
     * Updates search fields based on selected search type
     * @param {string} inputSearchType - Type of search (keyword or date range)
     */
    const setChangedInputType = (inputSearchType) => {
      if (inputSearchType === 'keyword_search') {
        // Set up keyword search field
        searchFields.value = [
          {
            label: 'Search text',
            name: 'search',
            type: 'text',
            value: null,
            placeholder: '企業内部ID、企業名、学生ID、学生名、大学名',
          },
        ];
      } else if (inputSearchType === 'month_feedback_target') {
        // Set up date range search fields
        searchFields.value = [
          {
            label: 'Label',
            name: 'date_from',
            type: 'date',
            pickerType: 'months',
            rules: 'required',
            show_after_approx: true,
            value: moment().format('YYYY-MM'),
            menu: false,
            locale: 'ja',
            date_format: 'YYYY-MM',
          },
          {
            label: 'Label',
            name: 'date_to',
            type: 'date',
            pickerType: 'months',
            rules: 'required',
            show_after_approx: false,
            value: moment().format('YYYY-MM'),
            menu: false,
            locale: 'ja',
            range: true,
            range_input: 'date_from',
            date_format: 'YYYY-MM',
          },
        ];
      } else {
        searchFields.value = {};
      }
    };

    /**
     * Processes search form submission and updates search criteria
     * @param {Object} $event - Form submission event with field values
     */
    const searchSubmit = async ($event) => {
      let obj = {};
      if ($event.fields.length > 0) {
        $event.fields.forEach((field) => {
          obj[field.name] = field.value;
        });
      }
      userSearchedInput.value = { ...obj };
      getDataFromApi();
    };

    /**
     * Handles CSV export functionality
     * Downloads feedback data as CSV file or shows error message
     */
    const downloadCsv = async () => {
      await store.dispatch('FEEDBACK_EXPORT_CSV');

      // Show error if no data available
      if (store.getters.getFeedbackCsvData?.message) {
        dialog.value.errorDialog = true;
        errorMessages.value =
          '<div class="pt-10">データが見つかりません。</div>';
        return;
      }

      // Convert UTF-8 CSV data to Shift-JIS
      const utf8Array = Encoding.stringToCode(
        store.getters.getFeedbackCsvData.data.csv
      );
      const sjisArray = Encoding.convert(utf8Array, {
        to: 'SJIS',
        from: 'UNICODE',
      });
      const uint8Array = new Uint8Array(sjisArray);
      const blob = new Blob([uint8Array], { type: 'text/csv' });

      // Create and trigger CSV download
      const fileUrl = window.URL.createObjectURL(blob);
      const fileLink = document.createElement('a');
      fileLink.href = fileUrl;
      fileLink.setAttribute(
        'download',
        `フィードバック管理_${new Date().toISOString().slice(0, 10)}.csv`
      );
      document.body.appendChild(fileLink);
      fileLink.click();
      document.body.removeChild(fileLink);
    };

    /**
     * Component initialization
     * Fetches initial feedback data
     */
    onMounted(async () => {
      await getDataFromApi();
    });

    return {
      feedbackTypes,
      selectedTab,
      options,
      toggleSearch,
      declineItem,
      searchFields,
      dialog,
      selectedItem,
      headers,
      initialLoad,
      userSearchedInput,
      getAllFeedbacks,
      getFeedbackPagination,
      getFeedbackCounts,
      getFeedbackCsvData,
      getStatusColor,
      getStatus,
      getFeedbackReasonTypeString,
      tabAction,
      resetPagination,
      updateTable,
      updateApi,
      getDataFromApi,
      setChangedInputType,
      searchSubmit,
      downloadCsv,
      dateFormat,
      selectTypeOptions,
      updateSearchResults,
      errorMessages,
      isLoading,
    };
  },
};
</script>
<style lang="scss" scoped></style>

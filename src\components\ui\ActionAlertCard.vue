<template>
  <div
    class="d-flex width-full justify-center align-center mt-14"
    style="z-index: 9999"
  >
    <div
      class="d-flex justify-center mt-2 alert-adjust align-center alert mt-14"
      :style="alertStyle"
    >
      <v-alert
        class="message-text"
        :value="showAlert"
        transition="fade-transition"
        color="#13aba3"
      >
        {{ message }}
      </v-alert>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ActionAlertCard',
  props: {
    message: {
      type: String,
      required: true,
    },
    duration: {
      type: Number,
      default: 5,
      required: false,
    },
    // eslint-disable-next-line vue/no-reserved-props
    alertStyle: {
      type: String,
      required: false,
      default: '',
    },
    type: {
      type: String,
      required: false,
      default: 'success',
    },
  },
  data() {
    return {
      showAlert: true,
    };
  },
  mounted() {
    // setInterval(() => {
    //   this.showAlert = false
    //   this.$emit('alertShutdown')
    // }, this.duration * 1000)
  },
};
</script>

<style>
.alert-adjust {
  z-index: 1000;
  position: fixed;
  top: 20px;
}
@media (max-width: 768px) {
  .message-text {
    font-size: 12px;
    padding-top: 8px;
    padding-bottom: 8px;
  }
  .alert .v-icon.v-icon {
    font-size: 22px !important;
  }
  .v-alert--density-default {
    padding: 8px !important;
  }
}
</style>

<template>
  <div>
    <v-card class="text-center mt-5" height="794px">
      <div class="pt-13">
        <Field
          name="seo_featured_image"
          rules="size:5120"
          v-slot="{ field, errors }"
        >
          <div
            :class="{ 'px-2': smAndDown }"
            class="file-input-box-container mx-auto"
          >
            <div
              v-if="!data?.previewImageURL"
              class="file-input-box-container mx-auto"
            >
              <v-file-input
                accept="image/*"
                v-model="data.logoImage"
                hide-input
                class="file-input-box d-flex align-center justify-center"
                @change="previewImage"
                prepend-icon="mdi-plus-circle"
                truncate-length="1"
                dense
              >
              </v-file-input>
            </div>
            <div
              @click="previewImage"
              class="image-preview"
              v-if="data.previewImageURL"
            >
              <img
                class="object-fit-cover"
                :src="data.previewImageURL"
                id="image"
                alt=""
              />
            </div>
          </div>
          <h6
            @click="removeImage"
            v-if="data.previewImageURL"
            class="cursor-pointer font-14px fw-500 text-default mb-0 text-green"
          >
            削除する
          </h6>
          <span v-if="errors.length > 0" class="font-12px text-red">{{
            errors[0]
          }}</span>
        </Field>

        <div
          :class="{ 'px-2': smAndDown }"
          class="file-upload-information-container mx-auto"
        >
          <div
            class="file-upload-information d-flex align-center justify-center font-14px mt-4 pt-4 pb-4"
          >
            画素：1,000px×2,000px<br />
            サイズ：5MB以下<br />
            形式：jpg、png
          </div>
        </div>

        <!-- ALT Text -->
        <div class="text-start mt-4 px-8">
          <span class="font-14px">altテキスト</span>
          <Field
            name="seo_ogp"
            :rules="data.seo_ogp.rules"
            v-slot="{ field, errors }"
          >
            <v-textarea
              v-model="data.seo_ogp.value"
              :error-messages="errors"
              :error="errors.length > 0"
              :hide-details="errors.length <= 0"
              single-line
              class="mt-1"
              density="compact"
              variant="outlined"
              rows="2"
              placeholder="altテキスト"
            />
          </Field>
        </div>

        <!-- Slug -->
        <div class="text-start mt-2 px-8">
          <span class="font-14px">
            スラグ <span class="font-12px error--text ml-2">必須</span>
          </span>
          <Field
            name="seo_slug"
            :value="data.seo_slug.value"
            :rules="data.seo_slug.rules"
            v-slot="{ field, errors }"
          >
            <v-text-field
              v-bind="field"
              v-model="data.seo_slug.value"
              :error-messages="errors"
              :error="errors.length > 0"
              :hide-details="errors.length <= 0"
              single-line
              class="mt-1"
              density="compact"
              variant="outlined"
              placeholder="スラグ"
            />
          </Field>
        </div>

        <!-- Meta Description -->
        <div class="text-start mt-2 px-8">
          <div class="d-flex justify-space-between">
            <span class="font-14px">メタディスクリプション</span>
            <span class="font-12px text-grey pt-1">推奨120文字以内</span>
          </div>
          <Field
            name="seo_meta_description"
            :rules="data.seo_meta_description.rules"
            v-slot="{ field, errors }"
          >
            <v-textarea
              v-model="data.seo_meta_description.value"
              :error-messages="errors"
              :error="errors.length > 0"
              :hide-details="errors.length <= 0"
              rows="5"
              :row-height="20"
              auto-grow
              class="mt-1"
              density="compact"
              variant="outlined"
              placeholder="メタディスクリプション"
            />
            <div class="d-flex justify-end font-14px text-grey text-14px pb-12">
              {{
                data.seo_meta_description.value
                  ? data.seo_meta_description.value.length
                  : 0
              }}
            </div>
          </Field>
        </div>
      </div>
    </v-card>
  </div>
</template>

<script>
import { Field } from 'vee-validate';
import { useDisplay } from 'vuetify';

export default {
  name: 'ImageUpload',
  props: {
    data: {
      type: Object,
      required: true,
    },
  },
  components: {
    Field,
  },
  setup(props) {
    const previewImage = (event) => {
      const file = event.target.files[0];
      if (file) {
        props.data.previewImageURL = URL.createObjectURL(file);
        props.data.removeimage = 0;
      }
    };

    const removeImage = () => {
      props.data.previewImageURL = '';
      props.data.logoImage = null;
      props.data.removeimage = 1;
    };

    const { mdAndUp, smAndDown } = useDisplay();

    return {
      previewImage,
      removeImage,
      mdAndUp,
      smAndDown,
    };
  },
};
</script>

<style lang="scss" scoped>
.file-input-box-container {
  max-width: 290px;
}

.file-input-box {
  height: 134px;
  width: 100%;
  border: 1px dashed #13aba3;
  box-sizing: border-box;
  position: relative;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.file-input-box:hover {
  border-color: #13aba3;
}

.v-icon {
  font-size: 30px;
  color: #13aba3;
}

.image-preview {
  max-width: 290px !important;
  width: 100%;
  height: 134px;
  z-index: 1;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
.file-upload-information-container {
  max-width: 290px !important;
  .file-upload-information {
    width: 100%;
    border-radius: 5px;
    background: #f9f9f9;
    color: #7d7e7e;
    text-align: center;
  }
}
.file-upload-information-container-error {
  max-width: 290px !important;
  .file-upload-information-error {
    width: 100%;
    border-radius: 5px;
    background: transparent;
    color: red !important;
    text-align: center;
  }
}
</style>

// internship.mixin.js
import { computed, ref, nextTick } from 'vue';
import { useStore } from 'vuex';
import { debounce } from 'debounce';
import { useForm } from 'vee-validate';
import { useRouter, useRoute } from 'vue-router';

export default function useInternshipPostMixin() {
  const store = useStore();
  const router = useRouter();
  const route = useRoute();

  // Reactive state
  const alertText = ref('');
  const buttonText = ref('一覧へ戻る');
  const successDialog = ref(false);
  const routeName = ref('');
  const userSiteUrl = import.meta.env.VUE_APP_USER_SITE_URL;
  const isPreview = ref(false);
  const previewedData = ref(null);
  const imageDetails = ref({
    previewImageURL: null,
    logoImage: null,
    seo_ogp: { value: '', rules: '' },
    seo_slug: { value: '', rules: 'required|slug' },
    seo_meta_description: { value: '', rules: '' },
  });
  const basicInformation = ref([]);
  const isDraftOrPublic = ref('0'); // public post
  const dialog = ref({
    saveAsDraft: false,
    deletePost: false,
    preview: false,
    alert: false, // new alert dialog flag
  });

  const companyEditFields = ref({
    contactable_user5_id: null,
    contactable_user1_id: null,
    contactable_user2_id: null,
    contactable_user3_id: null,
    contactable_user4_id: null,
    rules: 'required',
  });

  // Vuex Getters
  const getAllCompany = computed(() => store.getters.getAllCompany);
  const getApiProcessingStatus = computed(
    () => store.getters.getApiProcessingStatus
  );
  const getSingleInternship = computed(() => store.getters.getSingleInternship);
  const { validate, setErrors, errors, setFieldValue } = useForm();

  const openDraftPopup = async () => {
    companyEditFields.value.rules = '';
    basicInformation.value = basicInformation.value.map((item) => {
      if (
        item.requiredChecks &&
        item.name !== 'company_id' &&
        item.name !== 'title'
      ) {
        item.rules = '';
        item.rules = item.rules
          .replace('required|', '')
          .replace('required', '');
      }
      return item;
    });
    await nextTick();
    // Check for draft validations on title and seo_slug
    const companyField = basicInformation.value.find(
      (item) => item.name === 'company_id'
    );
    const titleField = basicInformation.value.find(
      (item) => item.name === 'title'
    );
    const seoSlug = imageDetails.value.seo_slug.value;
    if (
      !companyField ||
      !companyField.value ||
      !titleField ||
      !titleField.value ||
      !titleField.value.trim() ||
      !seoSlug ||
      !seoSlug.trim()
    ) {
      alertText.value =
        '下書きを保存する際は、企業情報、求人タイトルとスラグを必ず入力してください。';
      dialog.value.alert = true;
      return;
    }

    // Validate inputs before showing draft dialog
    const isValid = await validate(); // Run the form validation
    if (!isValid.valid) {
      return;
    }

    dialog.value.saveAsDraft = true;
  };

  const setRequired = async () => {
    companyEditFields.value.rules = 'required';
    basicInformation.value = basicInformation.value.map((item) => {
      isDraftOrPublic.value = '1'; // Set as public post
      if (item.requiredChecks) {
        item.rules = '';
        item.rules = 'required|' + item.rules;
      }
      return item;
    });
    await nextTick();
    // Validate inputs before showing draft dialog
    const isValid = await validate(); // Run the form validation
    if (!isValid.valid) {
      return;
    }
  };

  const preview = () => {
    isPreview.value = true;
    saveAsDraft();
  };

  const saveAsDraft = () => {
    submitInformation('Y'); // Call the function to submit as draft
  };

  const getInputsPageData = () => {
    const data = new FormData();
    data.append(
      'seo_meta_description',
      imageDetails.value.seo_meta_description.value ?? ''
    );
    data.append('seo_ogp', imageDetails.value.seo_ogp.value ?? '');
    data.append('seo_slug', imageDetails.value.seo_slug.value ?? null);
    data.append('draft_or_public', isDraftOrPublic.value ?? '');
    if (imageDetails.value.logoImage) {
      data.append('seo_featured_image', imageDetails.value.logoImage);
    }
    data.append('remove_image', imageDetails.value.removeimage);

    data.append(
      'contactable_user1_id',
      companyEditFields.value.contactable_user1_id ?? ''
    );
    data.append(
      'contactable_user2_id',
      companyEditFields.value.contactable_user2_id ?? ''
    );
    data.append(
      'contactable_user3_id',
      companyEditFields.value.contactable_user3_id ?? ''
    );
    data.append(
      'contactable_user4_id',
      companyEditFields.value.contactable_user4_id ?? ''
    );
    data.append(
      'contactable_user5_id',
      companyEditFields.value.contactable_user5_id ?? ''
    );

    basicInformation.value.forEach((field) => {
      if (field.name && field.value && field.name !== 'wage') {
        if ('additional_field' in field) {
          data.append(
            field.additional_field.name,
            field.additional_field.value
          );
        }

        if (field.name == 'internship_feature_id') {
          if (field.value && field.value.length > 0) {
            field.value.forEach((item) => {
              data.append('internship_feature_id[]', item);
            });
          }
        } else {
          data.append(field.name, field.value);
        }
      }
      if (field.name == 'wage') {
        data.append('wage', parseFloat(field.value.replace(/,/g, '')) || 0);
      }
    });
    data.append('flg_delete', 0);

    return data;
  };

  const setFormDataFromServer = (serverData) => {
    // Loop through the server data and set each field value
    Object.keys(serverData.value).forEach((key) => {
      setFieldValue(key, serverData.value[key]); // Set each field value
    });
  };

  const savePageData = async (isCreateOrUpdate) => {
    await nextTick();
    const isValid = await validate(); // Run the form validation
    if (!isValid.valid) {
      return;
    }

    let data = getInputsPageData();
    let SET_CALL = 'INTERNSHIP_CREATE';

    if (isCreateOrUpdate == 'update') {
      SET_CALL = 'INTERNSHIP_UPDATE';
      data.append('id', route.params.id);
      if (isPreview.value) {
        data.set('draft_or_public', postStatus);
      }
    }

    if (previewedData.value?.id) {
      SET_CALL = 'INTERNSHIP_UPDATE';
      data.append('id', previewedData.value.id);
    }

    store
      .dispatch(SET_CALL, data)
      .then((result) => {
        if (isPreview.value) {
          isPreview.value = false;
          dialog.value.preview = true;
          previewedData.value = result.data.data.data;
        } else {
          if (result.data.data.data.draft_or_public == '1') {
            alertText.value = '求人広告を更新しました。';
            buttonText.value = '求人広告一覧に戻る'
            routeName.value = 'InternshipPostList';
            successDialog.value = true;
          } else {
            alertText.value = '下書きを保存しました。';
            routeName.value = 'InternshipPostDraftList';
            successDialog.value = true;
          }
        }
      })
      .catch((error) => {
        if (error?.status == 422) {
          const backendErrors = error?.data?.errors || {};
          setErrors(backendErrors);
          // Build error message mapping field names to their labels.
          // First, build a mapping from field names to labels from basicInformation.
          const fieldLabelMap = {};
          basicInformation.value.forEach((field) => {
            fieldLabelMap[field.name] = field.label;
          });
          // Add mapping for seo_slug from imageDetails.
          fieldLabelMap['seo_slug'] = 'スラグ';
          // Build error messages
          let errorMessages = '';
          for (const [fieldName, msgs] of Object.entries(backendErrors)) {
            const label = fieldLabelMap[fieldName] || fieldName;
            errorMessages += `${label}: ${msgs.join(', ')}<br/>`;
          }
          alertText.value = errorMessages;
        } else {
          alertText.value = 'APIエラーが発生しました。';
        }
        dialog.value.alert = true;
      });
  };

  const getAutoSuggestionText = (item) => {
    // Check if all properties are null or undefined
    if (
      !item?.internal_company_id &&
      !item?.name &&
      !item?.business_industry?.name
    ) {
      return ''; // Return an empty string if all are null/undefined
    }

    // Otherwise, format and return the string as usual
    return `${item?.internal_company_id ?? ''} / ${item.name ?? ''} ${item?.business_industry?.name ?? ''}`;
  };

  const searchCompany = debounce(function (field) {
    field.is_loading = true;

    store
      .dispatch('COMPANY_GET_ALL', {
        search: field.searched_text ?? null,
        silent_loading: true,
        page: 1,
        paginate: 10,
        showActive: 1,
      })
      .then(() => {
        // Ensure `items` is initialized before assigning a value
        if (Array.isArray(field.items)) {
          field.items = getAllCompany.value;
        } else {
          // If `items` is not defined or is not an array, initialize it as an array
          field.items = [...getAllCompany.value];
        }

        field.is_loading = false;
      })
      .catch((error) => {
        console.error('Error fetching company data:', error);
      });
  }, 500);

  const getMasterData = computed(() => store.getters.getMasterData);
  const getPageFields = () => {
    basicInformation.value = [
      {
        label: '企業名',
        name: 'company_id',
        type: 'autocomplete',
        placeholder: '内部ID、または企業名フリガナを入力してください',
        row_class: '',
        item_value: 'id',
        item_text: getAutoSuggestionText,
        searchable: true,
        no_data_text: '選択してください',
        search_api: searchCompany,
        is_loading: false,
        searched_text: '',
        items: getAllCompany.value, // Initialize `items` as an empty array
        value: null,
        rules: 'required',
        requiredChecks: true,
      },
      {
        label: '求人タイトル',
        name: 'title',
        type: 'text',
        placeholder: 'タイトルを入力してください',
        row_class: 'job-title',
        value: '',
        rules: 'required|max:60',
        requiredChecks: true,
        counter: true,
        counterValue: 60,
      },
      {
        label: '職種',
        name: 'work_category_id',
        type: 'dropdown',
        placeholder: '選択してください',
        row_class: '',
        field_class: '',
        item_value: 'id',
        item_text: 'name',
        items: getMasterData.value?.work_categories,
        no_data_text: '選択してください',
        value: null,
        rules: 'required',
        requiredChecks: true,
      },
      {
        label: 'インターン期間',
        name: 'period',
        type: 'dropdown',
        placeholder: '選択してください',
        row_class: '',
        field_class: '',
        item_value: 'id',
        item_text: 'name',
        items: getMasterData.value?.period,
        no_data_text: '選択してください',
        value: null,
        rules: 'required',
        requiredChecks: true,
      },
      {
        label: '週稼働時間',
        name: 'workload',
        type: 'dropdown',
        placeholder: '選択してください',
        row_class: '',
        field_class: '',
        item_value: 'id',
        item_text: 'name',
        items: getMasterData.value?.workload,
        no_data_text: '選択してください',
        value: null,
        rules: 'required',
        requiredChecks: true,
      },
      {
        label: '対象学年',
        name: 'target_grade',
        type: 'dropdown',
        placeholder: '選択してください',
        row_class: '',
        field_class: '',
        item_value: 'id',
        item_text: 'name',
        items: getMasterData.value?.target_grade,
        no_data_text: '選択してください',
        value: null,
        rules: 'required',
        requiredChecks: true,
      },
      {
        label: '時給',
        name: 'wage',
        type: 'number-currency',
        placeholder: '入力してください',
        counter: true,
        counterValue: 25,
        value: '',
        rules: 'required|max:25',
        requiredChecks: true,
      },
      {
        label: '特徴',
        name: 'internship_feature_id',
        type: 'dropdown',
        multiple: true,
        placeholder: '選択してください',
        value: [],
        rules: '',
        item_value: 'id',
        item_text: 'name',
        items: getMasterData.value?.internship_feature_list,
      },
      {
        label: '採用フロー',
        name: 'application_step_1',
        type: 'text',
        placeholder: '1.',
        counter: true,
        counterValue: 25,
        value: '',
        rules: 'required|max:25',
        requiredChecks: true,
      },
      {
        label: '',
        name: 'application_step_2',
        type: 'text',
        placeholder: '2.',
        counter: true,
        counterValue: 25,
        value: '',
        rules: 'max:25',
      },
      {
        label: '',
        name: 'application_step_3',
        type: 'text',
        placeholder: '3.',
        counter: true,
        counterValue: 25,
        value: '',
        rules: 'max:25',
      },
      {
        label: '',
        name: 'application_step_4',
        type: 'text',
        placeholder: '4.',
        counter: true,
        counterValue: 25,
        value: '',
        rules: 'max:25',
      },
      {
        label: '表示順',
        name: 'display_order_select',
        type: 'dropdown',
        items: [
          { id: 0, name: '設定しない' },
          { id: 1, name: '設定する' },
        ],
        item_value: 'id',
        item_text: 'name',
        value: 0,
        rules: '',
        additional_field: {
          label: '表示順',
          name: 'display_order',
          type: 'text',
          placeholder:
            '半角数字を入力してください。値の小さな求人から順に掲載されます',
          value: '',
          rules: 'required|min_value:1',
          requiredChecks: true,
          visibility_check: true,
          visible_value: '0',
          col_class_right: 'pl-3',
        },
      },
      {
        label: '会社概要',
        name: 'description_corporate_profile',
        type: 'richbox',
        placeholder: '',
        value: '',
        rules: '',
      },
      {
        label: '募集内容',
        name: 'description_internship_content',
        type: 'richbox',
        placeholder: '',
        value: '',
        rules: '',
      },
      {
        label: '自己PR',
        name: 'flg_required_self_introduction',
        type: 'checkbox',
        placeholder: '',
        value: true,
        rules: '',
        isCheckBoxDisabled: true,
      },
      {
        label: '履歴書URL',
        name: 'flg_required_resume',
        type: 'checkbox',
        placeholder: '',
        value: '',
        rules: '',
      },
      {
        label: '資格・実績URL',
        name: 'flg_required_qualification',
        type: 'checkbox',
        placeholder: '',
        value: '',
        rules: '',
      },
      {
        label: '独自課題（テキスト）',
        name: 'flg_required_item',
        type: 'checkbox',
        placeholder: '',
        value: 0,
        rules: '',
        additional_field: {
          label: '独自課題（テキスト）',
          name: 'required_item_prompt',
          type: 'text',
          placeholder: '独自課題（テキスト）のタイトルを入力してください',
          value: '',
          rules: 'required',
          visibility_check: true,
          requiredChecks: true,
          visible_value: 0,
          col_class_right: 'col-md-12',
        },
      },
      {
        label: '独自課題（URL）',
        name: 'flg_required_url',
        type: 'checkbox',
        placeholder: '',
        value: 0,
        rules: '',
        additional_field: {
          label: '独自課題（URL）',
          name: 'required_url_prompt',
          type: 'text',
          placeholder: '独自課題（URL）のタイトルを入力してください',
          value: '',
          rules: 'required',
          visibility_check: true,
          requiredChecks: true,
          visible_value: 0,
          col_class_right: 'col-md-12',
        },
      },
    ];
  };

  return {
    alertText,
    buttonText,
    successDialog,
    routeName,
    userSiteUrl,
    isPreview,
    previewedData,
    imageDetails,
    basicInformation,
    isDraftOrPublic,
    dialog,
    openDraftPopup,
    preview,
    saveAsDraft,
    getInputsPageData,
    savePageData,
    getAutoSuggestionText,
    searchCompany,
    getPageFields,
    getMasterData,
    getSingleInternship,
    getApiProcessingStatus,
    setFormDataFromServer,
    companyEditFields,
    setRequired,
  };
}

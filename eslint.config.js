import { defineConfig } from 'eslint-define-config';
import vue from 'eslint-plugin-vue';
import typescript from '@typescript-eslint/eslint-plugin';

export default defineConfig([
  // Vue recommended config
  vue.configs['vue3-recommended'],

  // TypeScript recommended config, if using TypeScript
  typescript.configs['recommended'],

  {
    // General parser options
    parserOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
    },

    // Custom rules
    rules: {
      'no-console': 'warn',
      'vue/multi-word-component-names': 'off', // Example rule adjustment
    },
  },
]);

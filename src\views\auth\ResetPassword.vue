<template>
  <v-app>
    <section class="auth-main-section font-Noto-Sans text-center">
      <v-container fill-height>
        <v-row>
          <v-col cols="12">
            <div class="login-main-blk">
              <div class="login-top-blk mb-10 pb-7">
                <div class="logo-circle-top">
                  <v-img class="mx-auto" :src="logoUrl" lazy></v-img>
                </div>
              </div>
              <div ref="observer">
                <Form @submit="handleFormSubmit" :validation-schema="schema">
                  <Field v-slot="{ field, errors }" name="password">
                    <v-text-field
                      class="input-text"
                      v-bind="field"
                      :error-messages="errors"
                      :error="errors.length !== 0"
                      placeholder="パスワード"
                      solo
                      density="compact"
                      variant="outlined"
                      type="password"
                      :appendToggleEye="true"
                      autocomplete="new-password"
                      :hide-details="errors.length <= 0"
                    />
                  </Field>
                  <Field v-slot="{ field, errors }" name="passwordConfirmation">
                    <v-text-field
                      class="input-text"
                      v-bind="field"
                      :error-messages="errors"
                      :error="errors.length !== 0"
                      placeholder="再度入力してください"
                      solo
                      density="compact"
                      variant="outlined"
                      type="password"
                      :appendToggleEye="true"
                      autocomplete="new-password"
                      :hide-details="errors.length <= 0"
                    />
                  </Field>
                  <div class="mb-1 mt-n1 text-left font-12px" v-if="error">
                    <FlashMessage :error="error" />
                  </div>
                  <v-btn
                    block
                    class="text-capitalize white--text font-14px fw-500 btn-primary"
                    type="submit"
                    >パスワードを更新する</v-btn
                  >

                  <div class="forget-blk mt-10 pt-6">
                    <router-link
                      class="forget-link fw-500 font-12px"
                      :to="{ name: 'Login' }"
                    >
                      ログイン画面へ戻る
                    </router-link>
                  </div>
                </Form>
              </div>
            </div>
          </v-col>
        </v-row>
      </v-container>
    </section>
  </v-app>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useStore } from 'vuex';
import { Form, Field, useForm } from 'vee-validate';
import FlashMessage from '@/components/FlashMessage.vue';
import * as yup from 'yup';
import { useI18n } from 'vue-i18n';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();
const logoUrl = new URL('@/assets/images/logo-big.svg', import.meta.url).href;

// Utility function to get the translated message from Vee-Validate config
const { t } = useI18n();
const getMessage = (key, params = {}) => {
  return t(key, params);
};

// Custom validation function for 'only_english_lang_allowed'
const onlyEnglishLangAllowed = (value) => {
  const englishRegex = /^[\u0000-\u007F]*$/; // Allows only ASCII characters (English letters, numbers, and symbols)
  // If value is empty, we don't want to fail the validation
  if (!value || value.trim() === '') {
    return true;
  }
  // Return true if validation passes, otherwise false
  return englishRegex.test(value);
};

// Create the Yup validation schema using the existing Vee-Validate messages
const schema = yup.object().shape({
  password: yup
    .string()
    .min(8, getMessage('field_min_message', { field: 'password', length: 8 }))
    .required(getMessage('field_required_message', { field: 'password' }))
    .test(
      'verify_password',
      getMessage('field_verify_password_message'),
      (value) => {
        // Add custom password criteria here, for example:
        const hasNumber = /\d/.test(value);
        const hasUpperCase = /[A-Z]/.test(value);
        const hasLowerCase = /[a-z]/.test(value);
        return hasNumber && hasUpperCase && hasLowerCase;
      }
    )
    .test(
      'only-english-lang-allowed',
      getMessage('field_allowed_only_en_lange_message'), // Message when validation fails
      onlyEnglishLangAllowed
    ),
  passwordConfirmation: yup
    .string()
    .required(
      getMessage('field_required_message', { field: 'password confirmation' })
    )
    .oneOf(
      [yup.ref('password')],
      getMessage('field_password_confirmed_message')
    ),
});

// State and variables
const store = useStore();
const error = ref('');

// Submit form handler
const handleFormSubmit = async (values) => {
  error.value = null;
  if (!route.query.token || route.query.token === '') {
    error.value = 'Token is required';
    return;
  }

  const payload = {
    password: values.password,
    password_confirmation: values.passwordConfirmation,
    token: route.query.token,
  };

  try {
    const response = await store.dispatch('AUTH_RESET', payload);
    if (response.status === 200) {
      router.push({ name: 'Login' });
    }
  } catch (err) {
    error.value = err.data?.error || 'An error occurred';
  }
};

onMounted(() => {
  store.dispatch('API_PROCESSING', false);
});
</script>

<style src="./__auth.scss" lang="scss"></style>

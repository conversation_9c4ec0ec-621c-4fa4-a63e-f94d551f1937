<template>
  <div class="chart-parent">
    <canvas ref="chart"></canvas>
  </div>
</template>

<script>
import Chart from 'chart.js/auto';
import 'chartjs-adapter-moment';

export default {
  mounted() {
    this.initPieChart();
  },
  props: {
    graphData: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      chartInstance: null,
      gradient1: null,
      gradient2: null,
      currentYear: null,
      currentMonth: null,
      recentMonths: [],
      colorStops: {
        color1: [
          'rgba(170, 21, 139, 1)', // #AA158B with full opacity
          'rgba(170, 21, 139, 0.5)', // #AA158B with 50% opacity
          'rgba(255, 255, 255, 0.1)', // White with 10% opacity for the last stop
        ],
      },
    };
  },
  computed: {
    formatted() {
      return this.formatData(this.graphData);
    },
  },
  methods: {
    generateRecentMonths() {
      if (this.recentMonths.length) return;

      const date = new Date();
      this.currentYear = date.getFullYear();
      this.currentMonth = date.getMonth() + 1;

      for (let i = 0; i < 12; i++) {
        const month = this.currentMonth - i;
        const adjustedYear = this.currentYear - (month <= 0 ? 1 : 0);
        const adjustedMonth = (month + 12) % 12 || 12;
        this.recentMonths.unshift(
          `${adjustedYear}-${String(adjustedMonth).padStart(2, '0')}`
        );
      }
    },
    formatData(data) {
      this.generateRecentMonths();

      const dataMap = data.reduce((acc, item) => {
        const monthKey = `${item.year}-${String(item.month).padStart(2, '0')}`;
        acc[monthKey] = item.total;
        return acc;
      }, {});

      return this.recentMonths.map((month) => dataMap[month] || 0);
    },
    createGradient(
      ctx,
      colorStops,
      data,
      positions = [0, 0.8, 1],
      defaultHeight = 300
    ) {
      // Validate input for colorStops and positions
      if (colorStops.length !== positions.length) {
        throw new Error(
          'The number of color stops must match the number of positions.'
        );
      }

      // Determine the gradient height
      const maxDataValue = Math.max(...data);
      const gradientHeight = Math.max(maxDataValue, defaultHeight); // Ensure a minimum gradient height

      // Create the linear gradient
      const gradient = ctx.createLinearGradient(0, 0, 0, gradientHeight * 0.6);

      // Add color stops to the gradient
      colorStops.forEach((color, index) => {
        const position = positions[index];
        gradient.addColorStop(position, color);
      });

      return gradient;
    },

    initPieChart() {
      if (this.chartInstance) {
        this.chartInstance.destroy();
      }

      const ctx = this.$refs.chart.getContext('2d');
      this.gradient1 = this.createGradient(
        ctx,
        this.colorStops.color1,
        this.formatted
      );

      this.chartInstance = new Chart(this.$refs.chart, {
        type: 'bar',
        data: {
          labels: this.recentMonths,
          datasets: [
            {
              data: this.formatted,
              borderColor: '#AA158B',
              backgroundColor: this.gradient1, // Use backgroundColor for bar charts
              pointBackgroundColor: '#AA158B',
              pointBorderColor: '#AA158B',
              borderWidth: 0.7,
              tension: 0.4,
              pointRadius: 2,
            },
          ],
        },
        options: {
          interaction: {
            mode: 'index',
            intersect: false,
          },
          plugins: {
            legend: {
              display: false,
            },
            tooltip: {
              enabled: true,
              displayColors: false,
              backgroundColor: '#ffffff',
              bodyColor: '#AA158B',
              titleColor: '#AA158B',
              borderWidth: 0.7,
              borderColor: 'rgba(0,0,0,0.3)',
            },
          },
          elements: {
            point: {
              radius: 0,
              hitRadius: 4,
              hoverRadius: 4,
            },
          },
          scales: {
            x: {
              grid: {
                display: false,
              },
              type: 'time',
              time: {
                displayFormats: {
                  month: 'M',
                },
                tooltipFormat: 'YYYY年 M月',
              },
            },
            y: {
              grid: {
                color: '#e9e9e9',
              },
              ticks: {
                stepSize: 10,
              },
            },
          },
          responsive: true,
          maintainAspectRatio: false,
        },
      });
    },
  },
  beforeDestroy() {
    if (this.chartInstance) {
      this.chartInstance.destroy();
    }
  },
};
</script>

<style scoped>
.chart-parent {
  height: 120px;
  width: 100%;
}
canvas {
  position: relative;
  width: 100%;
  height: 120px;
  z-index: 3;
}
</style>

<template>
  <div>
    <v-form @submit.prevent="submitInformation()">
      <PageTitle
        :items="{
          title: title,
          back: {
            action: () => {
              $router.push({
                name: 'InternshipPostList',
              });
            },
          },
        }"
      ></PageTitle>
      <v-row>
        <v-col cols="8" class="pa-4">
          <InternshipBasicInformation
            :basicInformation="basicInformation"
            :headings="headings"
          />
        </v-col>
        <v-col cols="4" class="pa-4">
          <v-card height="264px" class="text-center pt-14">
            <div class="button-width mx-auto">
              <div :class="{ 'px-2': smAndDown }" class="btn-container">
                <v-btn
                  class="full-width"
                  variant="outlined"
                  color="primary"
                  @click="openDraftPopup()"
                  >下書き保存</v-btn
                >
                <br />
                <v-btn
                  variant="outlined"
                  color="primary"
                  @click="submitInformationPreview()"
                  class="mt-6 full-width"
                  >プレビュー</v-btn
                >
                <br />
                <v-btn
                  block
                  type="submit"
                  color="#13ABA3"
                  class="white--text mt-6 button-width"
                  >公開</v-btn
                >
              </div>
            </div>
          </v-card>

          <v-card height="450px" class="pt-2 px-10 my-5">
            <!-- Header -->
            <div class="pt-6 text-center font-14px">連絡先</div>

            <!-- User 1 -->
            <v-row class="mt-4">
              <v-col cols="12" class="pt-0 mt-0">
                <label class="font-14px">企業管理ユーザ</label>
              </v-col>
              <v-col cols="1" class="pt-2 py-0 my-0 pr-0 mr-0 overflow-text">
                <label>1.</label>
              </v-col>
              <v-col cols="11" class="py-0 my-0">
                <!-- we need to manipulate in dom for init validation from JS -->
                <Field
                  v-if="!isShowFirstEmployee"
                  v-slot="{ field, errors }"
                  name="企業管理ユーザ1"
                  :rules="companyEditFields.rules"
                  :value="companyEditFields.contactable_user1_id"
                >
                  <v-select
                    v-bind="field"
                    density="compact"
                    variant="outlined"
                    single-line
                    class="w-100 small-label"
                    :model-value="companyEditFields.contactable_user1_id"
                    :items="formattedUsersAdmin"
                    :item-title="(item) => `${item.name} | ${item.email_valid ? item.email_valid : item.email_invalid ? `${item.email_invalid} (未認証)`  : ''}`"
                    no-data-text="データがありません"
                    :item-value="(item) => item.id"
                    placeholder="選択してください"
                    :error-messages="errors"
                    :error="errors.length !== 0"
                    :hide-details="errors.length <= 0"
                    :class="{
                      'bg-input-disabled': !basicInformation[0]?.value,
                    }"
                    menu-icon="$greyExpansionDropdown"
                  >
                  </v-select>
                </Field>
                <Field
                  v-if="isShowFirstEmployee"
                  v-slot="{ field, errors }"
                  name="企業管理ユーザ1"
                  :rules="companyEditFields.rules"
                  :value="companyEditFields.contactable_user1_id"
                >
                  <v-select
                    v-bind="field"
                    density="compact"
                    variant="outlined"
                    single-line
                    class="w-100 small-label"
                    :model-value="companyEditFields.contactable_user1_id"
                    :items="formattedUsersAdmin"
                    :item-title="(item) => `${item.name} | ${item.email_valid ? item.email_valid : item.email_invalid ? `${item.email_invalid} (未認証)`  : ''}`"
                    no-data-text="データがありません"
                    :item-value="(item) => item.id"
                    placeholder="選択してください"
                    :error-messages="errors"
                    :error="errors.length !== 0"
                    :hide-details="errors.length <= 0"
                    :class="{
                      'bg-input-disabled': !basicInformation[0]?.value,
                    }"
                    menu-icon="$greyExpansionDropdown"
                  >
                  </v-select>
                </Field>
              </v-col>
            </v-row>

            <!-- User 2 -->
            <v-row class="mt-5">
              <v-col cols="12" class="pt-0 mt-0">
                <label class="font-14px">企業一般ユーザ</label>
              </v-col>
              <v-col cols="1" class="pt-2 py-0 my-0 pr-0 mr-0 overflow-text">
                <label>2.</label>
              </v-col>
              <v-col cols="10" class="py-0 my-0">
                <Field
                  v-slot="{ field: { value, ...fieldWithoutValue }, errors }"
                  name="企業一般ユーザ2"
                >
                  <v-select
                    v-bind="fieldWithoutValue"
                    density="compact"
                    variant="outlined"
                    single-line
                    class="w-100 small-label"
                    v-model="companyEditFields.contactable_user2_id"
                    :items="formattedUsers"
                    :item-title="(item) => `${item.name} | ${item.email_valid ? item.email_valid : item.email_invalid ? `${item.email_invalid} (未認証)`  : ''}`"
                    no-data-text="データがありません"
                    :item-value="(item) => item.id"
                    placeholder="選択してください"
                    :error-messages="errors"
                    :error="errors.length !== 0"
                    :hide-details="errors.length <= 0"
                    :class="{
                      'bg-input-disabled': !basicInformation[0]?.value,
                    }"
                    menu-icon="$greyExpansionDropdown"
                  >
                  </v-select>
                </Field>
              </v-col>
              <v-col
                cols="1"
                class="d-flex justify-end py-0 my-0 pt-1 pointer ml-0 pl-0"
              >
                <v-icon
                  @click="companyEditFields.contactable_user2_id = null"
                  size="24px"
                  >$TrashSmall</v-icon
                >
              </v-col>
            </v-row>

            <!-- User 3 -->
            <v-row class="mt-5">
              <v-col cols="1" class="pt-2 py-0 my-0 pr-0 mr-0 overflow-text">
                <label>3.</label>
              </v-col>
              <v-col cols="10" class="py-0 my-0">
                <Field
                  v-slot="{ field: { value, ...fieldWithoutValue }, errors }"
                  name="企業一般ユーザ3"
                >
                  <v-select
                    v-bind="fieldWithoutValue"
                    density="compact"
                    variant="outlined"
                    single-line
                    class="w-100 small-label"
                    v-model="companyEditFields.contactable_user3_id"
                    :items="formattedUsers"
                    :item-title="(item) => `${item.name} | ${item.email_valid ? item.email_valid : item.email_invalid ? `${item.email_invalid} (未認証)`  : ''}`"
                    no-data-text="データがありません"
                    :item-value="(item) => item.id"
                    placeholder="選択してください"
                    :error-messages="errors"
                    :error="errors.length !== 0"
                    :hide-details="errors.length <= 0"
                    :class="{
                      'bg-input-disabled': !basicInformation[0]?.value,
                    }"
                    menu-icon="$greyExpansionDropdown"
                  >
                  </v-select>
                </Field>
              </v-col>
              <v-col
                cols="1"
                class="d-flex justify-end py-0 my-0 pt-1 pointer ml-0 pl-0"
              >
                <v-icon
                  @click="companyEditFields.contactable_user3_id = null"
                  size="24px"
                  >$TrashSmall</v-icon
                >
              </v-col>
            </v-row>

            <!-- User 4 -->
            <v-row class="mt-5">
              <v-col cols="1" class="pt-2 py-0 my-0 pr-0 mr-0 overflow-text">
                <label>4.</label>
              </v-col>
              <v-col cols="10" class="py-0 my-0">
                <Field
                  v-slot="{ field: { value, ...fieldWithoutValue }, errors }"
                  name="企業一般ユーザ4"
                >
                  <v-select
                    v-bind="fieldWithoutValue"
                    density="compact"
                    variant="outlined"
                    single-line
                    class="w-100 small-label"
                    v-model="companyEditFields.contactable_user4_id"
                    :items="formattedUsers"
                    :item-title="(item) => `${item.name} | ${item.email_valid ? item.email_valid : item.email_invalid ? `${item.email_invalid} (未認証)`  : ''}`"
                    no-data-text="データがありません"
                    :item-value="(item) => item.id"
                    placeholder="選択してください"
                    :error-messages="errors"
                    :error="errors.length !== 0"
                    :hide-details="errors.length <= 0"
                    :class="{
                      'bg-input-disabled': !basicInformation[0]?.value,
                    }"
                    menu-icon="$greyExpansionDropdown"
                  >
                  </v-select>
                </Field>
              </v-col>
              <v-col
                cols="1"
                class="d-flex justify-end py-0 my-0 pt-1 pointer ml-0 pl-0"
              >
                <v-icon
                  @click="companyEditFields.contactable_user4_id = null"
                  size="24px"
                  >$TrashSmall</v-icon
                >
              </v-col>
            </v-row>

            <!-- User 5 -->
            <v-row class="mt-5">
              <v-col cols="1" class="pt-2 py-0 my-0 pr-0 mr-0 overflow-text">
                <label>5.</label>
              </v-col>
              <v-col cols="10" class="py-0 my-0">
                <Field
                  v-slot="{ field: { value, ...fieldWithoutValue }, errors }"
                  name="企業一般ユーザー5"
                >
                  <v-select
                    v-bind="fieldWithoutValue"
                    density="compact"
                    variant="outlined"
                    single-line
                    class="w-100 small-label"
                    v-model="companyEditFields.contactable_user5_id"
                    :items="formattedUsers"
                    :item-title="(item) => `${item.name} | ${item.email_valid ? item.email_valid : item.email_invalid ? `${item.email_invalid} (未認証)`  : ''}`"
                    no-data-text="データがありません"
                    :item-value="(item) => item.id"
                    placeholder="選択してください"
                    :error-messages="errors"
                    :error="errors.length !== 0"
                    :hide-details="errors.length <= 0"
                    :class="{
                      'bg-input-disabled': !basicInformation[0]?.value,
                    }"
                    menu-icon="$greyExpansionDropdown"
                  >
                  </v-select>
                </Field>
              </v-col>
              <v-col
                cols="1"
                class="d-flex justify-end py-0 my-0 pt-1 pointer ml-0 pl-0"
              >
                <v-icon
                  @click="companyEditFields.contactable_user5_id = null"
                  size="24px"
                  >$TrashSmall</v-icon
                >
              </v-col>
            </v-row>
            <v-row
              class="d-flex align-center justify-end mt-8 cursor-pointer"
              :class="{ 'disabled-class': !basicInformation[0]?.value }"
              @click="!basicInformation[0]?.value ? null : openNewTab()"
            >
              <v-col
                cols="12"
                class="py-0 my-0 d-flex align-center justify-end"
              >
                <span
                  class="font-14px mr-2"
                  :class="{
                    'text-primary': basicInformation[0]?.value,
                    'text-grey': !basicInformation[0]?.value,
                  }"
                >
                  企業ユーザ管理
                </span>
                <span
                  aria-hidden="true"
                  class="v-icon notranslate theme--light error--text"
                >
                  <v-icon v-if="basicInformation[0]?.value" size="24px"
                    >$ChevronRightSmall</v-icon
                  >
                  <v-icon v-else size="24px">$ChevronRightSmallGrey</v-icon>
                </span>
              </v-col>
            </v-row>
          </v-card>

          <ImageUpload :data="imageDetails" />
        </v-col>
      </v-row>
    </v-form>

    <SimpleModel
      text="この求人を下書きとして保存しますか？"
      :dialog="dialog.saveAsDraft"
      @submitSuccess="submitInformation('Y')"
      @closeModel="dialog.saveAsDraft = false"
    ></SimpleModel>
    <InterPreviewModel
      :dialog="dialog.preview"
      @submitSuccess="preview()"
      @closeModel="dialog.preview = false"
    ></InterPreviewModel>

    <SuccessModel
      :text="alertText"
      :buttonText="buttonText"
      :routeName="routeName"
      :dialog="successDialog"
      @closeModel="successDialog = false"
    >
    </SuccessModel>
    <SimpleModel
      :text="alertText"
      :dialog="dialog.alert"
      :buttonOption="{ hideSubmit: true, hideCancel: true }"
      showCloseIcon
      @closeModel="dialog.alert = false"
    ></SimpleModel>
  </div>
</template>

<script>
import {
  ref,
  computed,
  onMounted,
  watch,
  nextTick,
  defineAsyncComponent,
} from 'vue';
import { useRouter } from 'vue-router';
import { useForm } from 'vee-validate';
const PageTitle = defineAsyncComponent(
  () => import('@/components/ui/PageTitle.vue')
);
const InternshipBasicInformation = defineAsyncComponent(
  () => import('@/components/pages/PostInputs.vue')
);
const ImageUpload = defineAsyncComponent(
  () => import('@/components/ui/ImageUpload.vue')
);
const SimpleModel = defineAsyncComponent(
  () => import('@/components/models/SimpleModel.vue')
);
const InterPreviewModel = defineAsyncComponent(
  () => import('@/components/models/InterPreviewModel.vue')
);
const SuccessModel = defineAsyncComponent(
  () => import('@/components/models/SuccessModel.vue')
);
import InternshipMixins from './internship.mixin';
import { useDisplay } from 'vuetify';
import { useStore } from 'vuex';

export default {
  name: 'InternshipPostCreate',
  components: {
    InternshipBasicInformation,
    PageTitle,
    ImageUpload,
    SimpleModel,
    SuccessModel,
    InterPreviewModel,
  },
  setup() {
    const {
      routeName,
      userSiteUrl,
      isPreview,
      previewedData,
      getPageFields,
      basicInformation,
      savePageData,
      imageDetails,
      preview,
      openDraftPopup,
      dialog,
      saveAsDraft,
      isDraftOrPublic,
      successDialog,
      alertText,
      buttonText,
      companyEditFields,
      setRequired,
    } = InternshipMixins();
    const { mdAndUp, smAndDown } = useDisplay();
    const title = ref('求人広告新規作成');
    const headings = ref({
      heading1: 3,
      heading2: 4,
    });

    const router = useRouter();

    const { validate, setErrors, errors } = useForm();

    const submitInformationPreview = async () => {
      isDraftOrPublic.value = '0'; // Set as draft initially
      const { valid, errors: validationErrors } = await validate(); // Run the form validation
      if (!valid) {
        setErrors(validationErrors);
        return;
      }
      isPreview.value = true;
      savePageData('create');
    };

    /**
     * Component initialization
     * Fetches initial page fields for the internship form
     */
    onMounted(() => {
      getPageFields();
    });

    /**
     * Fetches users associated with the selected company
     * Called when company selection changes
     */
    const fetchCompanyUsers = async () => {
      if (companyId.value) {
        await store.dispatch('COMPANY_USERS_GET_ALL', {
          company_id: companyId.value,
        });
      }
    };

    const store = useStore();

    /**
     * Computed property to access all company users from Vuex store
     */
    const getAllCompanyUsers = computed(() => store.getters.getAllCompanyUsers);

    /**
     * Formats and filters admin users (type 1) for the company
     * Used for primary contact selection dropdown
     * @returns {Array} Array of admin users with formatted name and email
     */
    const formattedUsersAdmin = computed(() => {
      return (
        getAllCompanyUsers.value
          ?.map((user) => ({
            name: `${user.surname} ${user.name}`,
            email_valid: user.email_valid,
            email_invalid: user.email_invalid,
            id: user.id,
            isCompany: user.company_id === basicInformation.value[0]?.value,
            type: user.type_user,
          }))
          .filter((user) => user.isCompany && user.type === 1) || []
      );
    });

    /**
     * Formats and filters all company users
     * Used for secondary contact selection dropdowns
     * @returns {Array} Array of all company users with formatted name and email
     */
    const formattedUsers = computed(() => {
      return (
        getAllCompanyUsers.value
          ?.map((user) => ({
            name: `${user.surname} ${user.name}`,
            email_valid: user.email_valid,
            email_invalid: user.email_invalid,
            id: user.id,
            isCompany: user.company_id === basicInformation.value[0]?.value,
            type: user.type_user,
          }))
          .filter((user) => user.isCompany) || []
      );
    });

    /**
     * Finds the first admin user (type 1) in the company
     * Used for default primary contact selection
     */
    const firstUserWithType1 = computed(() =>
      formattedUsers.value.find((user) => user.type === 1)
    );

    /**
     * Gets selected company ID from basic information
     */
    const companyId = computed(() => basicInformation.value[0]?.value);

    /**
     * Watches for company changes to update user lists
     * Automatically sets primary contact to first admin user when company changes
     */
    const isShowFirstEmployee = ref(false);
    watch(companyId, async () => {
      isShowFirstEmployee.value = true;
      companyEditFields.value.contactable_user1_id = null;
      await fetchCompanyUsers();
      await nextTick(); // Ensure DOM updates

      if (formattedUsers.value.length > 0 && firstUserWithType1.value) {
        companyEditFields.value.contactable_user1_id =
          firstUserWithType1?.value?.id || '';
        setTimeout(() => {
          companyEditFields.value.contactable_user1_id =
            firstUserWithType1?.value?.id || '';
          isShowFirstEmployee.value = false;
        }, 500);
      }
    });

    /**
     * Handles form submission for both draft and publish
     * @param {string} isDraftCheck - 'Y' for draft, 'N' for publish
     */
    const submitInformation = async (isDraftCheck = 'N') => {
      isDraftOrPublic.value = '0'; // Set as draft initially
      if (isDraftCheck !== 'Y') {
        const { valid, errors: validationErrors } = await validate(); // Run validations only for publish
        if (!valid) {
          setErrors(validationErrors);
          return;
        }
        setRequired();
      }

      savePageData('create');
      dialog.value.saveAsDraft = false;
    };

    const openNewTab = () => {
      const routeData = router.resolve({
        name: 'CorporateDetails',
        params: { id: basicInformation.value[0]?.value },
      });
      window.open(routeData.href, '_blank');
    };

    return {
      title,
      headings,
      basicInformation,
      imageDetails,
      alertText,
      buttonText,
      successDialog,
      dialog,
      submitInformation,
      openDraftPopup,
      preview,
      saveAsDraft,
      router,
      routeName,
      mdAndUp,
      smAndDown,
      formattedUsers,
      formattedUsersAdmin,
      companyEditFields,
      openNewTab,
      isShowFirstEmployee,
      submitInformationPreview,
    };
  },
};
</script>

<style lang="scss" src="./style.scss"></style>

<template>
  <v-app>
    <section class="auth-main-section font-Noto-Sans text-center">
      <v-container fill-height>
        <v-row>
          <v-col cols="12">
            <div class="login-main-blk">
              <div class="login-top-blk mb-10 pb-7">
                <div class="logo-circle-top">
                  <v-img class="mx-auto" :src="logoUrl" lazy></v-img>
                </div>
              </div>
              <Form @submit="login">
                <div>
                  <Field
                    name="email"
                    rules="required|email"
                    v-slot="{ field, errors }"
                  >
                    <v-text-field
                      v-bind="field"
                      :error-messages="errors"
                      :error="errors.length > 0"
                      :hide-details="errors.length <= 0"
                      placeholder="メールアドレス"
                      density="compact"
                      variant="outlined"
                      class="input-text"
                    />
                  </Field>
                </div>

                <div>
                  <Field
                    name="password"
                    rules="required"
                    v-slot="{ field, errors }"
                  >
                    <input-password
                      v-bind="field"
                      :error-messages="errors"
                      :error="errors.length > 0"
                      :hide-details="errors.length <= 0"
                      placeholder="パスワード"
                      density="compact"
                      variant="outlined"
                      :appendToggleEye="true"
                    />
                  </Field>
                </div>

                <div
                  v-if="error"
                  class="mb-1 mt-2 text-left font-12px text-body-2 error--text"
                >
                  <FlashMessage :error="error" />
                </div>

                <v-btn
                  block
                  class="text-capitalize white--text font-14px fw-500 btn-primary mt-4"
                  type="submit"
                  :loading="getApiProcessingStatus"
                  :disabled="getApiProcessingStatus"
                >
                  ログイン
                </v-btn>

                <div class="forget-blk pt-8">
                  <router-link
                    class="forget-link fw-500 font-12px"
                    :to="{ name: 'ForgotPassword' }"
                  >
                    パスワードを忘れた方はこちら
                  </router-link>
                </div>
              </Form>
            </div>
          </v-col>
        </v-row>
      </v-container>
    </section>
  </v-app>
</template>

<script>
// Import Vue core functionalities
import { ref, computed, onMounted } from 'vue';
// Import state management and routing
import { useStore } from 'vuex';
import { useRouter, useRoute } from 'vue-router';
// Import internationalization
import { useI18n } from 'vue-i18n';
// Import form validation components
import { Form, Field, ErrorMessage } from 'vee-validate';
// Import custom components
import InputPassword from '@/components/ui/InputPassword.vue';
import FlashMessage from '@/components/FlashMessage.vue';

export default {
  name: 'Login',
  // Register components used in the template
  components: { InputPassword, FlashMessage, Form, Field, ErrorMessage },

  setup() {
    // Initialize Vuex store and routing utilities
    const store = useStore();
    const router = useRouter();
    const route = useRoute();
    // Initialize i18n for translations
    const { t } = useI18n();

    // Define reactive state variables
    const email = ref(''); // Store email input
    const password = ref(''); // Store password input
    const error = ref(''); // Store error messages

    // Get logo URL using Vite's import.meta.url
    const logoUrl = new URL('@/assets/images/logo-big.svg', import.meta.url)
      .href;

    // Computed property to track API request status
    // Used to show loading state and disable form submission
    const getApiProcessingStatus = computed(
      () => store.getters.getApiProcessingStatus
    );

    /**
     * Handle login form submission
     * @param {Object} values - Form values from vee-validate
     * @param {string} values.email - User email
     * @param {string} values.password - User password
     */
    const login = async (values) => {
      const { email, password } = values;

      try {
        // Attempt authentication
        await store.dispatch('AUTH_REQUEST', { email, password });

        // Handle redirect after successful login
        if (route.query.redirect && route.query.redirect !== '') {
          // Redirect to the originally requested page
          router.push(route.query.redirect);
        } else {
          // Default redirect to Dashboard
          router.push({ name: 'Dashboard' });
        }
      } catch (err) {
        // Set error message using i18n translation
        error.value = t('validation.login.api_error_message');
      }
    };

    // Initialize component
    onMounted(() => {
      // Reset API processing status on component mount
      store.dispatch('API_PROCESSING', false);
    });

    // Expose necessary properties and methods to template
    return {
      email,
      password,
      error,
      logoUrl,
      getApiProcessingStatus,
      login,
    };
  },
};
</script>

<style src="./__auth.scss"></style>

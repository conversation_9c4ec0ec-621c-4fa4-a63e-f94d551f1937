// Import Vuetify and necessary icon sets
import { createVuetify } from 'vuetify';
import { VTreeview } from 'vuetify/labs/VTreeview';
import { aliases, mdi } from 'vuetify/iconsets/mdi'; // Import Material Design Icons set
import 'vuetify/styles'; // Ensure styles are imported
import config from '@/config';
import DateFnsAdapter from '@date-io/date-fns';
import { ja } from 'date-fns/locale';

// Import custom icons
import ArrowRightDrop from '@/components/icons/ArrowRightDrop.vue';
import Setting from '@/components/icons/Setting.vue';
import Companies from '@/components/icons/Companies.vue';
import Bell from '@/components/icons/Bell.vue';
import Search from '@/components/icons/Search.vue';
import Chat from '@/components/icons/Chat.vue';
import Account from '@/components/icons/Account.vue';
import HomeIcon from '@/components/icons/HomeIcon.vue';
import SwapVertical from '@/components/icons/SwapVertical.vue';
import EyeClose from '@/components/icons/EyeClose.vue';
import EyeOpen from '@/components/icons/EyeOpen.vue';
import Warning from '@/components/icons/Warning.vue';
import WarningRed from '@/components/icons/WarningRed.vue';
import Pincel from '@/components/icons/Pincel.vue';
import OpenNewTab from '@/components/icons/OpenNewTab.vue';
import PreviousAngleCircle from '@/components/icons/PreviousAngleCircle.vue';
import Star from '@/components/icons/Star.vue';
import StarFilled from '@/components/icons/StarFilled.vue';
import Check from '@/components/icons/Check.vue';
import VerifiedCheck from '@/components/icons/VerifiedCheck.vue';
import CrossIcon from '@/components/icons/CrossIcon.vue';
import CameraIcon from '@/components/icons/CameraIcon.vue';
import ChevronLeftFilledIcon from '@/components/icons/ChevronLeftFilledIcon.vue';
import HeartIcon from '@/components/icons/HeartIcon.vue';
import PaperPlaneIcon from '@/components/icons/PaperPlaneIcon.vue';
import BackArrow from '@/components/icons/BackArrow.vue';
import LeadershipIcon from '@/components/icons/LeadershipIcon.vue';
import IndepthIcon from '@/components/icons/IndepthIcon.vue';
import CloseIcon from '@/components/icons/CloseIcon.vue';
import EyeFillIcon from '@/components/icons/EyeFillIcon.vue';
import OpenPaperPlaneIcon from '@/components/icons/OpenPaperPlaneIcon.vue';
import MessagesIcon from '@/components/icons/MessagesIcon.vue';
import DeleteIcon from '@/components/icons/DeleteIcon.vue';
import NotificationBubbleIcon from '@/components/icons/NotificationBubbleIcon.vue';
import DropdownIcon from '@/components/icons/DropdownIcon.vue';
import MessageIcon from '@/components/icons/MessageIcon.vue';
import EditIcon from '@/components/icons/EditIcon.vue';
import EditGrayIcon from '@/components/icons/EditGrayIcon.vue';
import Logo from '@/components/icons/Logo.vue';
import LovedIcon from '@/components/icons/LovedIcon.vue';
import StipendIcon from '@/components/icons/StipendIcon.vue';
import TimeIcon from '@/components/icons/TimeIcon.vue';
import UniversityIcon from '@/components/icons/UniversityIcon.vue';
import CalenderIcon from '@/components/icons/CalenderIcon.vue';
import FingerTouch from '@/components/icons/FingerTouch.vue';
import FavouritedIcon from '@/components/icons/FavouritedIcon.vue';
import GreenCross from '@/components/icons/GreenCross.vue';
import LeftArrow from '@/components/icons/LeftArrow.vue';
import RightArrow from '@/components/icons/RightArrow.vue';
import NavigateRightArrowIcon from '@/components/icons/NavigateRightArrowIcon.vue';
import NavigateLeftIcon from '@/components/icons/NavigateLeftIcon.vue';
import ExpansionDropDownArrow from '@/components/icons/ExpansionDropDownArrow.vue';
import ContractIcon from '@/components/icons/ContractIcon.vue';
import FeedIcon from '@/components/icons/FeedIcon.vue';
import QuestionIcon from '@/components/icons/QuestionIcon.vue';
import HandShakeIcon from '@/components/icons/HandShakeIcon.vue';
import NotificationIcon from '@/components/icons/NotificationIcon.vue';
import FeedbackIcon from '@/components/icons/FeedbackIcon.vue';
import HomeErrorIcon from '@/components/icons/HomeErrorIcon.vue';
import TrashSmall from '@/components/icons/TrashSmall.vue';
import ChevronRightSmall from '@/components/icons/ChevronRightSmall.vue';
import ChevronRightSmallGrey from '@/components/icons/ChevronRightSmallGrey.vue';
import CopyIcon from '@/components/icons/CopyIcon.vue';
import CopySmallIcon from '@/components/icons/CopySmallIcon.vue';

// Create Vuetify instance
const vuetify = createVuetify({
  date: {
    adapter: new DateFnsAdapter({ locale: ja }),
  },
  icons: {
    defaultSet: 'mdi',
    aliases: {
      ...aliases,
      LeftArrow: ArrowRightDrop,
      RightArrow: RightArrow,
      GreenCross: GreenCross,
      Logo: Logo,
      ArrowRightDrop: ArrowRightDrop,
      Search: Search,
      Companies: Companies,
      Chat: Chat,
      Bell: Bell,
      Account: Account,
      Setting: Setting,
      HomeIcon: HomeIcon,
      SwapVertical: SwapVertical,
      EyeClose: EyeClose,
      EyeOpen: EyeOpen,
      Warning: Warning,
      WarningRed: WarningRed,
      Pincel: Pincel,
      OpenNewTab: OpenNewTab,
      PreviousAngleCircle: PreviousAngleCircle,
      Star: Star,
      StarFilled: StarFilled,
      Check: Check,
      VerifiedCheck: VerifiedCheck,
      CrossIcon: CrossIcon,
      CameraIcon: CameraIcon,
      ChevronLeft: ChevronLeftFilledIcon,
      HeartIcon: HeartIcon,
      PaperPlaneIcon: PaperPlaneIcon,
      BackArrow: BackArrow,
      leadership: LeadershipIcon,
      indepth: IndepthIcon,
      close: CloseIcon,
      eyeFill: EyeFillIcon,
      openPaperPlane: OpenPaperPlaneIcon,
      messages: MessagesIcon,
      delete: DeleteIcon,
      notification: NotificationBubbleIcon,
      dropdown: DropdownIcon,
      grayDropdown: {
        component: DropdownIcon,
        props: { fill: '#B8B8B8' },
      },
      message: MessageIcon,
      edit: EditIcon,
      editGray: EditGrayIcon,
      calenderIcon: CalenderIcon,
      fingerTouch: FingerTouch,
      lovedIcon: LovedIcon,
      stipendIcon: StipendIcon,
      timeIcon: TimeIcon,
      universityIcon: UniversityIcon,
      favouritedIcon: FavouritedIcon,
      navigateRight: NavigateRightArrowIcon,
      navigateLeft: NavigateLeftIcon,
      greyExpansionDropdown: ExpansionDropDownArrow,
      contractIcon: ContractIcon,
      FeedIcon: FeedIcon,
      QuestionIcon: QuestionIcon,
      HandShakeIcon: HandShakeIcon,
      NotificationIcon: NotificationIcon,
      FeedbackIcon: FeedbackIcon,
      HomeErrorIcon: HomeErrorIcon,
      TrashSmall: TrashSmall,
      ChevronRightSmall: ChevronRightSmall,
      ChevronRightSmallGrey: ChevronRightSmallGrey,
      CopyIcon: CopyIcon,
      CopySmallIcon: CopySmallIcon,
    },
    sets: {
      mdi, // Add Material Design Icons set
    },
  },
  theme: {
    defaultTheme: 'light',
    themes: {
      light: config.light, // Use your configuration for light theme
    },
  },
  components: {
    VTreeview,
  },
  variables: {
    fontFamily: 'Noto Sans, sans-serif',
  },
});

export default vuetify;

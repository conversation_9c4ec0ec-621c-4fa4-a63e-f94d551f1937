<template>
  <div v-show="!loading && !$store.getters.getApiProcessingStatus">
    <ActionAlertCard
      v-if="showStatusAlert"
      class="text-center"
      :message="statusAlertMessage"
      width="610px"
      height="190px"
      type="success"
      :style="{
        position: 'fixed',
        margin: 'auto',
        left: '0',
        right: '0',
        top: '60px',
      }"
    />
    <Form @submit="submitNotice">
      <PageTitle
        :items="{
          title: title,
          subTitle: subTitle,
        }"
      ></PageTitle>
      <v-row v-if="!getApiProcessingStatus">
        <v-col class="pr-4" cols="8">
          <v-card height="770px" class="text-center pa-16">
            <div class="d-flex justify-end">最終更新日：{{ todayDate }}</div>
            <v-col cols="12" md="12" class="mb-n5">
              <label class="text-left d-block font-14px mb-1">
                <span>タイトル</span>
                <span class="error--text ml-2 font-12px">必須</span>
              </label>
              <Field
                v-slot="{ field, errors }"
                name="titlenotice"
                :value="notice_title"
                rules="required|max:60"
              >
                <v-text-field
                  v-bind="field"
                  :error-messages="errors"
                  :error="errors.length !== 0"
                  :hide-details="errors.length <= 0"
                  variant="outlined"
                  density="compact"
                  v-model="notice_title"
                  placeholder="タイトルを入力してください"
                  color="grey"
                  id="name"
                  name="name"
                >
                </v-text-field>
                <!-- Counter positioned below the input -->
                <div class="text-b8b8b8 font-12px text-right">
                  {{ notice_title?.length || '0' }} / 60
                </div>
              </Field>

              <!-- content -->
              <label class="text-left d-block font-14px mb-1 mt-4">
                <span>お知らせ内容</span>
                <span class="error--text ml-2 font-12px">必須</span>
              </label>
              <Field
                v-slot="{ field, errors }"
                name="notice_content"
                :value="notice_content.value"
                :rules="
                  !notice_content.value ||
                  notice_content.value === '<p><br></p>'
                    ? 'required'
                    : ''
                "
              >
                <rich-text-editor
                  v-bind="field"
                  :id="notice_content.name"
                  :error-messages="errors"
                  v-model:content="notice_content.value"
                  @update:content="handleTextChange"
                  :isCounter="true"
                />
                <template
                  v-if="
                    (errors && errors.length > 0) ||
                    notice_content.value === '<p><br></p>' ||
                    !notice_content.value
                  "
                >
                  <div class="font-12px error--text text-left">
                    {{ errors[0] || '入力されていない項目があります' }}
                  </div>
                </template>
              </Field>
            </v-col>
          </v-card>
        </v-col>
        <v-col class="pl-4" cols="4">
          <v-card height="207px" class="text-center pt-16">
            <div class="button-width mx-auto">
              <div :class="{ 'px-2': smAndDown }" class="btn-container">
                <v-btn
                  block
                  class="white--text button-width"
                  type="submit"
                  color="#13ABA3"
                  >公開</v-btn
                >
                <br />
                <div
                  @click.stop="deleteInitiate()"
                  class="font-14px mt-2 cursor-pointer"
                  :class="{
                    'text-b8b8b8 cancel-mouse-event': !notice_id,
                    'error--text': notice_id,
                  }"
                >
                  削除
                </div>
              </div>
            </div>
          </v-card>
        </v-col>
      </v-row>
    </Form>
    <SimpleModel
      text="この通知を削除してもよろしいですか?"
      :dialog="dialog.delete"
      @submitSuccess="deleteNotice()"
      :submitButtonText="'削除する'"
      @closeModel="dialog.delete = false"
    ></SimpleModel>
  </div>
</template>

<script setup>
/**
 * Imports and Setup
 */
import { ref, computed, onMounted, defineAsyncComponent } from 'vue';
import { useStore } from 'vuex';
const PageTitle = defineAsyncComponent(
  () => import('@/components/ui/PageTitle.vue')
);
const RichTextEditor = defineAsyncComponent(
  () => import('@/components/ui/RichTextEditor.vue')
);
const SimpleModel = defineAsyncComponent(
  () => import('@/components/models/SimpleModel.vue')
);
const ActionAlertCard = defineAsyncComponent(
  () => import('@/components/ui/ActionAlertCard.vue')
);
import moment from 'moment';
import { useDisplay } from 'vuetify';
import { Field, Form } from 'vee-validate';

const statusAlertMessage = ref('');
const showStatusAlert = ref(false);

/**
 * Responsive display utilities from Vuetify
 */
const { smAndDown } = useDisplay();

/**
 * Vuex store initialization
 */
const store = useStore();

/**
 * Computed Properties
 */
// Tracks API loading state
const getApiProcessingStatus = computed(
  () => store.getters.getApiProcessingStatus
);

/**
 * Component State Management
 */
// Page title configuration (Japanese)
const title = 'お知らせ管理'; // Notice Management
const subTitle = '編集'; // Edit

// Form fields and state
const todayDate = ref(moment().format('YYYY/MM/DD'));
const notice_title = ref('');
const notice_content = ref({
  name: 'notice_content',
  value: '',
});
const notice_id = ref(null);
const dialog = ref({ delete: false });

/**
 * Rich Text Editor Event Handler
 * Syncs editor content with form state
 * @param {string} newContent - Updated content from editor
 */
const handleTextChange = (newContent) => {
  notice_content.value.value = newContent;
};

/**
 * Vuex Getters
 * Access to notice data from store
 */
const getAllNotices = computed(() => store.getters.getAllNotices);

/**
 * Component Lifecycle
 * Fetches and sets initial notice data
 */
const loading = ref(false);
onMounted(async () => {
  loading.value = true;
  await store.dispatch('NOTICE_GET_ALL');
  if (getAllNotices.value) {
    notice_id.value = getAllNotices.value.id;
    notice_title.value = getAllNotices.value.notice_title;
    notice_content.value.value = getAllNotices.value.notice_content;
    todayDate.value = moment(getAllNotices.value.created_at).format(
      'YYYY/MM/DD'
    );
  }
  loading.value = false;
});

/**
 * Form Submission Handler
 * Creates or updates notice
 */
const submitNotice = async () => {
  const params = {
    notice_content: notice_content.value.value,
    notice_title: notice_title.value,
  };

  try {
    await store.dispatch('NOTICE_CREATE', params);
    await store.dispatch('NOTICE_GET_ALL'); // Refresh notice list

    // Assuming getAllNotices.value is an array, get the id of the last created notice, if needed.
    if (getAllNotices.value) {
      notice_id.value = getAllNotices.value.id;
    } else {
      notice_id.value = null; // or handle the case when there are no notices.
    }
    showAlert('お知らせを公開しました。');
  } catch (error) {
    // Handle validation errors
    if (error?.status === 422) {
      $refs.observer.setErrors(
        error?.data?.error?.errors || error?.data?.errors
      );
    } else {
      // Handle other errors (e.g., network errors, server errors)
      console.error('Error submitting notice:', error);
      // Optionally, show a user-friendly error message.
    }
  }
};

const showAlert = (message) => {
  statusAlertMessage.value = message;
  showStatusAlert.value = true;
  setTimeout(() => {
    showStatusAlert.value = false;
  }, 5000);
};

/**
 * Notice Deletion Handler
 * Deletes notice and resets form
 */
const deleteNotice = async () => {
  const params = {
    id: notice_id.value,
    notice_content: notice_content.value.value,
    notice_title: notice_title.value,
  };
  try {
    await store.dispatch('NOTICE_DELETE', params);
    dialog.value.delete = false;
    store.dispatch('NOTICE_GET_ALL');
    // Reset form state
    notice_title.value = '';
    notice_content.value.value = '<p><br></p>';
    notice_id.value = null;
    showAlert('お知らせを削除しました。');
  } catch (error) {
    dialog.value.delete = false;
  }
};

/**
 * Delete Confirmation Dialog Handler
 * Opens delete confirmation dialog
 */
const deleteInitiate = () => {
  dialog.value.delete = true;
};
</script>

<style lang="scss" src="./style.scss" scoped></style>

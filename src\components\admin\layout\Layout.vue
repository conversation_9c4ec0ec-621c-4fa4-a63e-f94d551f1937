<template>
  <v-app class="pa-0">
    <Header />
    <Sidebar />
    <v-main class="content">
      <div class="v-main__wrap font-Noto-Sans">
        <router-view v-slot="{ Component }">
          <component :is="Component" />
        </router-view>
      </div>
    </v-main>
    <ActionAlertCard
      class="text-center"
      v-if="getStatusAlert"
      :message="getStatusAlertMessage"
      width="610px"
      height="190px"
      :type="'success'"
      :alertStyle="'position: absolute; top: 20px'"
    />
    <div
      class="loader-application d-flex align-center justify-center"
      v-if="
        $store.getters.getApiProcessingStatus ||
        $store.getters.getApiProcessingDetailAppStatus
      "
    >
      <v-progress-circular
        size="64"
        indeterminate
        color="primary"
      ></v-progress-circular>
    </div>
  </v-app>
</template>

<script setup>
import Header from '@/components/admin/partials/Header/Header.vue';
import Sidebar from '@/components/admin/partials/Sidebar/Sidebar.vue';
import './Layout.scss';
import ActionAlertCard from '@/components/ui/ActionAlertCard.vue';
import { computed, onMounted, watch } from 'vue';
import { useStore } from 'vuex';
const store = useStore();

const getStatusAlert = computed(() => store.getters.getStatusAlert);
const getStatusAlertMessage = computed(
  () => store.getters.getStatusAlertMessage
);

onMounted(() => {
  store.dispatch('updateStatusAlert', {
    showStatusAlert: false,
    statusAlertMessage: '',
  });
});

watch(getStatusAlert, (newValue) => {
  if (newValue) {
    setTimeout(() => {
      store.dispatch('updateStatusAlert', {
        showStatusAlert: false,
        statusAlertMessage: '',
      });
    }, 5000);
  }
});

</script>

<style src="./Layout.scss" lang="scss" />

<template>
  <div class="font-Noto-Sans corporate-page">
    <v-sheet color="transparent">
      <PageTitle
        v-show="!loading && !$store.getters.getApiProcessingStatus"
        :items="{
          title: '契約管理',
          subTitle: 'テンプレート管理',
          buttons: [
            {
              title: '新規作成',
              icon: 'mdi-plus-circle',
              action: this.goToCreate,
              others: null,
              class: [],
            },
          ],
        }"
      ></PageTitle>
      <v-row>
        <v-col cols="12" md="12" class="d-flex w-100">
          <DataTable
            v-show="!loading && !$store.getters.getApiProcessingStatus"
            :items="getAllContractTemplates"
            :headers="headers"
            :total-records="totalRecords"
            :number-of-pages="totalPages"
            @update:options="updateTable"
            ref="pagination"
            @row-clicked="
              $router.push({
                name: 'EditContractTemplate',
                params: { id: $event.id },
              })
            "
          >
            <template v-slot:item.created_at="{ item }">
              <div class="fw-400">
                {{ item.created_at ? dateFormat(item.created_at) : '' }}
              </div>
            </template>
            <template v-slot:item.creator="{ item }">
              <div class="fw-400">
                {{ item.creator?.name }}
              </div>
            </template>
          </DataTable>
        </v-col>
      </v-row>
    </v-sheet>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue';
import { useStore } from 'vuex';
import DataTable from '@/components/ui/DataTable.vue';
import moment from 'moment';
import { useRouter } from 'vue-router';

export default {
  name: 'ContractTemplateListing',
  components: { DataTable },
  metaInfo: {
    title: 'コトナル 管理者 企業管理 | 企業一覧',
  },
  setup() {
    const router = useRouter();
    const store = useStore();
    const dateFormat = (date) => {
      return date ? moment(date).format('YYYY/MM/DD') : '---- / - / -';
    };

    const showActive = ref(1);
    const initialLoad = ref(true);
    const loading = ref(false);
    const userSearchedInput = ref('');
    const headers = [
      {
        title: '企業名',
        sortable: false,
        value: 'company_name',
      },
      {
        title: 'テンプレート名',
        align: 'left',
        sortable: false,
        value: 'contract_template_name',
      },
      {
        title: '時給',
        align: 'left',
        sortable: false,
        value: 'wage',
      },
      {
        title: '作成者',
        align: 'left',
        sortable: false,
        value: 'creator',
      },
      {
        title: '作成日',
        value: 'created_at',
      },
    ];

    const totalRecords = computed(() => 0);
    const totalPages = computed(() => 0);
    const getAllContractTemplates = computed(
      () => store.getters.getAllContractTemplates
    );

    const resetPagination = () => {
      if (paginationRef.value) {
        paginationRef.value.currentPage = 1;
        ++paginationRef.value.updatePaginate;
      }
    };

    const goToCreate = () => {
      router.push({ name: 'CreateContractTemplate' });
    };

    const sort_by_order = ref('desc');
    const sort_by = ref('created_at');
    const getDataFromApi = async (e = undefined, obj = {}) => {
      loading.value = true;
      let data = {
        // need to adjust sorting
        sort_by_order: sort_by_order.value,
        sort_by: sort_by.value,
        page: typeof e === 'number' ? e : (e?.page ?? 1),
        paginate: e?.itemsPerPage || 25,
        showActive: showActive.value,
      };

      data = Object.assign({}, obj, data);
      if (data.search === '') {
        delete data.search;
      }

      await store
        .dispatch('GET_ALL_CONTRACT_TEMPLATES', data)
        .then(() => {
          if (initialLoad.value) initialLoad.value = false;
        })
        .finally(() => {
          loading.value = false;
        });
    };

    const updateTable = (e) => {
      getDataFromApi(e);
    };

    const handleRowClick = (event) => {
      router.push({
        name: 'EditContractTemplate',
        params: { id: event.id },
      });
    };

    onMounted(() => {
      getDataFromApi();
    });

    return {
      headers,
      initialLoad,
      loading,
      userSearchedInput,
      showActive,
      totalRecords,
      totalPages,
      getAllContractTemplates,
      goToCreate,
      getDataFromApi,
      updateTable,
      handleRowClick,
      dateFormat,
    };
  },
};
</script>

<style scoped src="../contract/style.scss" lang="scss" />

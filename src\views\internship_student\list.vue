<template>
  <div class="">
    <PageTitle
      v-show="!loading && !$store.getters.getApiProcessingStatus"
      :items="{
        title: 'インターン生管理',
        subTitle: '一覧',
        icon: 'QuestionIcon',
        iconAction: iconAction,
        tabs: [
          {
            title: '稼働中',
            count: getInternshipStudentCounts.active_count
              ? getInternshipStudentCounts.active_count
              : 0,
            action: () => resultOnTab(0),
          },
          {
            title: '停止中',
            count: getInternshipStudentCounts.stopped_count
              ? getInternshipStudentCounts.stopped_count
              : 0,
            action: () => resultOnTab(1),
          },
        ],
        buttons: [
          {
            title: 'CSVエクスポート',
            class: 'bg-white text-ff862f',
            color: 'text-ff862f',
            variant: 'outlined',
            action: () => this.downloadCsv(),
          },
          {
            title: '詳細条件検索',
            variant: 'outlined',
            class: 'bg-white',
            action: () => {
              toggleSearch = true;
            },
          },
        ],
      }"
    ></PageTitle>
    <v-fade-transition>
      <SearchArea
        class="mb-4"
        v-if="toggleSearch"
        v-bind="{ toggleSearch, selectTypeOptions, searchFields }"
        @toggleSearch="updateSearchResults"
        @changedInputType="changedInputType"
        @searchSubmit="searchSubmit"
      ></SearchArea>
    </v-fade-transition>
    <DataTable
      v-show="!loading && !$store.getters.getApiProcessingStatus"
      :items="items"
      :headers="getHeaders"
      :total-records="
        getInternshipStudentPagination
          ? getInternshipStudentPagination.records_total
          : 0
      "
      :number-of-pages="
        getInternshipStudentPagination
          ? getInternshipStudentPagination.total_pages
          : 0
      "
      @update:options="updateTable"
      :page="configuration.page"
      ref="pagination"
      :customHeight="'height: 69px'"
    >
      <template v-slot:serial_number="{ item, index }">
        <!-- Calculate the correct index -->
      </template>
      <!-- company -->
      <template v-slot:item.company_id="{ item }">
        <div class="font-12px fw-400">
          {{ item?.company?.internal_company_id }}
        </div>
        <div class="mouse-pointer">
          <router-link
            v-if="item?.company?.id"
            target="_blank"
            class="text-3f74c2 text-decoration-none truncate-lines lines-1"
            :to="{ name: 'CorporateDetails', params: { id: item?.company?.id } }"
          >
            <v-tooltip :text="item?.company?.name" location="top" color="white">
              <template #activator="{ props }">
                <span v-bind="props">{{ item?.company?.name }}</span>
              </template>
            </v-tooltip>
          </router-link>
        </div>
      </template>

      <!-- student -->
      <template v-slot:item.student_id="{ item }">
        <div class="font-12px fw-400">
          {{ item?.student?.student_internal_id }}
        </div>
        <div class="mouse-pointer">
          <router-link
            v-if="item?.student?.id"
            target="_blank"
            class="text-3f74c2 text-decoration-none truncate-lines lines-1"
            :to="{ name: 'StudentProfile', params: { id: item?.student?.id } }"
          >
            <v-tooltip
              :text="`${item.student.family_name} ${item.student.first_name}`"
              location="top"
              color="white"
            >
              <template #activator="{ props }">
                <span v-bind="props">
                  {{ item.student.family_name }} {{ item.student.first_name }}
                </span>
              </template>
            </v-tooltip>
          </router-link>
          <div v-else>存在しない</div>
        </div>
      </template>

      <template v-slot:item.student_name="{ item }">
        <div v-if="item?.student_name">
          <div class="font-12px fw-400">
            {{ item.student_name }}
          </div>
        </div>
        <div v-else>存在しない</div>
      </template>
      <template v-slot:item.university="{ item }">
        <div v-if="item?.student?.id">
          <div class="font-12px fw-400">
            {{ item.student_university }}
          </div>
          <div class="font-12px truncate-lines lines-1">
            {{ item.student.email_valid }}
          </div>
        </div>
      </template>

      <template v-slot:item.application_details="{ item }">
        <div
          v-if="item.student"
          class="position-relative pl-5"
          @click.stop="showDetailApp(item)"
        >
          <v-icon size="18px">$FeedIcon</v-icon>
        </div>
      </template>
      <template v-slot:item.internship_student_status="{ item }">
        <div class="font-12px fw-400">
          {{ item?.internship_student_status?.updated_at ? dateFormat(item?.internship_student_status?.updated_at) : '' }}
        </div>
      </template>
      <template v-slot:item.date_application_passed="{ item }">
        <div class="font-12px fw-400">
          {{ item.date_application_passed ? dateFormat(item.date_application_passed) : '' }}
        </div>
      </template>
      <template v-slot:item.incremental_id="{ index }">
        <div class="font-12px fw-400">
          {{ index + 1 }}
        </div>
      </template>

      <!-- status -->

      <template v-slot:[`item.status`]="{ item }">
        <v-sheet color="transparent" class="d-flex align-center justify-center">
          <v-menu :close-on-click="true" offset-y activator="parent">
            <!-- Activator slot for the menu -->
            <template v-slot:activator="{ props }">
              <v-chip
                :color="getStatusColor(item?.status)"
                variant="flat"
                dense
                size="small"
                v-bind="props"
              >
                <div class="d-flex align-center justify-space-between">
                  <div class="text-truncate white--text font-12px">
                    {{ getStatusText(item?.status) }}
                  </div>
                  <v-icon size="18px" color="white">mdi-chevron-down</v-icon>
                </div>
              </v-chip>
            </template>

            <!-- Menu content -->
            <v-list>
              <v-list-item
                class="mouse-pointer font-12px fw-400"
                v-for="(option, index) in statusOptions"
                :key="index"
                @click="statusChange(item, option)"
              >
                <v-list-item-title>{{
                  getStatusText(option)
                }}</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </v-sheet>
      </template>

      <!-- feedback -->
      <template v-slot:item.feedback="{ item }">
        <span class="cursor-pointer">
          <router-link
            v-if="item.student_id"
            target="_blank"
            class="text-3f74c2 text-decoration-none truncate-lines lines-1"
            :to="{
              name: 'FeedbackStudent',
              params: {
                id: item.student_id,
              },
            }"
          >
            <v-icon size="18px">$FeedbackIcon</v-icon>
          </router-link>
          <div v-else>存在しない</div>
        </span>
      </template>
    </DataTable>
  </div>
  <ApplicationDetailDialog
    text="応募内容"
    :dialog="dialog.isShowDetailApp"
    :application="application"
    @submitSuccess="dialog.isShowDetailApp = false"
    @closeModel="dialog.isShowDetailApp = false"
  ></ApplicationDetailDialog>
  <SimpleModel
    :text="errorMessages"
    :dialog="dialog.errorDialog"
    :showCloseIcon="true"
    @closeModel="dialog.errorDialog = false"
    :buttonOption="{
      hideCancel: true,
      hideSubmit: true,
    }"
  ></SimpleModel>
</template>
<script>
import {
  ref,
  computed,
  onMounted,
  onBeforeUnmount,
  defineAsyncComponent,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
const DataTable = defineAsyncComponent(
  () => import('@/components/ui/DataTable.vue')
);
const SearchArea = defineAsyncComponent(
  () => import('@/components/ui/SearchArea.vue')
);
const ApplicationDetailDialog = defineAsyncComponent(
  () => import('@/components/models/ApplicationDetailDialog.vue')
);
const SimpleModel = defineAsyncComponent(
  () => import('@/components/models/SimpleModel.vue')
);
import moment from 'moment';
import Encoding from 'encoding-japanese';

import { useStore } from 'vuex';

export default {
  components: { DataTable, SearchArea, ApplicationDetailDialog, SimpleModel },

  setup() {
    const store = useStore();
    const route = useRoute();
    const router = useRouter();
    const selection = ref(1); // Default to active status (1: 稼働中)
    const toggleSearch = ref(false);
    const statusOptions = ref([1, 2, 3]); // 1: 稼働中, 2: 停止中, 3: 完了
    const items = ref([]);
    const searchFields = ref([]);
    const activeStatus = ref(1);
    const setTimeOutRef = ref(null);
    const searchQuery = ref({});
    const loading = ref(false);
    const dialog = ref({ isShowDetailApp: false, errorDialog: false });
    const errorMessages = ref(null);
    const application = ref({});

    const showDetailApp = (item) => {
      application.value = item.application;
      dialog.value.isShowDetailApp = true;
    };

    const dateFormat = (date) => {
      return date ? moment(date).format('YYYY/MM/DD') : '---- / - / -';
    };

    const selectTypeOptions = ref([
      {
        id: 'keyword_search',
        name: 'キーワード検索',
      },
      {
        id: 'created_at',
        name: '雇用期間',
      },
    ]);

    const configuration = ref({
      page: 1,
      sort_by: 'updated_at',
      sort_by_order: 'desc',
      paginate: 25,
      status: 1, // Default to active status
    });

    const getAllInternshipStudent = computed(
      () => store.getters.getAllInternshipStudent
    );
    const getInternshipStudentPagination = computed(
      () => store.getters.getInternshipStudentPagination
    );
    const getInternshipStudentCsvData = computed(
      () => store.getters.getInternshipStudentCsvData
    );
    const getInternshipStudentCounts = computed(
      () => store.getters.getInternshipStudentCounts
    );

    const getHeaders = computed(() => [
      {
        title: 'No.',
        value: 'serial_number',
        align: 'center',
        width: '3%',
        sortable: false,
      },
      {
        title: '内部ID',
        subTitle: '企業名',
        align: 'left',
        class: ['py-3', 'px-0'],
        value: 'company_id',
        sortable: false,
        flex: '1 1 20%',
      },
      {
        title: '学生ID',
        subTitle: '学生名',
        class: ['py-3', 'px-0'],
        value: 'student_id',
        align: 'left',
        sortable: false,
        flex: '1 1 30%',
      },
      {
        title: '大学名',
        subTitle: '学生メールアドレス',
        value: 'university',
        class: ['py-3', 'px-0'],
        align: 'left',
        sortable: false,
        flex: '1 1 15%',
      },
      {
        title: '応募内容',
        value: 'application_details',
        class: ['py-3', 'px-0'],
        align: 'left',
        sortable: false,
        flex: '1 1 15%',
      },
      {
        title: 'フィードバック履歴',
        value: 'feedback',
        class: ['py-3', 'px-0'],
        align: 'center',
        sortable: false,
        flex: '1 1 15%',
      },
      {
        title: '合格日',
        sortable: false,
        align: 'left',
        class: ['py-3', 'px-0'],
        value: 'date_application_passed',
        flex: '1 1 15%',
      },
      {
        title: 'ステータス更新日',
        sortable: false,
        align: 'left',
        class: ['py-3', 'px-0'],
        value: 'internship_student_status',
        flex: '1 1 15%',
      },
      {
        title: 'ステータス',
        sortable: false,
        align: 'left',
        class: ['py-3', 'px-0'],
        value: 'status',
        flex: '1 1 15%',
      },
    ]);

    const changedInputType = (inputSearchType) => {
      if (inputSearchType === 'keyword_search') {
        searchFields.value = [
          {
            label: 'Label',
            name: 'search',
            type: 'text',
            value: '',
            placeholder: '企業名、学生名',
          },
        ];
      } else if (inputSearchType === 'created_at') {
        searchFields.value = [
          {
            label: 'Label',
            name: 'date_from',
            type: 'date',
            rules: 'required',
            show_after_approx: true,
            value: moment().format('YYYY-MM-DD'),
            menu: false,
            locale: 'ja',
            date_format: 'YYYY-MM-DD',
          },
          {
            label: 'Label',
            name: 'date_to',
            type: 'date',
            rules: 'required',
            show_after_approx: false,
            value: moment().format('YYYY-MM-DD'),
            menu: false,
            locale: 'ja',
            range: true,
            range_input: 'date_from',
            date_format: 'YYYY-MM-DD',
          },
        ];
      } else {
        searchFields.value = [];
      }
    };

    /**
     * Cleans up timeout reference to prevent memory leaks
     */
    const removeTimeoutRef = () => {
      if (setTimeOutRef.value) {
        clearTimeout(setTimeOutRef.value);
        setTimeOutRef.value = null;
      }
    };

    /**
     * Resets search parameters and refreshes data
     * Called when search panel is closed
     */
    const updateSearchResults = async () => {
      toggleSearch.value = false;
      configuration.value.page = 1;
      configuration.value.search = null;
      configuration.value.date_from = null;
      configuration.value.date_to = null;
      await generateItems();
      resetPagination();
    };

    /**
     * Handles tab switching between active and stopped interns
     * @param {number} tab - 0 for active (status=1), 1 for stopped (status=2)
     */
    const resultOnTab = async (tab) => {
      configuration.page = 1;
      selection.value = tab === 0 ? 1 : 2; // Convert tab index to status value
      await generateItems();
      resetPagination();
    };

    /**
     * Updates intern's status
     * @param {Object} item - Intern data object
     * @param {number} option - New status (1: 稼働中, 2: 停止中, 3: 完了)
     */
    const statusChange = async (item, option) => {
      if (item?.status === option) return false;
      await updateApi(item, option);
    };

    /**
     * Makes API call to update intern's status
     * @param {Object} item - Intern data object
     * @param {number} option - New status value
     */
    const updateApi = async (item, option) => {
      let params = { id: item.id, status: option };
      await store.dispatch('INTERNSHIP_STUDENT_UPDATE', params);
      await generateItems();
    };

    /**
     * Handles table sorting and pagination updates
     * @param {Object} e - Event object containing sort and page information
     */
    const updateTable = async (e) => {
      // Update sort field
      configuration.value.sort_by =
        e && e.sortBy && e.sortBy.length && e?.sortBy[0]?.key
          ? e?.sortBy[0]?.key
          : 'updated_at';

      // Update sort direction
      configuration.value.sort_by_order =
        e && e.sortBy && e.sortBy.length && e?.sortBy[0]?.order
          ? e?.sortBy[0]?.order
          : 'desc';

      configuration.value.page = e.page || 1;
      await generateItems();
    };

    /**
     * Handles CSV export functionality
     * Downloads intern data as CSV file or shows error if no data
     */
    const downloadCsv = async () => {
      await store.dispatch('INTERNSHIP_STUDENT_EXPORT_CSV');

      // Show error if no data available
      if (store.getters.getInternshipStudentCsvData?.message) {
        dialog.value.errorDialog = true;
        errorMessages.value =
          '<div class="pt-10">データが見つかりません。</div>'; // No data found
        return;
      }

      // Convert UTF-8 CSV data to Shift-JIS
      const utf8Array = Encoding.stringToCode(
        store.getters.getInternshipStudentCsvData.csv
      );
      const sjisArray = Encoding.convert(utf8Array, {
        to: 'SJIS',
        from: 'UNICODE',
      });
      const uint8Array = new Uint8Array(sjisArray);
      const blob = new Blob([uint8Array], { type: 'text/csv' });

      // Create and trigger CSV download
      const fileUrl = window.URL.createObjectURL(blob);
      const fileLink = document.createElement('a');
      fileLink.href = fileUrl;
      fileLink.setAttribute(
        'download',
        `インターン生管理_${new Date().toISOString().slice(0, 10)}.csv` // internship_student.csv
      );
      document.body.appendChild(fileLink);
      fileLink.click();
      document.body.removeChild(fileLink);
    };

    /**
     * Returns color code based on intern's status
     * @param {number} status - 1: 稼働中 (blue), 2: 停止中 (gray), 3: 完了 (green)
     */
    const getStatusColor = (status) => {
      switch (status) {
        case 1: return '#60D1CB'; // 稼働中 - blue/teal
        case 2: return '#A7A7A7'; // 停止中 - gray
        case 3: return '#4CAF50'; // 完了 - green
        default: return '#A7A7A7'; // default gray
      }
    };

    /**
     * Returns status text in Japanese
     * @param {number} status - 1: 稼働中, 2: 停止中, 3: 完了
     */
    const getStatusText = (status) => {
      switch (status) {
        case 1: return '稼働中';
        case 2: return '停止中';
        case 3: return '完了';
        default: return '不明';
      }
    };

    /**
     * Fetches and updates intern list based on current configuration
     * Handles loading state and parameter preparation
     */
    const generateItems = async () => {
      loading.value = true;
      items.value = [];
      configuration.value.status = selection.value;

      // Prepare API parameters
      const params = {
        status: selection.value,
        page: configuration.value.page,
        paginate: configuration.value.paginate,
        sort_by: configuration.value.sort_by,
        sort_by_order: configuration.value.sort_by_order,
      };

      // Add search parameters if present
      if (configuration.value.search)
        params.search = configuration.value.search;
      if (configuration.value.date_from) {
        params.date_from = configuration.value.date_from;
        params.date_to = configuration.value.date_to;
        params.search = '';
      }

      const response = await store.dispatch(
        'INTERNSHIP_STUDENT_GET_ALL',
        params
      );
      items.value = response.data.data;
      loading.value = false;
    };

    /**
     * Resets pagination to first page
     */
    const resetPagination = () => {
      configuration.value.page = 1;
    };

    /**
     * Processes search form submission
     * Updates configuration with search parameters
     * @param {Object} $event - Form submission event with field values
     */
    const searchSubmit = async ($event) => {
      let obj = {};
      if ($event.fields.length > 0) {
        $event.fields.forEach((field) => {
          obj[field.name] = field.value;
        });
      }
      configuration.value = {
        ...configuration.value,
        ...obj,
      };
      generateItems();
    };

    /**
     * Opens help documentation in new window
     * Uses URL from store's information URLs
     */
    const iconAction = () => {
      const url = store.getters.getInformationURL[3].url;
      window.open(url, '_blank');
    };

    /**
     * Component initialization
     * Sets up initial search type
     */
    onMounted(() => {
      changedInputType('keyword_search');
    });

    /**
     * Cleanup on component unmount
     * Removes any pending timeouts
     */
    onBeforeUnmount(() => {
      removeTimeoutRef();
    });

    return {
      selection,
      toggleSearch,
      statusOptions,
      items,
      searchFields,
      activeStatus,
      selectTypeOptions,
      configuration,
      searchQuery,
      getHeaders,
      changedInputType,
      removeTimeoutRef,
      updateSearchResults,
      resultOnTab,
      statusChange,
      updateApi,
      updateTable,
      downloadCsv,
      generateItems,
      resetPagination,
      searchSubmit,
      dateFormat,
      getAllInternshipStudent,
      getInternshipStudentPagination,
      getInternshipStudentCounts,
      router,
      route,
      getStatusText,
      getStatusColor,
      loading,
      showDetailApp,
      dialog,
      application,
      iconAction,
      errorMessages,
    };
  },
};
</script>
<style lang="scss" scoped>
.pill {
  width: 77px;
  height: 30px;
  border-radius: 30px;
}

.pill-large {
  width: 96px;
  height: 30px;
  border-radius: 30px;
}

.application-table {
  :deep(thead th) {
    padding-left: 16px !important;
    padding-right: 16px !important;
  }

  :deep(tbody tr td) {
    padding-top: 15px !important;
    padding-bottom: 15px !important;

    &:nth-child(2) {
      padding-left: 0 !important;
      padding-right: 0 !important;
    }
  }
}
</style>

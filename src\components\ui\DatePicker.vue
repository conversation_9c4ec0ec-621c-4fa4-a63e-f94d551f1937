<template>
  <div class="text-white position-absolute"></div>
  <v-menu
    v-model="menu"
    :close-on-content-click="false"
    transition="scale-transition"
    offset-y
  >
    <template v-slot:activator="{ props }">
      <v-text-field
        style="min-width: 160px"
        v-bind="props"
        v-model="field.value"
        :value="changeFormat(field.value)"
        :placeholder="field.placeholder"
        :class="field.class"
        readonly
        :disabled="disabled"
        density="compact"
        variant="outlined"
        class="input-text"
        :label="null"
        @click:prepend="openMenu"
        @blur="onBlur"
        :error-messages="errors"
        :error="!!errors.length"
        :hide-details="!isShowMessage || errors.length <= 0"
        clearable
        @click:clear="
          field.value = '';
          date = '';
        "
      ></v-text-field>
    </template>
    <v-date-picker
      v-model="date"
      :view-mode="pickerType"
      :max="parsedMaxDate"
      :min="parsedMinDate"
      locale="ja"
      @update:model-value="onDateSelect"
      @update:year="selectorChangeYear"
      @update:month="selectorChangeMonth"
      ><template v-slot:header="{ props }">
        <div class="date-selector">{{ formattedHeader }}</div>
      </template>
    </v-date-picker>
  </v-menu>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue';
import moment from 'moment';

export default {
  name: 'DatePicker',
  inheritAttrs: false,
  props: {
    modelValue: {
      type: String,
      default: '',
    },
    field: {
      type: Object,
      default: () => ({
        menu: false,
        locale: 'ja',
        value: '',
        placeholder: '',
      }),
    },
    separator: {
      type: String,
      default: '/',
    },
    max: {
      type: String,
      default: '',
    },
    min: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    pickerType: {
      type: String,
      default: 'month', // Can be 'date', 'month', or 'year'
    },
    errors: {
      type: Array,
      default: () => [],
    },
    name: {
      type: String,
      default: '',
    },
    isShowMessage: {
      type: Boolean,
      default: true,
    },
  },
  emits: ['update:modelValue', 'blur', 'input', 'change'],
  setup(props, { emit }) {
    const date = ref(new Date());
    const menu = ref(false);

    const formattedHeader = computed(() => {
      return moment(date.value).format('YYYY年M月'); // Format as YYYY年M月
    });

    // Update displayed month
    const selectorChangeMonth = (month) => {
      date.value = moment(date.value)
        .month(month) // Change month
        .format('YYYY-MM-DD');
    };

    // Update displayed year
    const selectorChangeYear = (year) => {
      date.value = moment(date.value)
        .year(year) // Change year
        .format('YYYY-MM-DD');
    };

    const parsedMaxDate = computed(() =>
      props.max ? new Date(props.max) : null
    );
    const parsedMinDate = computed(() =>
      props.min ? new Date(props.min) : null
    );

    const computedFieldProps = (fieldProps, attrs) => {
      return {
        ...fieldProps,
        ...attrs,
      };
    };

    watch(
      () => props.field.value,
      (newValue) => {
        date.value = newValue ? new Date(newValue) : new Date();
      },
      { immediate: true }
    );

    const onDateSelect = (selectedDate) => {
      if (selectedDate) {
        menu.value = false;
        const formattedDate = formatDate(selectedDate);
        date.value = new Date(selectedDate);
        props.field.value = formattedDate;
        emit('update:modelValue', formattedDate);
      }
    };

    const openMenu = () => {
      menu.value = true;
    };

    const formatDate = (date) => {
      if (!date || date === '') return null;

      const format =
        props.pickerType === 'months'
          ? `YYYY${props.separator}MM`
          : `YYYY${props.separator}MM${props.separator}DD`;

      if (props.field?.date_format) {
        return moment(date)
          .locale(props.field.locale ?? 'ja')
          .format(props.field.date_format);
      } else {
        return moment(date).format(format);
      }
    };

    // parseDate function
    const parseDate = (date) => {
      if (!date || date === '') return null;

      // Check if the date format contains slashes and replace them
      const formattedDate = date.replace(/\//g, '-');

      // Check if it's a valid date before formatting
      if (!moment(formattedDate, moment.ISO_8601, true).isValid()) {
        console.warn(`Invalid date format: ${date}`);
        return null;
      }

      const format =
        props.pickerType === 'months'
          ? `YYYY${props.separator}MM`
          : `YYYY${props.separator}MM${props.separator}DD`;

      return moment(formattedDate).format(format);
    };

    // changeDayFormat function
    const changeFormat = (date) => {
      return date ? moment(date).format(
        props.pickerType === 'months' ? 'YYYY/MM' : 'YYYY/MM/DD'
      ) : '';
    };

    const onBlur = () => {
      props.field.value = parseDate(props.field.value);
      if (!props.field.value) {
        date.value = moment().format(
          props.pickerType === 'months' ? 'YYYY-MM' : 'YYYY-MM-DD'
        );
      }
    };

    onMounted(() => {
      if (!props.field.value) {
        date.value = new Date().toISOString().slice(0, 10);
      }
    });

    return {
      date,
      menu,
      parsedMaxDate,
      parsedMinDate,
      onDateSelect,
      selectorChangeMonth,
      selectorChangeYear,
      openMenu,
      computedFieldProps,
      onBlur,
      formattedHeader,
      changeFormat,
    };
  },
};
</script>

<style>
.v-picker-title {
  display: none !important;
}
.v-date-picker-header {
  display: none !important;
}
.searchable-box {
  background-color: white;
}

.font-14px {
  font-size: 14px;
}

.text-333 {
  color: #333;
}

.v-btn__underlay .v-btn__content {
  color: white !important;
}

.v-date-picker-controls > .v-btn:first-child {
  color: white !important;
}

.date-selector {
  position: absolute;
  margin-top: 16px;
  margin-left: 20px;
  z-index: 9999;
  pointer-events: none;
}
</style>

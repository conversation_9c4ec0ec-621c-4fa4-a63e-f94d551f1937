<template>
  <v-dialog :persistent="true" v-model="getLaunch" max-width="775px">
    <v-card :elevation="0" color="white" width="775px">
      <v-card-title class="d-flex justify-end pr-9 pb-2 pt-8">
        <v-btn @click="closeDialog" variant="text" icon class="pa-0">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-card-title
        v-if="edit"
        class="d-flex justify-center text-center pt-0 font-20px mb-8 font"
      >
        <div v-if="item.status === 3 && item.type_format === 1" class="text-center line-height-12 break">
          <div>契約フォーマットを、コトナル推奨の労働条件通知書</div>
          <div>（KNフォーマット）に変更しますか？</div>
          <div>変更する場合は、新しいレコードを生成します。</div>
          <div>このまま労働条件通知書を作成し学生に送付してください。</div>
        </div>

        <div v-if="item?.type_format === 2">独自フォーマット　管理項目</div>

        <div class="text-center line-height-12" v-if="item?.type_format === 1 && item.status !== 3">
          <div>契約フォーマットを独自に変更する場合は、当初雇用開始日と</div>
          <div>最新の雇用終了日（もしくは、最新の契約終了日）を</div>
          <div>入力してください。</div>
        </div>
      </v-card-title>
      <div class="d-flex flex-column align-center justify-space-between">
        <Form @submit="submitForm">
          <div v-if="item.status === 3 && item.type_format === 1"></div>
          <div v-else class="d-flex flex-column align-center justify-center">
            <div class="input-width">
              <label class="d-block font-14px mb-1">
                <span>当初雇用開始日</span>
                <span class="error--text ml-2">必須</span>
              </label>
              <Field
                name="date_employment_start"
                rules="required"
                v-slot="{ field, errors }"
                :value="fields.date_employment_end.value"
              >
                <DatePicker
                  v-bind="field"
                  :field="fields.date_employment_start"
                  v-model="fields.date_employment_start.value"
                  :error-messages="errors"
                  :error="errors.length > 0"
                  :hide-details="errors.length <= 0"
                  :separator="'/'"
                />
              </Field>
            </div>

            <div class="input-width mt-4">
              <label class="d-block font-14px mb-1">
                <span>最新雇用終了日</span>
                <span class="error--text ml-2">必須</span>
              </label>
              <Field
                name="date_employment_end"
                rules="required"
                v-slot="{ field, errors }"
                :value="fields.date_employment_end.value"
              >
                <DatePicker
                  v-bind="field"
                  :field="fields.date_employment_end"
                  v-model="fields.date_employment_end.value"
                  :error-messages="errors"
                  :error="errors.length > 0"
                  :hide-details="errors.length <= 0"
                  :min="minEndDate"
                  :separator="'/'"
                />
              </Field>
            </div>
          </div>
          <v-card-actions v-if="edit" class="d-flex justify-center pt-7 pb-16">
            <v-btn
              variant="outlined"
              color="primary"
              width="188px"
              height="35px"
              @click="closeDialog"
            >
              戻る
            </v-btn>
            <v-btn
              variant="elevated"
              color="primary"
              class="ml-5"
              width="188px"
              height="35px"
              type="submit"
            >
              {{ item?.status !== 1 ? '変更する' : '変更' }}
            </v-btn>
          </v-card-actions>
        </Form>
      </div>
    </v-card>
  </v-dialog>
</template>

<script>
import { ref, computed, watch } from 'vue';
import { Field, Form } from 'vee-validate';
import DatePicker from '@/components/ui/DatePicker.vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import moment from 'moment';

export default {
  name: 'ContractEdit',
  components: { DatePicker, Field, Form },
  props: {
    edit: {
      type: Boolean,
      default: true,
    },
    launch: {
      type: Boolean,
      required: true,
    },
    item: {
      type: Object,
      default: null,
    },
  },
  /**
   * Component Setup
   * Handles contract creation and editing functionality
   * @param {Object} props - Component props containing dialog state and contract data
   * @param {Function} emit - Event emitter for parent communication
   */
  setup(props, { emit }) {
    const store = useStore();
    const router = useRouter();

    /**
     * Form Fields Configuration
     * Manages employment contract dates and status
     */
    const fields = ref({
      date_employment_start: {
        name: 'date_employment_start', // Initial employment start date
        menu: false, // Date picker menu state
        value: '', // Selected date value
        date: '', // Date object
        locale: 'ja', // Japanese locale for date formatting
      },
      date_employment_end: {
        name: 'date_employment_end', // Latest employment end date
        menu: false,
        value: '',
        date: '',
        locale: 'ja',
      },
      wage: 0, // Contract wage amount
      status: null, // Contract status
    });

    /**
     * Dialog Control
     * Two-way binding for dialog visibility
     */
    const getLaunch = computed({
      get() {
        return props.launch;
      },
      set(value) {
        emit('update:launch', value);
      },
    });

    /**
     * Form Data Watcher
     * Updates form fields when dialog opens or contract data changes
     */
    watch(
      () => props.launch,
      (val) => {
        if (val && props.item) {
          // Format and set employment dates when editing existing contract
          fields.value.date_employment_start.value = props.item
            .date_employment_start
            ? moment(props.item.date_employment_start).format('YYYY/MM/DD')
            : '';
          fields.value.date_employment_end.value = props.item
            .date_employment_end
            ? moment(props.item.date_employment_end).format('YYYY/MM/DD')
            : '';
        } else {
          // Reset dates for new contract
          fields.value.date_employment_start.value = '';
          fields.value.date_employment_end.value = '';
        }
      }
    );

    /**
     * Date Constraints
     * Computed properties for date picker min values
     */
    // Minimum start date is yesterday
    const minStartDate = computed(() =>
      moment().subtract(1, 'day').format('YYYY-MM-DD')
    );
    // Minimum end date is start date or today
    const minEndDate = computed(() => {
      return fields.value.date_employment_start.value
        ? moment(fields.value.date_employment_start.value, 'YYYY/MM/DD').format(
            'YYYY-MM-DD'
          )
        : moment().format('YYYY-MM-DD');
    });

    /**
     * Dialog Actions
     */
    // Closes dialog and notifies parent
    const closeDialog = () => {
      getLaunch.value = false;
      emit('cancelUpdate', props.item);
    };

    /**
     * Form Submission Handlers
     */
    // Main form submission handler
    const submitForm = async (values) => {
      try {
        if (props.edit) {
          await updateContract(values); // Update existing contract
          if (props.item.status === 3 && props.item.type_format === 1) {
            closeDialog();
            return
          } 
        } else {
          await store.dispatch('CONTRACT_CREATE', values); // Create new contract
        }
        emit('refresh'); // Notify parent to refresh data
        closeDialog();
      } catch (error) {
        console.error(error);
      }
    };

    /**
     * Contract Update Handler
     * Processes contract updates with formatted dates
     */
    const updateContract = async (values) => {
      const params = {
        id: props.item.id,
        date_employment_start: moment(
          fields.value.date_employment_start.value
        ).format('YYYY-MM-DD'),
        date_employment_end: moment(
          fields.value.date_employment_end.value
        ).format('YYYY-MM-DD'),
        status: props.item.status,
        type_format: props.item?.type_format,
      };
      try {
        values.id = props.item.id;
        if (props.item.type_format == 2) {
          await store.dispatch('CONTRACT_UPDATE', params);
          return
        }
        if (props.item.type_format == 1) {
          params.status = 1
        }
        const response = await store.dispatch('CONTRACT_UPDATE', params);
        if (props.item.status == 3) router.push(`contract-detail/${response?.data?.contract?.id}`);
      } catch (error) {
        store.dispatch('updateStatusAlert', {
          showStatusAlert: true,
          statusAlertMessage: '最大契約数に達したので、変更できません。',
        });
      }
    };

    // Expose necessary properties and methods
    return {
      fields,
      getLaunch,
      minStartDate,
      minEndDate,
      closeDialog,
      submitForm,
    };
  },
};
</script>

<style lang="scss">
.input-width {
  width: 543px;
}
</style>

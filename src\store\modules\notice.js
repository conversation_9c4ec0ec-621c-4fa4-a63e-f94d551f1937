import axios from '@/services/api';

function initialState() {
  return {
    status: '',
    notices: [],
    singleNotice: {},
    noticePagination: 0,
  };
}

const state = initialState();

const getters = {
  getNoticesStatus: (state) => state.status,
  getAllNotices: (state) => state.notices,
  getSingleNotice: (state) => state.singleNotice,
  getNoticeCounts: (state) => state.noticeCounts,
  getNoticePagination: (state) => state.noticePagination,
};

const actions = {
  ['NOTICE_GET_ALL']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('NOTICE_REQUEST');
      axios
        .get(`/admin/notice`, { params })
        .then((response) => {
          let data = response.data.data;
          commit('NOTICE_GET_ALL_SUCCESS', { data });

          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['NOTICE_GET']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('NOTICE_REQUEST');
      axios
        .get(`/admin/notice/${params.id}`)
        .then((response) => {
          let data = response.data;
          commit('NOTICE_GET_SUCCESS', { data });

          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['NOTICE_CREATE']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('NOTICE_REQUEST');
      axios
        .post(`/admin/notice`, params)
        .then((response) => {
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['NOTICE_UPDATE']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('NOTICE_REQUEST');
      axios
        .put(`/admin/notice/${params.id}`, params)
        .then((response) => {
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['NOTICE_DELETE']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('NOTICE_REQUEST');
      axios
        .delete(`/admin/notice`, params)
        .then((response) => {
          commit('NOTICE_DELETE_SUCCESS');
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },
};

const mutations = {
  ['NOTICE_REQUEST']: (state) => {
    state.status = 'loading';
  },

  ['NOTICE_GET_ALL_SUCCESS']: (state, { data }) => {
    state.notices = data;
  },

  ['NOTICE_DELETE_SUCCESS']: (state) => {
    state.notices = [];
  },

  ['NOTICE_GET_SUCCESS']: (state, { data }) => {
    state.status = 'success';
    state.singleNotice = data.data.data;
  },
};

export default {
  state,
  getters,
  actions,
  mutations,
};

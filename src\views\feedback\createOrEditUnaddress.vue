<template>
  <div class="">
    <PageTitle
      :items="{
        title: 'フィードバック',
        subTitle: pageSubTitle,
        back: {
          action: () => {
            $router.push({
              name: 'UnaddressFeedbacks',
            });
          },
        },
      }"
    ></PageTitle>
    <Form
      v-if="route.name === 'AddressedFeedbackCreate' || (route.name === 'UnaddressFeedbackEdit' && getSingleFeedback.student_id && getSingleFeedback)"
      @submit="
        $route.params.id ? saveAndChangeStatus(statusTemp) : saveFeedback()
      "
    >
      <v-row>
        <v-col cols="8">
          <!-- Main card container -->
          <v-card class="w-100 pb-12">
            <div class="mx-auto" style="max-width: 642px">
              <div>
                <div class="mt-20">
                  <!-- Loop through fields and create form inputs dynamically -->
                  <v-sheet
                    v-for="(field, index) in fields"
                    :key="field.name || index"
                    width="100%"
                    :class="field.class"
                    color="transparent"
                  >
                    <Field
                      :key="isFirstLoadedOrFromSelectfield ? field.value : ''"
                      v-slot="{
                        field: { value, ...fieldWithoutValue },
                        errors,
                      }"
                      :name="field.name"
                      :rules="field.rules"
                      :value="
                        $route.params.id
                          ? field.value || getSingleFeedback[field.name]
                          : field.value
                      "
                    >
                      <template
                        v-if="
                          field.type != 'dropdownMonth' &&
                          field.type != 'dropdownWorkMonth'
                        "
                      >
                        <label class="d-block font-14px mb-1">
                          <span>{{ field.label }}</span>
                          <span
                            v-if="field.required"
                            class="error--text ml-2 font-12px"
                            >必須</span
                          >
                        </label>
                        <v-select
                          v-bind="fieldWithoutValue"
                          v-if="field.type === 'select'"
                          :items="field.items"
                          v-model="field.value"
                          density="compact"
                          variant="outlined"
                          :item-title="field.item_text"
                          :item-value="field.item_value"
                          :placeholder="field.placeholder"
                          :error-messages="errors"
                          :error="errors.length !== 0"
                          :hide-details="errors.length <= 0"
                          menu-icon="$greyExpansionDropdown"
                          @update:modelValue="selectUpdated(field)"
                        ></v-select>
                        <v-autocomplete
                          v-bind="fieldWithoutValue"
                          v-if="field.type === 'autocomplete'"
                          :items="field.items"
                          v-model="field.value"
                          density="compact"
                          variant="outlined"
                          v-model:search="field.searched_text"
                          @keyup="field.search_api(field)"
                          :loading="field.is_loading"
                          :hide-no-data="field.is_hide_no_data"
                          :item-title="field.item_text"
                          :item-value="field.item_value"
                          hide-selected
                          :placeholder="field.placeholder"
                          :error-messages="errors"
                          :error="errors.length !== 0"
                          :hide-details="errors.length <= 0"
                          color="#13ABA3"
                          append-icon=""
                          autocomplete="new-password"
                        ></v-autocomplete>
                        <v-text-field
                          v-bind="fieldWithoutValue"
                          v-if="field.type === 'text'"
                          v-model="field.value"
                          :placeholder="field.placeholder"
                          :error-messages="errors"
                          :error="errors.length !== 0"
                          :hide-details="errors.length <= 0"
                          variant="outlined"
                          density="compact"
                          :disabled="field.disabled"
                        ></v-text-field>
                        <v-textarea
                          v-bind="fieldWithoutValue"
                          v-if="field.type === 'text-area'"
                          height="210px"
                          variant="outlined"
                          density="compact"
                          :placeholder="field.placeholder"
                          v-model="field.value"
                          :error-messages="errors"
                          :error="errors.length !== 0"
                          :hide-details="errors.length <= 0"
                        >
                        </v-textarea>
                        <DatePicker
                          v-bind="fieldWithoutValue"
                          v-if="field.type === 'datepicker'"
                          :field="field.date"
                          :errors="errors"
                          density="compact"
                          v-model="field.value"
                          :error-messages="errors"
                          :error="errors.length !== 0"
                          :hide-details="errors.length <= 0"
                          @input="error = null"
                          :min="minStartDate"
                          :separator="'/'"
                          :pickerType="field.pickerType"
                        >
                        </DatePicker>
                      </template>
                      <template v-else>
                        <div
                          class="d-flex"
                          style="width: 642px"
                          v-if="field.type != 'dropdownWorkMonth'"
                        >
                          <div
                            class="full-width width-full w-100 mr-2"
                          >
                            <label class="d-block font-14px mb-1">
                              <span>{{ field.label }}</span>
                              <span
                                v-if="field.required"
                                class="error--text ml-2 font-12px"
                                >必須</span
                              >
                            </label>
                            <v-select
                              v-bind="fieldWithoutValue"
                              density="compact"
                              variant="outlined"
                              single-line
                              class="mt-1"
                              v-model="field.value"
                              :placeholder="field.placeholder"
                              :items="getFeedbackMonths"
                              :item-title="field.item_text"
                              :item-value="field.item_value"
                              :error-messages="errors"
                              :error="errors.length !== 0"
                              :hide-details="errors.length <= 0"
                              autocomplete="chrome-off"
                              menu-icon="$greyExpansionDropdown"
                              no-data-text="データがありません"
                              :disabled="getSingleFeedback.feedback_status == 4"
                              :class="{
                                'bg-input-disabled':
                                  getSingleFeedback.feedback_status == 4,
                              }"
                            >
                            </v-select>
                          </div>
                          <div
                            class="full-width width-full w-100 mr-2"
                          ></div>
                          <!-- if edit -->
                          <!-- <div
                            class="full-width width-full w-100 mr-2"
                            v-if="$route.params.id"
                          >
                            <label class="d-block font-14px mb-1">
                              <span>{{ field.label }}</span>
                              <span
                                v-if="field.required"
                                class="error--text ml-2 font-12px"
                                >必須</span
                              >
                            </label>
                            <v-text-field
                              v-bind="fieldWithoutValue"
                              v-model="field.value"
                              :placeholder="field.placeholder"
                              :error-messages="errors"
                              :error="errors.length !== 0"
                              :hide-details="errors.length <= 0"
                              density="compact"
                              variant="outlined"
                              class="bg-input-disabled"
                              disabled
                            ></v-text-field>
                          </div>
                          <div class="full-width width-full w-100">
                            <label class="d-block font-14px mb-1">
                              <span>{{ fields[3].label }}</span>
                              <span
                                v-if="fields[3].required"
                                class="error--text ml-2 font-12px"
                                >必須</span
                              >
                            </label>
                            <v-text-field
                              v-bind="fieldWithoutValue"
                              v-model="fields[3].value"
                              :placeholder="fields[3].placeholder"
                              :error-messages="errors"
                              :error="errors.length !== 0"
                              :hide-details="errors.length <= 0"
                              density="compact"
                              variant="outlined"
                              class="bg-input-disabled"
                              disabled
                            ></v-text-field>
                          </div> -->
                        </div>
                      </template>
                    </Field>
                  </v-sheet>
                </div>
              </div>
            </div>
            <!-- Centered section title -->
            <div class="font-18px text-center">
              Kotonaru Power 8について
              <!-- Centered power cards container -->
              <div class="mx-auto mt-6" style="width: 642px">
                <v-row justify="center">
                  <v-col
                    v-for="(power, index) in kotonaru_power_8"
                    :key="index"
                    cols="12"
                    md="3"
                  >
                    <div class="kotonaru-card">
                      <div class="kotonaru-card-title">{{ power.title }}</div>
                      <div class="kotonaru-card-text">
                        {{ power.description }}
                      </div>
                    </div>
                  </v-col>
                </v-row>
              </div>
            </div>
          </v-card>
        </v-col>
        <v-col cols="4">
          <v-card class="d-flex align-center justify-center" width="100%">
            <div v-if="$route.params.id" class="align-center justify-center">
              <!-- Save as Draft-->
              <div
                v-if="
                  getSingleFeedback.feedback_status === 1 ||
                  getSingleFeedback.feedback_status === 2
                "
              >
                <v-btn
                  height="35px"
                  width="250px"
                  variant="outlined"
                  class="mt-10"
                  type="submit"
                  color="#13ABA3"
                  @click="statusTemp = 2"
                >
                  下書き保存
                </v-btn>
                <br />
              </div>

              <!--Apply-->
              <div
                v-if="
                  getSingleFeedback.feedback_status === 1 ||
                  getSingleFeedback.feedback_status === 2
                "
              >
                <v-btn
                  height="35px"
                  width="250px"
                  class="mt-5 mb-10 text-capitalize white--text font-14px fw-500 btn-primary"
                  variant="outlined"
                  type="submit"
                  color="#13ABA3"
                  @click="statusTemp = 3"
                >
                  申請
                </v-btn>
                <br />
              </div>

              <!-- Temporary Save-->
              <div v-if="getSingleFeedback.feedback_status === 3">
                <v-btn
                  height="35px"
                  width="250px"
                  class="mt-10"
                  variant="outlined"
                  type="submit"
                  color="#13ABA3"
                  @click="statusTemp = 3"
                >
                  一時保存
                </v-btn>
                <br />
              </div>

              <!-- Public -->
              <div v-if="getSingleFeedback.feedback_status === 3">
                <v-btn
                  height="35px"
                  width="250px"
                  class="mt-5 text-capitalize white--text font-14px fw-500 btn-primary"
                  variant="outlined"
                  type="submit"
                  color="#13ABA3"
                  @click="statusTemp = 4"
                >
                  公開
                </v-btn>
                <br />
              </div>

              <div
                v-if="getSingleFeedback.feedback_status === 3"
                class="d-flex justify-center"
              >
                <div
                  @click.stop="dialog.delete = true"
                  class="font-14px mt-6 mb-10 cursor-pointer error--text font-weight-medium"
                >
                  削除
                </div>
              </div>

              <!-- Update and Apply -->
              <div class="mt-12" v-if="getSingleFeedback.feedback_status === 4">
                <v-btn
                  v-if="getSingleFeedback.feedback_status === 4"
                  height="35px"
                  width="250px"
                  class="text-capitalize white--text font-14px fw-500 btn-primary"
                  variant="outlined"
                  type="submit"
                  color="#13ABA3"
                  @click="statusTemp = 3"
                >
                  更新＆申請
                </v-btn>
              </div>
            </div>
          </v-card>
        </v-col>
      </v-row>
    </Form>

    <SuccessModel
      :text="`フィードバックを削除しました。`"
      :buttonText="`フィードバック一覧へ戻る`"
      :routeName="`Feedbacks`"
      :dialog="dialog.deleteSuccess"
      @closeModel="dialog.success = false"
    ></SuccessModel>
    <SuccessModel
      :text="`下書きとして保存しました。`"
      :buttonText="`下書き一覧へ戻る`"
      :routeName="`Feedbacks`"
      :dialog="dialog.draftSuccess"
      @closeModel="dialog.draftSuccess = false"
    ></SuccessModel>
    <SimpleModel
      text="このフィードバックを削除しますか？"
      :dialog="dialog.delete"
      @submitSuccess="deleteFeedback()"
      @closeModel="dialog.delete = false"
      submitButtonText="削除する"
    ></SimpleModel>
  </div>
</template>
<script setup>
import { computed, ref, watch, onMounted, defineAsyncComponent } from 'vue';
import { useStore } from 'vuex';
import { useRouter, useRoute } from 'vue-router';
const DatePicker = defineAsyncComponent(
  () => import('@/components/ui/DatePicker.vue')
);
const SimpleModel = defineAsyncComponent(
  () => import('@/components/models/SimpleModel.vue')
);
const SuccessModel = defineAsyncComponent(
  () => import('@/components/models/SuccessModel.vue')
);
import { debounce } from 'debounce';
import { Field, Form } from 'vee-validate';
import moment from 'moment';

const router = useRouter();
const route = useRoute();
const store = useStore();

const feedbackID = ref(null);
const pageType = ref('create');
const statusTemp = ref(0);

const getSingleFeedback = computed(() => store.getters.getSingleFeedback);
const getAllCompany = computed(() => store.getters.getAllCompany);
const getAllStudent = computed(() => store.getters.getAllStudent);
const feedbackMonths = ref([]);
const getFeedbackMonths = computed(() => {
  const months = store.getters.getFeedbackMonths ? [...store.getters.getFeedbackMonths] : []; // Create a copy of the store's array

  if (getSingleFeedback.value) {
    const tempMonth = {
      month_feedback_target: getSingleFeedback.value.month_feedback_target?.replace(/-/g, '/'),
      work_month_feedback_target: getSingleFeedback.value.work_month_feedback_target?.replace(
        /-/g,
        '/'
      ),
      label: `${getSingleFeedback.value.month_feedback_target?.replace(/-/g, '/')} (稼働月: ${getSingleFeedback.value.work_month_feedback_target?.replace(/-/g, '/')})`,
    };
    months.push(tempMonth);
  }

  // Remove duplicates based on month_feedback_target and work_month_feedback_target
  const uniqueMonths = [];
  const seen = new Set();

  for (const month of months) {
    const key = `${month.work_month_feedback_target}-${month.month_feedback_target}`;
    if (!seen.has(key)) {
      uniqueMonths.push(month);
      seen.add(key);
    }
  }

  uniqueMonths.sort((a, b) => {
    const dateA = new Date(a.month_feedback_target);
    const dateB = new Date(b.month_feedback_target);
    return dateA - dateB;
  });

  feedbackMonths.value = uniqueMonths; // Update the local ref
  return feedbackMonths.value;
});
const getMasterData = computed(() => store.getters.getMasterData);

const selectUpdated = (field) => {
  isFirstLoadedOrFromSelectfield.value = true

  if (field && field.label === '評価された力' && tempPowerId.value !== field.value) {
    fields.value[4].value = store.getters.getSuperPowerDefault.find(item => item.id === field.value).value;
  }
  
  if (field && field.label === '評価された力' && tempPowerId.value === field.value) {
    fields.value[4].value = tempPower.value;
  }

  if (field && field.label === '期待したい力' && tempGrowthId.value !== field.value) {
    fields.value[6].value = store.getters.getGrowthIdeaDefault.find(item => item.id === field.value).value;
  }

  if (field && field.label === '期待したい力' && tempGrowthId.value === field.value) {
    fields.value[6].value = tempGrowth.value;
  }

  setTimeout(() => {
    isFirstLoadedOrFromSelectfield.value = false
  }, 250);
};
const tempPower = ref(null);
const tempGrowth = ref(null);
const tempPowerId = ref(null);
const tempGrowthId = ref(null);

const searchStudent = debounce((field) => {
  field.is_loading = true;
  store
    .dispatch('STUDENT_GET_ALL', {
      sort_by_order: 'desc',
      sort_by: 'created_at',
      page: 1,
      paginate: 10,
      status: 1,
      search: field.searched_text || student_search.value,
      silent_loading: true,
    })
    .then(() => {
      field.items = getAllStudent.value;
      if (route.params?.student_id) {
        const index = field.items.findIndex(
          (item) => item.student_internal_id === route.params.student_id
        );
        field.value = index !== -1 ? field.items[index].id : null;
      }
      field.is_loading = false;
    });
}, 500);

const searchCompany = debounce(function (field) {
  field.is_loading = true;

  store
    .dispatch('COMPANY_GET_ALL', {
      search: field.searched_text ?? null,
      silent_loading: true,
      page: 1,
      paginate: 10,
      showActive: 1,
    })
    .then(() => {
      // Ensure `items` is initialized before assigning a value
      if (Array.isArray(field.items)) {
        field.items = getAllCompany.value;
      } else {
        // If `items` is not defined or is not an array, initialize it as an array
        field.items = [...getAllCompany.value];
      }

      field.is_loading = false;
    })
    .catch((error) => {
      console.error('Error fetching company data:', error);
    });
}, 500);

const getStudentSuggestionText = (item) =>
  [
    item.student_internal_id,
    item.family_name,
    item.first_name,
    item.family_name_furigana,
    item.first_name_furigana,
  ]
    .filter(Boolean)
    .join(' ');

const getCompanySuggestionText = (item) =>
  [item.internal_company_id, item.name, item.furigana_name]
    .filter(Boolean)
    .join(' ');

const dialog = ref({
  delete: false,
  deleteSuccess: false,
  draftSuccess: false,
});

const student_search = ref(null);

const kotonaru_power_8 = ref([
  {
    title: 'リーダーシップ',
    description:
      '主体性を発揮し、目標の達成に向かってチームを牽引することができます。',
  },
  {
    title: '大胆さ',
    description: '失敗や否定的な意見を恐れず、発言や行動をすることができます。',
  },
  {
    title: '外向的',
    description:
      '興味関心が広く、行動的で社交的です。外の世界に対し、自らアプローチできます。',
  },
  {
    title: '創造性',
    description:
      '自分なりの考えや新しい視点でアイデアを出したり、切り口を変えた提案ができます。',
  },
  {
    title: '協調性',
    description:
      'チームメンバーの意見に注意深く耳を傾け、共感することで、誰もが同じ目標に向かって取り組む環境を作ります。',
  },
  {
    title: '綿密さ',
    description:
      '詳細な点も把握し、決定や選択を行う際には、潜在的なリスクに注意を払い判断できます。',
  },
  {
    title: '内省的',
    description:
      '自分の発言や行動、その結果起きたことを振り返り、そこから学びを得ることができます。',
  },
  {
    title: '論理性',
    description:
      '物事の構造を理解し、考えの妥当性を検証したり、筋道を立てた発言ができます。',
  },
]);

const fields = ref([
  {
    label: '企業名',
    name: 'company_id',
    type: 'autocomplete',
    items: getAllCompany.value,
    item_text: getCompanySuggestionText,
    item_value: 'id',
    searchable: true,
    search_api: searchCompany,
    is_loading: false,
    searched_text: '',
    is_hide_no_data: true,
    value: null,
    placeholder: '内部ID、企業名または企業名フリガナを入力してください',
    width: '642px',
    required: true,
    rules: 'required:企業名',
    class: 'mb-2',
    disabled: true,
  },
  {
    label: '学生名',
    name: 'student_id',
    type: 'autocomplete',
    items: getAllStudent.value,
    item_text: getStudentSuggestionText,
    item_value: 'id',
    value: null,
    searchable: true,
    search_api: searchStudent,
    is_loading: false,
    searched_text: '',
    is_hide_no_data: true,
    placeholder: '学生ID、学生名または学生名フリガナを入力してください',
    width: '642px',
    required: true,
    rules: 'required:学生名',
    class: 'mb-5',
    disabled: true,
  },
  {
    label: 'フィードバック対象月',
    name: 'month_feedback_target',
    type: 'dropdownMonth',
    value: null,
    placeholder: '選択してください',
    width: '219px',
    class: 'mb-5',
    value: '',
    item_value: 'month_feedback_target',
    item_text: 'label',
    items: getFeedbackMonths,
    no_data_text: '選択してください',
    pickerType: 'month',
    rules: 'required:フィードバック対象月',
    required: true,
  },
  // {
  //   label: 'フィードバック対象稼働月',
  //   name: 'work_month_feedback_target',
  //   type: 'dropdownWorkMonth',
  //   value: null,
  //   placeholder: '',
  //   width: '219px',
  //   class: 'mb-5',
  //   disabled: route.name !== 'AddressedFeedbackCreate',
  // },
  {
    label: '評価された力',
    name: 'super_power_review',
    type: 'select',
    value: null,
    items: [],
    item_text: 'name',
    item_value: 'id',
    placeholder: '選択してください',
    width: '219px',
    required: true,
    rules: 'required:評価された力',
    class: 'mb-2',
  },
  {
    label: '評価された力　本文',
    name: 'super_power_comment',
    type: 'text-area',
    value: null,
    placeholder: '入力してください',
    width: '642px',
    required: true,
    rules: 'required:評価された力　本文',
    class: 'mb-5',
  },
  {
    label: '期待したい力',
    name: 'growth_idea_review',
    type: 'select',
    value: null,
    items: [],
    item_text: 'name',
    item_value: 'id',
    placeholder: '選択してください',
    width: '219px',
    required: true,
    rules: 'required:期待したい力',
    class: 'mb-2',
  },
  {
    label: '期待したい力　本文',
    name: 'growth_idea_comment',
    type: 'text-area',
    value: null,
    placeholder: '入力してください',
    width: '642px',
    required: true,
    rules: 'required:期待したい力　本文',
    class: 'mb-9',
  },
  {
    label: '',
    name: 'flg_feedback_target',
    class: 'mb-9',
  },
]);


const getCurrentFeedbackMonth = computed(() => {
  return fields.value[2].value;
});
const work_month_feedback_target = ref(null);
// Using watch with the computed property
watch(getCurrentFeedbackMonth, (newMonths, oldMonths) => {
  const currentWorkMonth = getFeedbackMonths.value.find((f) => f.month_feedback_target === getCurrentFeedbackMonth.value);
  work_month_feedback_target.value = currentWorkMonth?.work_month_feedback_target || '';
});

// for first loaded validator or changed by dropdown
const isFirstLoadedOrFromSelectfield = ref(false)

/**
 * Component initialization
 * Sets up initial data and fetches necessary information based on route parameters
 */
onMounted(() => {
  isFirstLoadedOrFromSelectfield.value = true
  checkPageType();

  // Initialize student data if student ID is provided
  if (route.params.student_id) {
    const studentField = fields.value.find((i) => i.name === 'student_id');
    searchStudent(studentField);
  }

  // Initialize company data if company ID is provided
  if (route.params.company_id) {
    const companyField = fields.value.find((i) => i.name === 'company_id');
    searchCompany(companyField);
  }
});

/**
 * Computed properties for dynamic content and form behavior
 */
// Determines page subtitle based on route name
const pageSubTitle = computed(() =>
  route.name === 'FeedbackCreate' ? '新規作成' : '編集'
);

// Gets minimum date for date picker (current date)
const minStartDate = computed(() => moment().format('YYYY-MM-DD'));

// References to form field values for reactivity
const studentId = computed(() => fields.value[1]?.value);
const companyId = computed(() => fields.value[0]?.value);

/**
 * Fetches and updates feedback months when company and student are selected
 * Enables/disables month selection based on company and student selection
 */
const fetchFeedbackMonth = () => {
  if (companyId.value && studentId.value) {
    const params = {
      studentId: studentId.value,
      companyId: companyId.value,
    };
    // Get available feedback months from API
    store.dispatch('FEEDBACK_GET_MONTH', params).then((data) => {
      let currentDate = fields.value[2].value;
      fields.value[2].value = currentDate.replace(/\-/g, '/');
    });
    // Enable/disable month selection based on company and student selection
    fields.value[2].disabled = !(
      fields.value[0].value && fields.value[1].value
    );
  }
};

// Watch for changes in company, student, and month selections
watch(companyId, fetchFeedbackMonth);
watch(studentId, fetchFeedbackMonth);


/**
 * Initializes form fields with master data options
 * Sets review options for power and growth idea fields
 */
const setfieldsItemsData = () => {
  fields.value = fields.value.map((field) => {
    if (['super_power_review', 'growth_idea_review'].includes(field.name)) {
      field.items = getMasterData.value.reviews_option;
    }
    return field;
  });
};

/**
 * Determines page type (create/edit) and initializes accordingly
 * Handles both direct feedback editing and company/student specific creation
 */
const checkPageType = () => {
  setfieldsItemsData();
  // Edit mode initialization
  if (route.params?.id) {
    feedbackID.value = route.params.id;
    pageType.value = 'edit';
    getPageData();
  }
  // Create mode with pre-selected company and student
  if (route.params?.company_id && route.params?.student_id) {
    company_search.value = route.params.company_id;
    fields.value[1].search_api(fields.value[1]);
    student_search.value = route.params.student_id;
    fields.value[2].search_api(fields.value[2]);
  }
};

/**
 * Fetches feedback data for editing
 * Redirects to listing on error
 */
const getPageData = () => {
  store
    .dispatch('FEEDBACK_GET', {
      id: feedbackID.value,
    })
    .then(setPageData)
    .catch(() =>
      router.push({
        name: 'Feedbacks',
      })
    );
};

/**
 * Populates form fields with fetched feedback data
 * Handles special formatting for date fields
 */
const duplicateVar = (value) => JSON.parse(JSON.stringify(value));
const setPageData = () => {
  fields.value.forEach((field) => {
    // Set company and student data
    if (field.name === 'company_id') {
      field.items = [getSingleFeedback.value.companies];
    }
    if (field.name === 'student_id') {
      field.items = [getSingleFeedback.value.student];
    }
    field.value = getSingleFeedback.value[field.name];

    // Format feedback target month
    if (field.name == 'month_feedback_target') {
      field.value = moment(
        getSingleFeedback.value?.month_feedback_target
      ).format('YYYY/MM');
    }

    tempGrowthId.value = duplicateVar(fields.value[5].value)
    tempPowerId.value = duplicateVar(fields.value[3].value)
    tempGrowth.value = duplicateVar(fields.value[6].value)
    tempPower.value = duplicateVar(fields.value[4].value)

    // Delayed update for work month to ensure proper initialization
    setTimeout(() => {
      if (field.name == 'work_month_feedback_target') {
        field.value = moment(
          getSingleFeedback.value?.work_month_feedback_target
        ).format('YYYY/MM');
      }
    }, 1000);
  });
  isFirstLoadedOrFromSelectfield.value = false
};

/**
 * Updates feedback status and saves changes
 * @param {number} status - New feedback status (2: draft, 3: pending, 4: published)
 */
const saveAndChangeStatus = (status) => {
  // Prepare data for API
  const data = {
    id: parseInt(feedbackID.value),
    is_draft_or_public: status === 2 ? 0 : 1, // 0 for draft, 1 for public
    feedback_status: status,
    month_feedback_target: getSingleFeedback.value?.month_feedback_target ? moment(
      getSingleFeedback.value?.month_feedback_target
    ).format('YYYY-MM') : '',
    work_month_feedback_target: work_month_feedback_target.value ? moment(
      work_month_feedback_target.value
    ).format('YYYY-MM') : '',
    date_contract_employment_start:
      getSingleFeedback.value?.date_contract_employment_start,
    date_contract_employment_end:
      getSingleFeedback.value?.date_contract_employment_end,
    flg_feedback_target: getSingleFeedback.value?.flg_feedback_target || 1,
    contract_id: getSingleFeedback.value?.contract_id,
  };

  // Add form field values
  fields.value.forEach((field) => {
    data[field.name] = field.value;
  });

     // Format dates and submit
  data.month_feedback_target = data.month_feedback_target ? moment(data.month_feedback_target).format('YYYY-MM') : '';
  // Format dates and submit
  data.work_month_feedback_target = data.work_month_feedback_target ? moment(data.work_month_feedback_target).format('YYYY-MM') : '';
  
  store.dispatch('FEEDBACK_UPDATE', data)
  .then(() => {
    if (status === 2) {
      router.push({ name: 'UnaddressFeedbacks', query: { tab: 'draft' }, });
      store.dispatch('updateStatusAlert', {
        showStatusAlert: true,
        statusAlertMessage: 'フィードバックの下書きを保存しました。',
      });
    } else {
      router.push({ name: 'Feedbacks' });
    }

    setTimeout(() => {
      if (status === 4) {
        store.dispatch('updateStatusAlert', {
          showStatusAlert: true,
          statusAlertMessage: 'フィードバックを公開しました',
        });
      } else if (status === 3) {
        store.dispatch('updateStatusAlert', {
          showStatusAlert: true,
          statusAlertMessage: 'フィードバックを申請しました',
        });
      }
    }, 1000);
  })
  .catch(error => {
    console.error('Failed to update feedback:', error);
    // optionally dispatch error alert here
  });
};

/**
 * Creates new feedback with default values
 * Formats dates and sets status before submission
 */
const saveFeedback = () => {
  const data = {};
  // Collect form field values
  fields.value.forEach((field) => {
    data[field.name] = field.value;
  });

  // Format dates
  data.month_feedback_target = moment(data.month_feedback_target).format(
    'YYYY-MM'
  );
  // data.work_month_feedback_target = moment(
  //   data.work_month_feedback_target
  // ).format('YYYY-MM');

  // Set default values for new feedback
  data.is_draft_or_public = 1; // Public by default
  data.feedback_status = 4; // Published status
  data.flg_feedback_target = 1; // Enable feedback target flag

  // Submit and redirect
  store.dispatch('FEEDBACK_CREATE', data).then(() =>
    router.push({
      name: 'Feedbacks',
    })
  );
};

/**
 * Deletes feedback and shows success message
 * Updates UI state after successful deletion
 */
const deleteFeedback = () => {
  store
    .dispatch('FEEDBACK_DELETE', {
      id: route.params.id,
    })
    .then(() => (dialog.value.deleteSuccess = true))
    .finally(() => (loading.value = false));
};
</script>
<style lang="scss">
.kotonaru-card {
  background-color: #fffbf2;
  /* Light yellow background */
  border-radius: 8px;
  /* Rounded corners */
  overflow: hidden;
  /* Clip children to fit the rounded corners */
  min-height: 131.76px;
}

.kotonaru-card-title {
  background-color: #f9cb53;
  /* Amber color for the title background */
  color: #fff;
  /* White text color for contrast */
  font-weight: bold;
  /* Bold text for title */
  padding: 8px 12px;
  /* Padding around the title */
  text-align: center;
  /* Centered text */
  font-size: 14px;
  /* Adjust font size */
}

.kotonaru-card-text {
  padding: 12px;
  /* Padding for the text */
  font-size: 12px !important;
  /* Font size for the description */
  color: #333;
  /* Dark color for the text */
  text-align: left;
  min-height: 131.76px;
}
</style>

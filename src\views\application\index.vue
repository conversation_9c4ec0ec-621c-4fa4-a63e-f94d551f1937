<template>
  <div class="">
    <PageTitle
      v-show="!loading && !$store.getters.getApiProcessingStatus"
      :items="{
        title: '応募管理',
        subTitle: '一覧',
        tabs: [
          {
            title: '応募済',
            count: getApplicationCounts.total_applied
              ? getApplicationCounts.total_applied?.value
              : 0,
            notification: getApplicationCounts.total_applied
              ? getApplicationCounts.total_applied?.admin_read
              : 0,
            action: () => resultOnTab(1),
          },
          {
            title: '応募返信済',
            count: getApplicationCounts.business_respond
              ? getApplicationCounts.business_respond?.value
              : 0,
            notification: getApplicationCounts.business_respond
              ? getApplicationCounts.business_respond?.admin_read
              : 0,
            action: () => resultOnTab(2),
          },
          {
            title: '合格',
            count: getApplicationCounts.total_completed
              ? getApplicationCounts.total_completed?.value
              : 0,

            action: () => resultOnTab(3),
          },
          {
            title: '不合格',
            count: getApplicationCounts.total_failed
              ? getApplicationCounts.total_failed?.value
              : 0,
            notification: getApplicationCounts.total_failed
              ? getApplicationCounts.total_failed?.admin_read
              : 0,
            action: () => resultOnTab(4),
          },
          {
            title: '辞退',
            count: getApplicationCounts.total_declined
              ? getApplicationCounts.total_declined?.value
              : 0,
            notification: getApplicationCounts.total_declined
              ? getApplicationCounts.total_declined?.admin_read
              : 0,
            action: () => resultOnTab(5),
          },
          {
            title: '連絡とれず',
            count: getApplicationCounts.no_responses
              ? getApplicationCounts.no_responses?.value
              : 0,
            notification: getApplicationCounts.no_responses
              ? getApplicationCounts.no_responses?.admin_read
              : 0,
            action: () => resultOnTab(6),
          },
          {
            title: 'その他',
            count: getApplicationCounts.other_reasons
              ? getApplicationCounts.other_reasons?.value
              : 0,
            notification: getApplicationCounts.other_reasons
              ? getApplicationCounts.other_reasons?.admin_read
              : 0,
            action: () => resultOnTab(99),
          },
        ],
        buttons: [
          {
            title: 'CSVエクスポート',
            class: 'bg-white text-ff862f',
            color: 'text-ff862f',
            variant: 'outlined',
            action: () => downloadCsv(),
          },
          {
            title: '詳細条件検索',
            class: 'bg-white',
            variant: 'outlined',
            action: () => {
              toggleSearch = !toggleSearch;
            },
          },
        ],
      }"
    ></PageTitle>
    <v-fade-transition>
      <SearchArea
        class="mb-4"
        v-if="toggleSearch"
        v-bind="{ toggleSearch, selectTypeOptions, searchFields }"
        @toggleSearch="updateSearchResults"
        @changedInputType="setChangedInputType"
        @searchSubmit="searchSubmit"
      ></SearchArea>
    </v-fade-transition>
    <DataTable
      v-show="!loading && !$store.getters.getApiProcessingStatus"
      :headers="getHeaders"
      :items="items"
      :customHeight="'height: 69px'"
      :total-records="
        getApplicationPagination ? getApplicationPagination.records_total : 0
      "
      :number-of-pages="
        getApplicationPagination ? getApplicationPagination.total_pages : 0
      "
      @update:options="updateTable"
    >
      <template v-slot:[`item.id`]="{ item }">
        <div
          class="position-relative"
          :class="item.student.reason_for_withdrawal ? 'text-b8b8b8' : ''"
        >
          {{ item.id }}
          <div class="position-absolute" style="top: 0%; left: 0px">
            <v-icon size="6px" v-if="!item.is_admin_read">$notification</v-icon>
          </div>
        </div>
      </template>

      <template v-slot:[`item.job_id`]="{ item }">
        <div
          class="font-12px fw-400"
          :class="item.student.reason_for_withdrawal ? 'text-b8b8b8' : ''"
        >
          {{ item.internship_post.id }}
        </div>
        <div class="mouse-pointer">
          <router-link
            target="_blank"
            class="text-3f74c2 text-decoration-none truncate-lines lines-1"
            :to="{
              name: 'InternshipPostEdit',
              params: { id: item.internship_post.id },
            }"
          >
            <v-tooltip
              :text="item.internship_post.title"
              location="top"
              color="white"
            >
              <template #activator="{ props }">
                <span v-bind="props">{{ item.internship_post.title }}</span>
              </template>
            </v-tooltip>
          </router-link>
        </div>
      </template>

      <template v-slot:[`item.company_id`]="{ item }">
        <div
          class="font-12px fw-400"
          :class="item.student.reason_for_withdrawal ? 'text-b8b8b8' : ''"
        >
          {{ item?.company?.internal_company_id }}
        </div>
        <div class="mouse-pointer">
          <router-link
            v-if="item?.company?.id"
            target="_blank"
            class="text-3f74c2 text-decoration-none truncate-lines lines-1"
            :to="{
              name: 'CorporateDetails',
              params: { id: item.company.id },
            }"
          >
            <v-tooltip :text="item.company.name" location="top" color="white">
              <template #activator="{ props }">
                <span v-bind="props">{{ item.company.name }}</span>
              </template>
            </v-tooltip>
          </router-link>
        </div>
      </template>

      <template v-slot:[`item.student_id`]="{ item }">
        <div
          class="font-12px fw-400"
          :class="item.student.reason_for_withdrawal ? 'text-b8b8b8' : ''"
        >
          {{ item.student.student_internal_id }}
        </div>
        <div class="mouse-pointer">
          <router-link
            target="_blank"
            class="text-3f74c2 text-decoration-none truncate-lines lines-1"
            :to="{
              name: 'StudentProfile',
              params: { id: item.student.id },
            }"
          >
            <v-tooltip
              :text="`${item.student.family_name} ${item.student.first_name}`"
              location="top"
              color="white"
            >
              <template #activator="{ props }">
                <span v-bind="props">
                  {{ item.student.family_name }} {{ item.student.first_name }}
                </span>
              </template>
            </v-tooltip>
          </router-link>
        </div>
      </template>

      <template v-slot:[`item.university`]="{ item }">
        <div
          class="font-12px fw-400"
          :class="item.student.reason_for_withdrawal ? 'text-b8b8b8' : ''"
        >
          {{ item.student.education_facility.name }}
        </div>
        <div
          class="truncate-lines lines-1"
          :class="item.student.reason_for_withdrawal ? 'text-b8b8b8' : ''"
        >
          {{ item.student.obfuscate_email }}
        </div>
      </template>

      <template v-slot:[`item.datetime_applied`]="{ item }">
        <div class="font-12px fw-400">
          {{ item.datetime_applied ? dateFormat(item.datetime_applied) : '' }}
        </div>
      </template>

      <template v-slot:[`item.created_at`]="{ item }">
        <div
          class="font-12px fw-400"
          :class="item.student.reason_for_withdrawal ? 'text-b8b8b8' : ''"
        >
          {{ dateFormat(item.created_at) }}
        </div>
      </template>

      <template v-slot:[`item.application_detail`]="{ item }">
        <div
          v-if="item.student"
          @click="showDetailApp(item)"
          class="position-relative pl-5 cursor-pointer"
        >
          <v-icon color="#black">$FeedIcon</v-icon>
        </div>
      </template>

      <template v-slot:[`item.cancel_reason`]="{ item }">
        <div class="mouse-pointer">
            <v-tooltip
              :text="`${get_reason_text(item.cancel_reason)}`"
              location="top"
              color="white"
            >
              <template #activator="{ props }">
                <div class="truncate-lines lines-1" v-bind="props">
                  {{ get_reason_text(item.cancel_reason) }}
                </div>
              </template>
            </v-tooltip>
        </div>
      </template>

      <template v-slot:[`item.update_date`]="{ item }">
        <div
          class="font-12px fw-400"
          :class="item.student.reason_for_withdrawal ? 'text-b8b8b8' : ''"
        >
          {{ dateFormat(item.updated_at) }}
        </div>
      </template>

      <template v-slot:[`item.status`]="{ item }">
        <v-sheet color="transparent" class="d-flex align-center justify-center">
          <v-menu
            :close-on-click="true"
            offset-y
            activator="parent"
          >
            <!-- Activator slot for the menu -->
            <template v-slot:activator="{ props }">
              <v-chip
                :color="chipColor(item.status)"
                variant="flat"
                dense
                size="small"
                v-bind="props"
              >
                <div
                  class="d-flex align-center justify-space-between"
                  :class="
                    item.status === 6 || item.status === 8
                      ? 'pill-large'
                      : 'pill'
                  "
                >
                  <div class="text-truncate white--text font-12px">
                    {{ getStatus(item.status) }}
                  </div>
                  <v-icon v-if="item.status !== 3" size="18px" color="white"
                    >mdi-chevron-down</v-icon
                  >
                </div>
              </v-chip>
            </template>

            <!-- Menu content -->
            <v-list>
              <v-list-item
                class="mouse-pointer font-12px fw-400"
                v-for="(option, index) in options"
                :key="index"
                @click="statusChange(item, option)"
              >
                <v-list-item-title>{{ getStatus(option) }}</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </v-sheet>
      </template>
    </DataTable>
    <ActionAlertCard
      v-if="showStatusAlert"
      class="text-center"
      :message="statusAlertMessage"
      width="610px"
      height="190px"
      type="success"
      :alertStyle="'position: fixed; top: 60px'"
    />
     <ApplicationPassDialog
      v-if="selectedItem"
      text="<div class='text-center' style='line-height: 1.5'>「合格のお知らせ」をコトナル事務局から学生様に送ります。<br/>
今後の手続きについては、必ず、<br/>
企業様より学生様にご連絡ください。</div>"
      :dialog="dialog.pass"
      :status="selectedItemStatusNew"
      :selectedItem="selectedItem"
      @update="setUpdate"
      @back="dialog.pass = false"
    ></ApplicationPassDialog>
    <ApplicationNoContactDialog
      v-if="selectedItem"
      text="<div class='text-center' style='line-height: 1.5'>「選考終了のお知らせ」をコトナル事務局から学生様に送ります。<br/>
この後、学生様から返信があった場合は「応募返信済」に変更し、<br/>
採用プロセスを再開してください。</div>"
      :dialog="dialog.noContact"
      :status="selectedItemStatusNew"
      :selectedItem="selectedItem"
      @update="setUpdate"
      @back="cancelUpdate"
      @closeModel="cancelUpdate"
    ></ApplicationNoContactDialog>
    <ApplicationFailDialog
      text="<div class='text-center' style='line-height: 1.5;font-size:20px;'>不合格に変更し、「不合格のお知らせ」を<br/>
コトナル事務局から学生様に送りますか？</div>"
      :dialog="dialog.fail"
      :status="selectedItemStatusNew"
      :selectedItem="selectedItem"
      @submitSuccess="setUpdate"
      @closeModel="cancelUpdate"
    ></ApplicationFailDialog>
    <ApplicationRejectModel
      text="辞退の理由を選択してください"
      :dialog="dialog.reject"
      :reasons="getReasons"
      @submitSuccess="setDecline"
      @closeModel="dialog.reject = false"
    ></ApplicationRejectModel>
    <ApplicationDetailDialog
      text="応募内容"
      :dialog="dialog.isShowDetailApp"
      :application="application"
      @submitSuccess="dialog.isShowDetailApp = false"
      @closeModel="dialog.isShowDetailApp = false"
    ></ApplicationDetailDialog>
    <SimpleModel
      :text="errorMessages"
      :dialog="dialog.errorDialog"
      :showCloseIcon="true"
      @closeModel="dialog.errorDialog = false"
      :buttonOption="{
        hideCancel: true,
        hideSubmit: true,
      }"
    ></SimpleModel>
  </div>
</template>

<script>
import { ref, computed, onMounted, defineAsyncComponent } from 'vue';
import moment from 'moment';
const DataTable = defineAsyncComponent(
  () => import('@/components/ui/DataTable.vue')
);
const SearchArea = defineAsyncComponent(
  () => import('@/components/ui/SearchArea.vue')
);
const ApplicationFailDialog = defineAsyncComponent(
  () => import('@/components/models/ApplicationFailDialog.vue')
);
const ApplicationRejectModel = defineAsyncComponent(
  () => import('@/components/models/ApplicationRejectModel.vue')
);
const ApplicationDetailDialog = defineAsyncComponent(
  () => import('@/components/models/ApplicationDetailDialog.vue')
);
const ApplicationPassDialog = defineAsyncComponent(
  () => import('@/components/models/ApplicationPassDialog.vue')
);
const ApplicationNoContactDialog = defineAsyncComponent(
  () => import('@/components/models/ApplicationNoContactDialog.vue')
);
const SimpleModel = defineAsyncComponent(
  () => import('@/components/models/SimpleModel.vue')
);
import ActionAlertCard from '@/components/ui/ActionAlertCard.vue';

import Encoding from 'encoding-japanese';

import { useStore } from 'vuex';

export default {
  components: {
    DataTable,
    SearchArea,
    ApplicationRejectModel,
    ApplicationDetailDialog,
    ApplicationFailDialog,
    ApplicationPassDialog,
    ApplicationNoContactDialog,
    SimpleModel,
    ActionAlertCard,
  },
  setup() {
    const dateFormat = (date) => {
      return date ? moment(date).format('YYYY/MM/DD') : '---- / - / -';
    };
    const store = useStore();
    const loading = ref(false);
    const selection = ref(1);
    const toggleSearch = ref(false);
    const options = ref([1, 2, 3, 4, 5, 6, 99]);
    const items = ref([]);
    const application = ref({});
    const selectedItem = ref(null);
    const selectedItemStatusNew = ref(null);
    const statusAlertMessage = ref('');
    const showStatusAlert = ref(false);
    const errorMessages = ref(null);
    const dialog = ref({
      reject: false,
      isShowDetailApp: false,
      errorDialog: false,
      fail: false,
    });
    /**
     * Sets the application status and updates via API.
     * @param {number} status The new application status.
     * @param {number} isEmail Flag to indicate if email should be sent.
     */
    const setUpdate = (status, isEmail) => {
      updateApi(selectedItem.value, status, isEmail);
    };
    const configuration = ref({
      page: 1,
      sort_by: 'updated_at',
      sort_by_order: 'desc',
      paginate: 25,
    });
    const searchFields = ref([]);
    const selectTypeOptions = ref([
      {
        id: 'keyword_search',
        name: 'キーワード検索',
      },
      {
        id: 'created_at',
        name: '応募日',
      },
    ]);

    const getApplicationCounts = computed(
      () => store.getters.getApplicationCounts
    );
    const getApplicationPagination = computed(
      () => store.getters.getApplicationPagination
    );
    const getHeaders = computed(() => {
      if (selection.value < 5 || selection.value >= 6) {
        return [
          {
            title: 'ID',
            value: 'id',
            align: 'center',
            class: ['px-0'],
            sortable: false,
            width: '3.9%',
          },
          {
            title: '求人ID',
            subTitle: '求人タイトル',
            value: 'job_id',
            align: 'left',
            class: ['py-3', 'px-0'],
            sortable: false,
            width: '23.3%',
          },
          {
            title: '企業ID',
            subTitle: '企業名',
            align: 'left',
            class: ['px-0'],
            value: 'company_id',
            sortable: false,
            width: '15.43%',
          },
          {
            title: '学生ID',
            subTitle: '学生名',
            class: ['px-0'],
            value: 'student_id',
            align: 'left',
            sortable: false,
            width: '12.194%',
          },
          {
            title: '大学名',
            subTitle: '学生メールアドレス',
            value: 'university',
            class: ['px-0'],
            align: 'left',
            sortable: false,
            width: '16%',
          },
          {
            title: '応募日',
            sortable: true,
            align: 'left',
            class: ['px-0'],
            value: 'datetime_applied',
            width: '10.90%',
          },
          {
            title: '応募内容',
            sortable: false,
            align: 'left',
            class: ['px-0'],
            value: 'application_detail',
            width: '9.62%',
          },
          {
            title: '更新日',
            sortable: false,
            align: 'left',
            class: ['px-0'],
            value: 'update_date',
            width: '9.22%',
          },
          {
            title: '',
            value: 'status',
            class: ['px-0'],
            sortable: false,
            align: 'left',
            width: '8.33%',
          },
        ];
      }
      return [
        {
          title: 'ID',
          value: 'id',
          align: 'center',
          class: ['px-0'],
          sortable: false,
          width: '3.9%',
        },
        {
          title: '企業ID',
          subTitle: '求人タイトル',
          value: 'job_id',
          align: 'left',
          class: ['py-3', 'px-0'],
          sortable: false,
          width: '19.3%',
        },
        {
          title: '企業ID',
          subTitle: '企業名',
          align: 'left',
          class: ['px-0'],
          value: 'company_id',
          sortable: false,
          width: '15.43%',
        },
        {
          title: '学生ID',
          subTitle: '学生名',
          class: ['px-0'],
          value: 'student_id',
          align: 'left',
          sortable: false,
          width: '12.194%',
        },
        {
          title: '大学名',
          subTitle: '学生メールアドレス',
          value: 'university',
          class: ['px-0'],
          align: 'left',
          sortable: false,
          width: '18%',
        },
        {
          title: '応募日',
          sortable: true,
          align: 'left',
          class: ['px-0'],
          value: 'datetime_applied',
          width: '10.90%',
        },
        {
          title: '辞退理由',
          sortable: false,
          align: 'left',
          class: ['px-0'],
          value: 'cancel_reason',
          width: '16%',
        },
        {
          title: '更新日',
          sortable: false,
          align: 'left',
          class: ['px-0'],
          value: 'update_date',
          width: '9.22%',
        },
        {
          title: '',
          value: 'status',
          sortable: false,
          align: 'left',
          width: '10.33%',
        },
      ];
    });

    // Get cancellation reasons from Vuex store
    const getReasons = computed(
      () => store.getters.getMasterData?.cancel_reasons
    );

    // Handle tab changes in the application list
    const resultOnTab = async (tab) => {
      configuration.value.page = 1;
      selection.value = tab;
      await generateItems();
      resetPagination();
    };

    // Reset search parameters and refresh the list
    const updateSearchResults = async () => {
      toggleSearch.value = false;
      configuration.value.page = 1;
      configuration.value.search = null;
      configuration.value.date_from = null;
      configuration.value.date_to = null;
      await generateItems();
      resetPagination();
    };

    // Mark applications as read after 5 seconds of viewing
    const updateAdminRead = () => {
      // Get IDs of unread applications
      const applicationsToBeUpdated = items.value
        .filter((application) => application.is_admin_read == 0)
        .map((application) => application.id);

      if (applicationsToBeUpdated.length > 0) {
        setTimeout(async () => {
          // Update read status in backend
          await store.dispatch('UPDATE_APPLICATION_ADMIN_READ', {
            application_ids: applicationsToBeUpdated,
          });
          // Update read status in frontend
          items.value.forEach((item) => {
            const findId = applicationsToBeUpdated.find((id) => item.id === id);
            if (findId) {
              item.is_admin_read = 1;
            }
          });
        }, 5000);
      }
    };

    // Get cancellation reason text from master data
    const get_reason_text = (value) => {
      if (value) {
        return store.getters.getMasterData?.cancel_reasons.find(
          (item) => item.id == value
        )?.name;
      }
    };

    // Show application detail dialog
    const showDetailApp = (item) => {
      application.value = item;
      dialog.value.isShowDetailApp = true;
    };

    // Handle application decline process
    const declineItem = ref([]);
    const setDecline = (reason) => {
      dialog.value.reject = false;
      declineItem.value.cancel_reason = reason;
      updateApi(declineItem.value, 5); // 5 represents declined status
    };

    const cancelUpdate = () => {
      dialog.value.pass = false;
      dialog.value.fail = false;
      dialog.value.noContact = false;
      selectedItem.value = null;
      selectedItemStatusNew.value = 0;
    };

    const resetStatus = () => {
      selectedItemStatusNew.value = 0;
      selectedItem.value = null;
    };

        /**
     * Handles application status changes.
     * @param {Object} item The application item.
     * @param {number} option The new status value.
     * @param {number} isEmail Flag to indicate if email should be sent.
     */
    const statusChange = async (item, option, isEmail) => {
      if (item.status == option) return;
      resetStatus();
      if (!selectedItemStatusNew.value && option == 3) {
        dialog.value.pass = true;
        selectedItem.value = item;
        selectedItemStatusNew.value = option;
        return;
      }

      if (!selectedItemStatusNew.value && option == 5) {
        dialog.value.reject = true;
        declineItem.value = item;
        return;
      }

      if (!selectedItemStatusNew.value && option == 6) {
        dialog.value.noContact = true;
        selectedItem.value = item;
        selectedItemStatusNew.value = option;
        return;
      }

      if (!selectedItemStatusNew.value && option == 4) {
        dialog.value.fail = true;
        selectedItem.value = item;
        selectedItemStatusNew.value = option;
        return;
      }

      await updateApi(item, option, isEmail);
    };

    // Update application status in backend
    const updateApi = async (item, option, isEmail) => {
      selectedItem.value = null;
      selectedItemStatusNew.value = 0;
      const params = {
        id: item.id,
        company_id: item.company_id,
        student_id: item.student_id,
        internship_post_id: item.internship_post_id,
        is_admin_read: item.is_admin_read,
        cancel_reason: item.cancel_reason ? item.cancel_reason : null,
        status: option?.status || option,
      };
      
      if (isEmail == 1 || option?.emailOption == 1) {
        params.flg_send_ng_mail = true;
      } else if (isEmail == 2 || option?.emailOption == 2) {
        params.flg_send_ng_mail = false;
      }

      await store.dispatch('APPLICATION_UPDATE', params);

      if (option === 3) {
        showAlert('ステータスを変更しました。契約管理に進んでください。');
        store.dispatch('GET_COUNTS_DATA');
      }

      if (option?.status === 4 && params.flg_send_ng_mail === true) {
        showAlert(
          'ステータスを変更しました。「不合格のお知らせ」を送りました。'
        );
        dialog.value.fail = false; // equivalent to this.cancelUpdate()
      }

      if (option?.status === 4 && params.flg_send_ng_mail === false) {
        showAlert('ステータスを変更しました。お見送りメールをお送りください。');
        dialog.value.fail = false; // equivalent to this.cancelUpdate()
      }

      if (option === 6) {
        showAlert(
          'ステータスを変更しました。「選考終了のお知らせ」を送りました。'
        );
        dialog.value.fail = false; // equivalent to this.cancelUpdate()
      }

      await generateItems();
    };

    /**
     * Shows a status update alert message.
     * @param {string} message The alert message to display.
     */
    const showAlert = (message) => {
      statusAlertMessage.value = message;
      showStatusAlert.value = true;
      setTimeout(() => {
        showStatusAlert.value = false;
      }, 5000);
    };

    // Export application list to CSV
    const downloadCsv = async () => {
      await store.dispatch('APPLICATION_EXPORT_CSV');

      // Show error if no data found
      if (store.getters.getApplicationCsvData?.message) {
        dialog.value.errorDialog = true;
        errorMessages.value =
          '<div class="pt-10">データが見つかりません。</div>'; // "No data found"
        return;
      }

      // Get CSV data from Vuex store
      const csvData = store.getters.getApplicationCsvData.data.csv;

      // Convert UTF-8 CSV to Shift-JIS
      const sjisArray = Encoding.convert(Encoding.stringToCode(csvData), {
        to: 'SJIS',
        from: 'UNICODE',
      });

      const uint8Array = new Uint8Array(sjisArray);
      const fileUrl = window.URL.createObjectURL(
        new Blob([uint8Array], { type: 'text/csv;charset=Shift_JIS' })
      );

      const fileLink = document.createElement('a');
      fileLink.href = fileUrl;
      fileLink.setAttribute(
        'download',
        `応募管理_${new Date().toISOString().slice(0, 10)}.csv`
      );
      document.body.appendChild(fileLink);
      fileLink.click();
      document.body.removeChild(fileLink);
    };

    // Fetch application list with current filters
    const generateItems = async () => {
      loading.value = true;
      // Removed the clear-out of items so that previous records remain until new data arrives
      // items.value = [];
      configuration.value.status = selection.value;
      // Prepare request parameters
      const params = {
        status: selection.value,
        page: configuration.value.page,
        paginate: configuration.value.paginate,
        sort_by: configuration.value.sort_by,
        sort_by_order: configuration.value.sort_by_order,
      };
      // Add search parameters if present
      if (configuration.value.search) {
        params.search = configuration.value.search;
      }

      // Add date range parameters if they are present
      if (configuration.value.date_from) {
        params.date_from = configuration.value.date_from;
        params.date_to = configuration.value.date_to;
        // Clear search parameter when date range is used
        params.search = '';
      }

      try {
        // Fetch data from the store (API call)
        const response = await store.dispatch('APPLICATION_GET_ALL', params);

        // Populate items with the fetched data
        items.value = response.data.data.data;

        // Call any necessary updates after fetching the data
        updateAdminRead();
      } catch (error) {
        console.error('Error fetching application data:', error);
      } finally {
        // Ensure loading is set to false, no matter the result
        loading.value = false;
      }
    };

    // Reset pagination to first page
    const resetPagination = () => {
      configuration.value.page = 1;
    };

    // Handle search form submission
    const searchSubmit = async ($event) => {
      let obj = {};
      if ($event.fields.length > 0) {
        $event.fields.forEach((field) => {
          obj[field.name] = field.value;
        });
      }
      // Update configuration with search parameters
      configuration.value = {
        ...configuration.value,
        ...obj,
      };
      generateItems();
    };

    // Update search fields based on selected search type
    const setChangedInputType = (inputSearchType) => {
      if (inputSearchType === 'keyword_search') {
        // Text search for job ID, title, or company name
        searchFields.value = [
          {
            label: 'Label',
            name: 'search',
            type: 'text',
            value: '',
            placeholder: '求人ID、求人タイトル、企業名',
          },
        ];
      } else if (inputSearchType === 'created_at') {
        // Date range search
        searchFields.value = [
          {
            label: 'Label',
            name: 'date_from',
            type: 'date',
            rules: 'required',
            show_after_approx: true,
            value: moment().format('YYYY-MM-DD'),
            menu: false,
            locale: 'ja',
            date_format: 'YYYY-MM-DD',
          },
          {
            label: 'Label',
            name: 'date_to',
            type: 'date',
            rules: 'required',
            show_after_approx: false,
            value: moment().format('YYYY-MM-DD'),
            menu: false,
            locale: 'ja',
            range: true,
            range_input: 'date_from',
            date_format: 'YYYY-MM-DD',
          },
        ];
      } else {
        searchFields.value = [];
      }
    };

    // Get color for status chip based on application status
    const chipColor = (status) => {
      if (status === 1) return '#5E94E4'; // Applied
      if (status === 2) return '#EE6C9B'; // Replied
      if (status === 3) return '#60D1CB'; // Accepted
      if (status === 4) return '#A7A7A7'; // Rejected
      if (status === 5) return '#C771B5'; // Declined
      if (status === 6) return '#DC8360'; // No Response
      if (status === 99) return '#666666'; // Other
    };

    // Get status text based on application status code
    const getStatus = (value) => {
      const status = parseInt(value);
      if (status === 1) return '応募済'; // Applied
      if (status === 2) return '応募返信済'; // Replied
      if (status === 3) return '合格'; // Accepted
      if (status === 4) return '不合格'; // Rejected
      if (status === 5) return '辞退'; // Declined
      if (status === 6) return '連絡とれず'; // No Response
      if (status === 99) return 'その他'; // Other
      return '';
    };

    onMounted(() => {});

    const updateTable = async (e) => {
      configuration.value.sort_by =
        e && e.sortBy && e.sortBy.length && e?.sortBy[0]?.key
          ? e?.sortBy[0]?.key
          : 'updated_at';
      configuration.value.sort_by_order =
        e && e.sortBy && e.sortBy.length && e?.sortBy[0]?.order
          ? e?.sortBy[0]?.order
          : 'desc';
      configuration.value.page = typeof e === 'number' ? e : (e?.page ?? 1);
      await generateItems();
    };

    return {
      selection,
      toggleSearch,
      options,
      selectedItem,
      selectedItemStatusNew,
      showStatusAlert,
      statusAlertMessage,
      cancelUpdate,
      items,
      dialog,
      configuration,
      searchFields,
      selectTypeOptions,
      getApplicationCounts,
      getApplicationPagination,
      getHeaders,
      getReasons,
      resultOnTab,
      updateSearchResults,
      updateAdminRead,
      get_reason_text,
      setDecline,
      statusChange,
      updateApi,
      updateTable,
      downloadCsv,
      generateItems,
      resetPagination,
      searchSubmit,
      dateFormat,
      chipColor,
      getStatus,
      setChangedInputType,
      application,
      showDetailApp,
      errorMessages,
      loading,
      setUpdate,
    };
  },
};
</script>

<style scoped>
.pill {
  width: 77px;
  height: 30px;
  border-radius: 30px;
}

.pill-large {
  width: 96px;
  height: 30px;
  border-radius: 30px;
}

.application-table {
  :deep(thead th) {
    padding-left: 16px !important;
    padding-right: 16px !important;
  }

  :deep(tbody tr td) {
    padding-top: 15px !important;
    padding-bottom: 15px !important;

    &:nth-child(2) {
      padding-left: 0 !important;
      padding-right: 0 !important;
    }
  }
}
</style>

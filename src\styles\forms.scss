.v-select__selection-text {
  font-size: 13px !important;
}

.v-field {
  &.v-field--variant-outlined {
    // Default idle state
    .v-field__outline {
      .v-field__outline__start,
      .v-field__outline__end {
        border-color: $light-grey-7d !important; // Idle state border color
      }
    }

    // Focus state
    &:focus-within {
      .v-field__outline {
        .v-field__outline__start,
        .v-field__outline__end {
          border-color: $primary !important; // Focus state border color
        }
      }
    }

    // Error state
    &.v-field--error {
      .v-field__outline {
        .v-field__outline__start,
        .v-field__outline__end {
          border-color: $color-red !important; // Error state border color
        }
      }

      // For progress linear error color
      .v-progress-linear__background,
      .v-progress-linear__buffer,
      .v-progress-linear__indeterminate .long,
      .v-progress-linear__indeterminate .short {
        background-color: $color-red !important;
      }
    }
  }
}

.font-20px {
  font-size: 20px !important;
}

.v-input__details {
  padding: 0 !important;
  padding-inline: 0 !important;
}

.v-text-field .v-input__details {
  padding-inline: 0 !important;
}

.v-text-field input::placeholder {
  font-size: 13px; /* Change to your desired size */
  color: #666; /* Optional: Change placeholder color */
}

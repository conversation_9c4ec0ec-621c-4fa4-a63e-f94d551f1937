<template>
  <div>
    <PageTitle
      v-show="!loading && !$store.getters.getApiProcessingStatus"
      @tab:clicked="getDataFromApi(undefined, $event)"
      :items="{
        title: title,
        subTitle: subTitle,
        buttons: [
          {
            title: '詳細条件検索',
            class: 'bg-white',
            variant: 'outlined',
            action: () => {
              toggleSearch = !toggleSearch;
            },
          },
          {
            title: '新規作成',
            icon: 'mdi-plus-circle',
            action: () => {
              router.push({ name: 'InternshipPostCreate' });
            },
          },
        ],
      }"
    ></PageTitle>
    <SearchArea
      v-if="toggleSearch"
      @toggleSearch="setToggleSearch"
      @searchSubmit="searchSubmit"
      @changedInputType="setChangedInputType"
      @resetForm="getDataFromApi"
      :toggleSearch="toggleSearch"
      :searchFields="searchFields"
      :selectTypeOptions="selectTypeOptions"
      class="mb-4"
    ></SearchArea>
    <DataTable
      v-show="!loading && !$store.getters.getApiProcessingStatus"
      :items="getAllInternship"
      :loading="false"
      :headers="headers"
      :total-records="
        getInternshipPagination ? getInternshipPagination.records_total : 0
      "
      :number-of-pages="
        getInternshipPagination ? getInternshipPagination.total_pages : 0
      "
      @update:options="updateTable"
      @row-clicked="
        router.push({ name: 'InternshipPostEdit', params: { id: $event.id } })
      "
    >
      <template v-slot:[`item.title`]="{ item }">
        <div class="mouse-pointer">
          <div class="text-decoration-none truncate-lines lines-1">
            <v-tooltip location="bottom">
              <template #activator="{ props }">
                <span v-bind="props">
                  {{ item.title || '-' }}
                </span>
              </template>
              <span>{{ item.title || '-' }}</span>
            </v-tooltip>
          </div>
        </div>
      </template>
      <template v-slot:[`item.company_name`]="{ item }">
        {{ item.company ? item.company.name : '-' }}
      </template>
      <template v-slot:[`item.work_category_name`]="{ item }">
        {{ item.work_category ? item.work_category.name : '-' }}
      </template>
      <template v-slot:[`item.public_date`]="{ item }">
        {{ formatDate(item.public_date) }}
      </template>
    </DataTable>
  </div>
</template>

<script>
import { ref, computed, onMounted, defineAsyncComponent } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
const PageTitle = defineAsyncComponent(
  () => import('@/components/ui/PageTitle.vue')
);
const DataTable = defineAsyncComponent(
  () => import('@/components/ui/DataTable.vue')
);
const SearchArea = defineAsyncComponent(
  () => import('@/components/ui/SearchArea.vue')
);
import moment from 'moment';

export default {
  name: 'InternshipPostList',
  components: { DataTable, PageTitle, SearchArea },
  setup() {
    const formatDate = (date) => {
      return date ? moment(date).format('YYYY/MM/DD') : '---- / - / -';
    };
    const store = useStore();
    const router = useRouter();
    const title = ref('求人広告');
    const subTitle = ref('下書き');
    const loading = ref(false);
    const initialLoad = ref(true);
    const selectedTab = ref('N');
    const toggleSearch = ref(false);
    const searchFields = ref([]);
    const selectTypeOptions = ref([
      { id: 'search', name: 'キーワード検索' },
      { id: 'industry', name: '業界' },
      { id: 'work', name: '職種' },
      { id: 'public_date', name: '公開日' },
    ]);

    const headers = ref([
      { title: 'ID', align: 'start', sortable: false, value: 'id' },
      {
        title: '求人タイトル',
        align: 'start',
        sortable: false,
        value: 'title',
      },
      {
        title: '企業名',
        align: 'start',
        sortable: false,
        value: 'company_name',
      },
      {
        title: '職種',
        align: 'center',
        sortable: false,
        width: '10%',
        value: 'work_category_name',
      },
      {
        title: '表示順',
        align: 'center',
        width: '20%',
        sortable: true,
        value: 'display_order',
      },
      {
        title: '公開日',
        align: 'center',
        sortable: true,
        width: '20%',
        value: 'public_date',
      },
      {
        title: '',
        align: 'center',
        sortable: true,
        value: 'favorites_count',
        heartIcon: true,
      },
      {
        title: '',
        align: 'center',
        value: 'applications_count',
        paperPlaneIcon: true,
        sortable: true,
      },
    ]);

    /**
     * Computed properties for accessing Vuex store data
     * Provides reactive access to internship-related data
     */
    const getAllInternship = computed(() => store.getters.getAllInternship);
    const getInternshipPagination = computed(
      () => store.getters.getInternshipPagination
    );
    const getInternshipCounts = computed(
      () => store.getters.getInternshipCounts
    );

    /**
     * Default sorting configuration for the data table
     */
    const sort_by_order = ref('desc');
    const sort_by = ref('created_at');

    /**
     * Fetches internship draft data from API with filtering and pagination
     * @param {Object} e - Optional table options for pagination
     * @param {Object} obj - Additional filter parameters
     */
    const getDataFromApi = async (e = undefined, obj = {}) => {
      loading.value = true;
      let data = {
        sort_by_order: sort_by_order.value,
        sort_by: sort_by.value,
        page: typeof e === 'number' ? e : (e?.page ?? 1),
        paginate: e?.itemsPerPage || 25,
        status: selectedTab.value, // 'N' for active drafts
        draft_or_public: 'draft', // Only show draft posts
      };

      // Merge additional filters with base data
      data = Object.assign({}, obj, data);
      // Remove empty search parameter
      if (data.search === '') {
        delete data.search;
      }

      await store
        .dispatch('INTERNSHIP_GET_ALL', data)
        .then(() => {
          if (initialLoad.value) initialLoad.value = false;
        })
        .finally(() => {
          loading.value = false;
        });
    };

    /**
     * Current page state for pagination
     */
    const page = ref(0);

    /**
     * Handles table updates (sorting, pagination)
     * Updates sort order and fetches new data
     * @param {Object} e - Table update event containing sort and page information
     */
    const updateTable = (e) => {
      // Update sort order (asc/desc)
      sort_by_order.value =
        e && e.sortBy && e.sortBy.length && e?.sortBy[0]?.order
          ? e?.sortBy[0]?.order
          : 'desc';

      // Update sort field
      sort_by.value =
        e && e.sortBy && e.sortBy.length && e?.sortBy[0]?.key
          ? e?.sortBy[0]?.key
          : 'created_at';

      let option = {};
      if (!e.page) {
        option.page = e;
      } else {
        option;
      }

      // Update current page
      page.value = e.page;

      // Fetch data if not initial load
      if (!initialLoad.value) {
        getDataFromApi(option);
      }
    };

    /**
     * Updates search toggle state
     * @param {boolean} value - New toggle state
     */
    const setToggleSearch = (value) => {
      toggleSearch.value = value;
    };

    /**
     * Processes search form submission
     * Collects field values and triggers API call
     * @param {Object} $event - Form submission event with field values
     */
    const searchSubmit = ($event) => {
      let obj = {};
      if ($event.fields.length > 0) {
        $event.fields.forEach((field) => {
          obj[field.name] = field.value;
        });
        getDataFromApi(undefined, obj);
      }
    };

    /**
     * Access to master data from Vuex store
     * Contains business industries and work categories
     */
    const getMasterData = computed(() => store.getters.getMasterData);

    /**
     * Updates search fields based on selected search type
     * Configures appropriate input fields for each search type
     * @param {string} inputSearchType - Type of search (keyword, date, industry, work)
     */
    const setChangedInputType = (inputSearchType) => {
      if (inputSearchType === 'search') {
        // Keyword search configuration
        searchFields.value = [
          {
            label: 'Label',
            name: 'search',
            type: 'text',
            value: '',
            placeholder: '求人ID、求人タイトル、企業名', // Job ID, Title, Company Name
          },
        ];
      } else if (inputSearchType === 'public_date') {
        // Date range search configuration
        searchFields.value = [
          {
            label: 'Label',
            name: 'date_from',
            type: 'date',
            rules: 'required',
            show_after_approx: true,
            value: moment().format('YYYY-MM-DD'),
            menu: false,
            rules: 'required',
            locale: 'ja',
            date_format: 'YYYY-MM-DD',
          },
          {
            label: 'Label',
            name: 'date_to',
            type: 'date',
            rules: 'required',
            show_after_approx: false,
            value: moment().format('YYYY-MM-DD'),
            menu: false,
            locale: 'ja',
            range: true,
            rules: 'required',
            range_input: 'date_from',
            date_format: 'YYYY-MM-DD',
          },
        ];
      } else if (inputSearchType === 'industry') {
        // Industry selection configuration
        searchFields.value = [
          {
            label: 'Label',
            name: 'business_industry_ids',
            type: 'select',
            item_value: 'id',
            rules: 'required',
            item_text: 'name',
            items: getMasterData.value.business_industories,
          },
        ];
      } else if (inputSearchType === 'work') {
        // Work category selection configuration
        searchFields.value = [
          {
            label: 'Label',
            name: 'work_category_ids',
            type: 'select',
            rules: 'required',
            item_value: 'id',
            item_text: 'name',
            items: getMasterData.value.work_categories,
          },
        ];
      } else {
        searchFields.value = [];
      }
    };

    /**
     * Component initialization
     * Fetches initial draft internship data
     */
    onMounted(() => {
      getDataFromApi();
    });

    return {
      title,
      subTitle,
      loading,
      initialLoad,
      selectedTab,
      toggleSearch,
      searchFields,
      selectTypeOptions,
      headers,
      getAllInternship,
      getInternshipPagination,
      getInternshipCounts,
      getDataFromApi,
      updateTable,
      setToggleSearch,
      searchSubmit,
      setChangedInputType,
      router,
      formatDate,
    };
  },
};
</script>

<style lang="scss" scoped>
.internship-title {
  max-width: 210px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
}
</style>

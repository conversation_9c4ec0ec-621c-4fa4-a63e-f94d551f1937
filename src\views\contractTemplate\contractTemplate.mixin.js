import { mapGetters } from 'vuex';

export default {
  name: 'ContractTemplateMixin',
  data() {
    return {
      alertText: '',
      successDialog: false,
      routeName: '',
      userSiteUrl: import.meta.env.VITE_APP_USER_SITE_URL,
      basicInformation: [],
      isDraftOrPublic: 1,
      dialog: {
        type_update_procedure: false,
        saveAsDraft: false,
        deletePost: false,
        preview: false,
      },
    };
  },
  computed: {
    ...mapGetters([
      'getAllCompany',
      'getApiProcessingStatus',
      'getSingleInternship',
      'getAllCompanyUsers',
      'getMasterData',
    ]),
  },
  watch: {
    basicInformation: {
      handler(newFields) {
        const typeUpdateProcedureField = newFields.find(
          (field) => field.name === 'type_update_procedure'
        );
        if (typeUpdateProcedureField) {
          this.handleTypeUpdateProcedureChange(typeUpdateProcedureField.value);
        }
      },
      deep: true,
    },
  },
  methods: {
    getInputsPageData() {
      const data = new FormData();

      this.basicInformation.forEach((field) => {
        if (field.name && field.value) {
          data.append(field.name, field.value);
        }
      });

      return data;
    },
    savePageData(isCreateOrUpdate) {
      this.$refs.observer.validate().then((success) => {
        if (!success) {
          this.scrollToTheFirstErrorInput();
          return;
        }

        let data = this.getInputsPageData();

        let SET_CALL = 'INTERNSHIP_CREATE';
        if (isCreateOrUpdate === 'update') {
          SET_CALL = 'CONTRACT_UPDATE';
          data.append('id', this.$route.params.id);
        }

        if (this.previewedData?.id) {
          SET_CALL = 'CONTRACT_UPDATE';
          data.append('id', this.previewedData.id);
        }
        this.$store
          .dispatch(SET_CALL, data)
          .then((result) => {})
          .catch((error) => {
            if (error?.status === 422) {
              this.$refs.observer.setErrors(
                error?.data?.error?.errors || error?.data?.errors
              );
              this.scrollToTheFirstErrorInput();
            }
          });
      });
    },
    getPageFields() {
      const contractTemplates = [
        { id: null, name: 'KNフォーマット' },
        ...(this.getSingleContract?.contractTemplates || []),
      ];

      this.basicInformation = [
        {
          label: '企業名',
          name: 'company_id',
          type: 'autocomplete',
          placeholder: '内部ID、または企業名フリガナを入力してください',
          row_class: '',
          item_value: 'id',
          item_text: this.getAutoSuggestionText,
          searchable: true,
          search_api: this.searchCompany,
          is_loading: false,
          searched_text: '',
          items: this.getAllCompany,
          value: '',
          rules: 'required',
          requiredChecks: true,
        },
        {
          label: '大学名',
          name: 'education_facility',
          type: 'text',
          placeholder: '',
          value: '',
          rules: '',
          disabled: true,
        },
        {
          label: '契約種別',
          name: 'type_contract',
          type: 'text',
          placeholder: '',
          value: '',
          rules: '',
          disabled: true,
        },
        {
          label: 'テンプレート',
          name: 'contract_template_id',
          type: 'dropdown',
          item_value: 'id',
          item_text: 'name',
          value: 0,
          rules: '',
          items: contractTemplates,
        },
        {
          label: '契約締結日',
          name: 'date_contract_signing',
          type: 'date',
          datePickerType: 'YYYY-MM',
          rules: '',
          show_after_approx: true,
          value: null,
          locale: 'ja',
        },
        {
          label: '事業所所在地',
          name: 'business_address',
          type: 'text',
          placeholder: '事業所所在地',
          value: '',
          rules: '',
        },
        {
          label: '名称',
          name: 'contract_name',
          type: 'text',
          placeholder: '',
          rules: '',
          value: '',
        },
        {
          label: '使用者職氏名',
          name: 'employee_title_name',
          type: 'text',
          placeholder: '',
          rules: '',
          value: '',
        },
        {
          label: '雇用期間',
          name: 'type_employment_period',
          type: 'dropdown',
          item_value: 'id',
          item_text: 'name',
          value: 0,
          rules: '',
          items: this.getMasterData.contract_employment_type,
        },
        {
          label: '雇入れ日',
          name: 'date_employment_start',
          datePickerType: 'YYYY-MM',
          rules: '',
          show_after_approx: true,
          value: null,
          type: 'date',
          locale: 'ja',
        },
        {
          label: '雇用期間',
          name: 'date_employment_end',
          datePickerType: 'YYYY-MM',
          rules: '',
          show_after_approx: true,
          value: null,
          type: 'date',
          locale: 'ja',
        },
        {
          label: '契約更新可否フラグ',
          name: 'type_update_procedure',
          type: 'dropdown',
          item_value: 'id',
          item_text: 'name',
          value: 0,
          rules: '',
          items: this.getMasterData.contract_update_procedure_type,
        },
        {
          label: '従事すべき業務の内容',
          name: 'job_detail',
          type: 'text',
          placeholder: '',
          rules: 'max:1000',
          value: '',
        },
        {
          label: '賃金',
          name: 'wage',
          type: 'text',
          placeholder: '入力してください',
          rules: 'integer',
          value: '',
        },
        {
          label: '労災保険',
          name: 'flg_insurance_industrial_accident_compensation',
          type: 'checkbox',
          placeholder: '',
          value: true,
          rules: '',
        },
        {
          label: '雇用保険',
          name: 'flg_insurance_unemployment',
          type: 'checkbox',
          placeholder: '',
          value: true,
          rules: '',
        },
        {
          label: '健康保険',
          name: 'flg_insurance_public_health',
          type: 'checkbox',
          placeholder: '',
          value: true,
          rules: '',
        },
        {
          label: '厚生年金保険',
          name: 'flg_insurance_employee_pension',
          type: 'checkbox',
          placeholder: '',
          value: true,
          rules: '',
        },
        {
          label: '担当者職氏名',
          name: 'consultation_user_title_name',
          type: 'text',
          placeholder: '',
          rules: '',
          value: '',
        },
        {
          label: '電子メール',
          name: 'consultation_user_mail_address',
          type: 'text',
          placeholder: '',
          rules: '',
          value: '',
        },
        {
          label: '電話',
          name: 'consultation_user_tel',
          type: 'text',
          placeholder: '',
          rules: '',
          value: '',
        },
      ];
    },
    handleTypeUpdateProcedureChange(value) {
      if (value === 1) {
        // this.dialog.type_update_procedure = true
      }
    },
  },
};

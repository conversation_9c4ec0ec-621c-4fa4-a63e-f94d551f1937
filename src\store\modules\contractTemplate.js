import axios from '@/services/api';

import {
  GET_ALL_CONTRACT_TEMPLATES,
  GET_ALL_CONTRACT_TEMPLATES_SUCCESS,
  CREATE_CONTRACT_TEMPLATES,
  GET_SINGLE_CONTRACT_TEMPLATES,
  GET_SINGLE_CONTRACT_TEMPLATES_SUCCESS,
  UPDATE_CONTRACT_TEMPLATES,
} from '@/store/actions/contractTemplate';

function initialState() {
  return {
    status: '',
    contractTemplates: [],
    singleContractTemplate: {},
  };
}

const state = initialState();

const getters = {
  getAllContractTemplates: (state) => state.contractTemplates,
  getSingleContractTemplate: (state) => state.singleContractTemplate,
};

const actions = {
  [GET_ALL_CONTRACT_TEMPLATES]: ({ commit, dispatch }) => {
    dispatch('API_PROCESSING', true, { root: true });
    return new Promise((resolve, reject) => {
      axios
        .get('/admin/contract-template')
        .then((response) => {
          commit('GET_ALL_CONTRACT_TEMPLATES_SUCCESS', response.data);
          dispatch('API_PROCESSING', false, { root: true });
          resolve(response);
        })
        .catch((error) => {
          reject(error);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  [GET_SINGLE_CONTRACT_TEMPLATES]: ({ commit, dispatch }, params) => {
    dispatch('API_PROCESSING', true, { root: true });
    return new Promise((resolve, reject) => {
      axios
        .get(`/admin/contract-template/${params}`)
        .then((response) => {
          commit('GET_SINGLE_CONTRACT_TEMPLATES_SUCCESS', response.data);
          dispatch('API_PROCESSING', false, { root: true });
          resolve(response);
        })
        .catch((error) => {
          reject(error);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  [CREATE_CONTRACT_TEMPLATES]: ({ commit, dispatch }, params) => {
    dispatch('API_PROCESSING', true, { root: true });
    return new Promise((resolve, reject) => {
      axios
        .post('/admin/contract-template', params)
        .then((response) => {
          commit('GET_ALL_CONTRACT_TEMPLATES_SUCCESS', response.data);
          dispatch('API_PROCESSING', false, { root: true });
          resolve(response);
        })
        .catch((error) => {
          reject(error);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  [UPDATE_CONTRACT_TEMPLATES]: ({ commit, dispatch }, params) => {
    dispatch('API_PROCESSING', true, { root: true });
    return new Promise((resolve, reject) => {
      axios
        .patch(`/admin/contract-template/${params.id}`, params)
        .then((response) => {
          commit('GET_SINGLE_CONTRACT_TEMPLATES_SUCCESS', response.data);
          dispatch('API_PROCESSING', false, { root: true });
          resolve(response);
        })
        .catch((error) => {
          reject(error);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },
};

const mutations = {
  [GET_ALL_CONTRACT_TEMPLATES_SUCCESS]: (state, payload) => {
    state.contractTemplates = payload.contractTemplates;
  },
  [GET_SINGLE_CONTRACT_TEMPLATES_SUCCESS]: (state, payload) => {
    state.singleContractTemplate = payload.contractTemplate;
  },
};

export default {
  state,
  getters,
  actions,
  mutations,
};

import { createStore } from 'vuex'; // Import `createStore` for Vue 3
import createPersistedState from 'vuex-persistedstate';
import auth from './modules/auth';
import corporate from './modules/corporate';
import application from './modules/application';
import feedback from './modules/feedback';
import feedbackUnaddress from './modules/feedback-unaddress';
import plans from './modules/plans';
import users from './modules/users';
import postalcode from './modules/postalcode';
import notifications from './modules/notifications';
import faqs from './modules/faqs';
import admins from './modules/admins';
import dashboards from './modules/dashboards';
import chats from './modules/chats';
import master from './modules/master';
import contract from './modules/contract';
import contract_admin from './modules/contract_admin';
import internship from './modules/internship';
import company from './modules/company';
import facilities from './modules/educational_facilities';
import internshipFeatures from './modules/internship_features';
import internshipOccupation from './modules/internship_occupation';
import student from './modules/student';
import mediaPosts from './modules/media_posts';
import mediaTags from './modules/media_tags';
import interestedJobs from './modules/interested_job';
import companyUser from '@/store/modules/companyUser';
import internship_student from '@/store/modules/internship_student';
import contractTemplate from '@/store/modules/contractTemplate';
import analysisFromOther from '@/store/modules/analysis-from-other';
import notice from '@/store/modules/notice';

const getDefaultState = () => {
  return {
    apiProcessing: false,
    apiProcessingDetailApp: false,
    alert: false,
    drawer: true,
    alertText: '',
    alertSuccess: false,
    errors: {},
    studentCurrentTab: null,
  };
};

export default createStore({
  state: getDefaultState(),

  mutations: {
    toggleDrawer(state) {
      state.drawer = !state.drawer;
    },
    showAlert(state, { text, successStatus }) {
      state.alert = true;
      state.alertText = text;
      state.alertSuccess = successStatus;
    },
    hideAlert(state) {
      state.alert = false;
    },
    setApiProcessing(state, payload) {
      setTimeout(() => {
        state.apiProcessing = payload;
      }, 300);
    },
    setApiProcessingGetDetailApp(state, payload) {
      state.apiProcessingDetailApp = payload;
    },
    setFormErrors(state, payload) {
      state.errors = payload;
    },
    clearFormErrors(state) {
      state.errors = {};
    },
    setStudentCurrentTab(state, payload) {
      state.studentCurrentTab = payload;
    },
  },

  actions: {
    TOGGLE_DRAWER({ commit }) {
      commit('toggleDrawer');
    },
    API_PROCESSING({ commit }, payload) {
      commit('setApiProcessing', payload);
    },
    SHOW_ALERT({ commit }, payload) {
      commit('showAlert', payload);
    },
    SET_STUDENT_CURRENT_TAB({ commit }, payload) {
      commit('setStudentCurrentTab', payload);
    },
    API_PROCESSING_GET_DETAIL_APP({ commit }, payload) {
      console.log(payload);
      commit('setApiProcessingGetDetailApp', payload);
    },
  },

  getters: {
    DRAWER_STATE(state) {
      return state.drawer;
    },
    getApiProcessingStatus: (state) => state.apiProcessing,
    getApiProcessingDetailAppStatus: (state) => state.apiProcessingDetailApp,
    getAlertStatus: (state) => state.alert,
    getAlertText: (state) => state.alertText,
    getAlertSuccess: (state) => state.alertSuccess,
    getErrors: (state) => state.errors,
    getStudentCurrentTab: (state) => state.studentCurrentTab,
  },

  modules: {
    auth,
    corporate,
    plans,
    users,
    postalcode,
    notifications,
    faqs,
    admins,
    dashboards,
    chats,
    master,
    contract,
    internship,
    company,
    facilities,
    student,
    internshipFeatures,
    internshipOccupation,
    mediaPosts,
    mediaTags,
    interestedJobs,
    application,
    feedback,
    feedbackUnaddress,
    companyUser,
    contract_admin,
    contractTemplate,
    internship_student,
    analysisFromOther,
    notice,
  },

  plugins: [createPersistedState()],
});

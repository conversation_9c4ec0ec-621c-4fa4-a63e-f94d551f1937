@use 'variables'; // Import the variables module
@import url(https://fonts.googleapis.com/css2?family=Time+New+Roman:wght@300;400;500;700&family=Noto+Sans+JP:wght@300;400;500;700&display=swap);

$font-family: 'Noto Sans JP';

.v-application {
  [class*='text-'] {
    font-family: $font-family, sans-serif !important;
  }
  font-family: $font-family, sans-serif !important;
}

body,
html,
.v-application {
  font-family: 'Noto Sans JP';
  overflow: hidden !important;
  color: #000000de; /* Use the Vuetify theme’s text color */
}
* {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  &::-webkit-scrollbar-track {
    background: #fff;
    border-radius: 10px;
  }
  &::-webkit-scrollbar-thumb {
    background: #e4e4e4;
    border-radius: 10px;
  }
  &::-webkit-scrollbar-thumb:hover {
    background: #bebaba;
  }
}
.cancel-mouse-event {
  pointer-events: none;
}
.m-auto {
  margin: auto !important;
}
.mouse-pointer:hover {
  cursor: pointer !important;
}
.overflow-y-auto {
  overflow-y: auto !important;
}
.mobile-view .page-head {
  font-size: 24px;
  font-weight: 400;
  text-align: center;
}
.text-b8b8b8 {
  color: #b8b8b8;
}
.mobile-view {
  // max-width: 600px;
  background-color: #ffffff;
  // border: 1px solid darkgray;
  // width: 600px;
}

.mobile-view .page-content p {
  font-weight: 500;
  font-size: 15px;
}

.v-application {
  .v-select .v-list .v-list-item--no-data {
    display: none !important;
  }
  .v-text-field .v-field {
    cursor: text;
    font-size: 14px !important;
  }

  .v-checkbox .v-selection-control {
    min-height: 10px !important;
  }

  .v-field {
    &.v-field--variant-outlined {
      // Default idle state
      .v-field__outline {
        .v-field__outline__start,
        .v-field__outline__end {
          border-color: variables.$light-grey !important; // Idle state border color
          opacity: 1 !important;
        }
      }

      // Focus state
      &:focus-within {
        .v-field__outline {
          .v-field__outline__start,
          .v-field__outline__end {
            // border-color: variables.$primary !important; // Focus state border color
          }
        }
      }

      // Error state
      &.v-field--error {
        .v-field__outline {
          .v-field__outline__start,
          .v-field__outline__end {
            border-color: variables.$color-red !important; // Error state border color
          }
        }

        // For progress linear error color
        .v-progress-linear__background,
        .v-progress-linear__buffer,
        .v-progress-linear__indeterminate .long,
        .v-progress-linear__indeterminate .short {
          background-color: variables.$color-red !important;
        }
      }
    }
  }

  .font-20px {
    font-size: 20px !important;
  }

  .v-input__details {
    padding: 0 !important;
    padding-inline: 0 !important;
  }

  .v-text-field .v-input__details {
    padding-inline: 0 !important;
  }

  .v-text-field input::placeholder {
    font-size: 14px; /* Change to your desired size */
    color: variables.$light-grey !important; // Idle state border color
    opacity: 1 !important;
  }

  .v-input--density-compact .v-field__input {
    padding-top: 9.5px !important;
  }

  .px-20 {
    padding-left: 80px;
    padding-right: 80px;
  }
  .mt-20 {
    margin-top: 80px;
  }
  &.main-app-container {
    .truncate-lines {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
      &.lines-1 {
        -webkit-line-clamp: 1;
      }
      &.lines-2 {
        -webkit-line-clamp: 2;
      }
      &.lines-3 {
        -webkit-line-clamp: 3;
      }
      &.lines-4 {
        -webkit-line-clamp: 4;
      }
      &.lines-5 {
        -webkit-line-clamp: 5;
      }
    }
    .position-relative {
      position: relative;
    }
    .position-absolute {
      position: absolute;
    }
    .bg-e5e5e5 {
      background-color: #e5e5e5 !important;
    }
    .bg-c3 {
      background-color: #c3c3c3 !important;
    }
    .bg-green {
      background-color: variables.$color-green !important;
    }
    .bg-input-disabled .v-input__control {
      background-color: #eef0f0!important;
      pointer-events: none!important;
      opacity: 1!important;
    }
    .bg-input-disabled-text-area .v-input__control {
      background-color: #EEF0F0!important;
      opacity: 1!important;
    }
    .v-field {
      color: #333333!important;
      opacity: 1!important;

    }
    .bg-white {
      background-color: white !important;
    }
    .color-violet {
      color: variables.$color-violet !important;
    }
    .text-ff862f {
      color: #ff862f !important;
    }

    .text-3f74c2 {
      color: #3f74c2 !important;
    }
    .text-delete {
      color: #e14d56;
    }
    .bg-EE6C9B26 {
      background-color: #ee6c9b26;
    }
    .text-97 {
      color: gray;
    }
    .text-8E8E8E {
      color: #8e8e8e;
    }
    .text-F1575F {
      color: #f1575f;
    }
    .text-529AEE {
      color: #529aee;
    }
    .top-12 {
      top: 12.5%;
    }
    .full-width {
      width: 100%;
    }
    .full-height {
      height: 100%;
    }
    .text-green {
      color: variables.$color-green !important;
    }
    .border-green {
      border: 1px solid variables.$color-green !important;
    }
    .bg-dark-green {
      background-color: variables.$color-dark-green !important;
    }
    .text-dark-green {
      color: variables.$color-dark-green !important;
    }
    .border-dark-green {
      border: 1px solid variables.$color-dark-green !important;
    }
    .bg-red {
      background-color: variables.$color-red !important;
    }
    .bg-red-light {
      background-color: variables.$color-red-light !important;
    }
    .text-red,
    .red--text {
      color: variables.$color-red !important;
    }
    .white--text {
      color: variables.$color-white !important;
    }
    .text-333 {
      color: variables.$color-secondary-333 !important;
    }
    .text-grey {
      color: variables.$light-grey !important;
    }
    .text-7d {
      color: variables.$light-grey-7d !important;
    }
    .text-blue {
      color: variables.$color-blue !important;
    }
    .text-black {
      color: #000000de !important;
    }
    .text-8e8e {
      color: variables.$color-8e8e !important;
    }
    .text-light-dark {
      color: variables.$color-light-dark !important;
    }
    .bg-blue {
      background-color: variables.$color-blue !important;
    }
    .border-blue {
      border: 1px solid variables.$color-blue !important;
    }

    .text-666 {
      color: variables.$light-grey-666;
    }

    .text-8e {
      color: #8e8e8e !important;
    }

    .text-dark-blue {
      color: variables.$color-dark-blue !important;
    }
    .bg-fffbf0 {
      background-color: #fffbf0 !important;
    }
    .bg-dark-blue {
      background-color: variables.$color-dark-blue !important;
    }
    .border-dark-blue {
      border: 1px solid variables.$color-dark-blue !important;
    }
    .border-red {
      border: 1px solid variables.$color-red !important;
    }
    .line-height-1 {
      line-height: 1;
    }
    .rounded-16 {
      border-radius: 16px !important;
    }
    .rounded-5 {
      border-radius: 5px !important;
    }
    .heading-text {
      h2 {
        display: flex;
        align-items: center;
        .line {
          width: 12px;
          background-color: variables.$color-secondary-333;
          height: 2px;
          display: inline-block;
          margin: 0 10px;
        }
      }
    }
    .small-line {
      width: 12px;
      background-color: variables.$color-secondary-333;
      height: 2px;
      display: inline-block;
    }
    .v-chip:not(.v-chip--active) {
      background-color: variables.$light-primary;
      color: variables.$color-secondary-333;
      .mdi-close-circle {
        color: variables.$primary;
      }
    }
    .v-autocomplete__content,
    .v-select-list {
      .primary--text {
        background-color: variables.$light-primary;
        color: variables.$color-secondary-333 !important;
      }
      .v-list-item--active:hover::before,
      .theme--light.v-list-item--active::before {
        opacity: 0 !important;
      }
      .v-icon {
        &.primary--text {
          background-color: transparent !important;
          color: variables.$primary !important;
        }
      }
    }
    .error--text {
      color: variables.$error !important;
      caret-color: variables.$error !important;
    }
    /* Change Autocomplete styles in Chrome*/
    input:-webkit-autofill,
    input:-webkit-autofill:hover,
    input:-webkit-autofill:focus,
    textarea:-webkit-autofill,
    textarea:-webkit-autofill:hover,
    textarea:-webkit-autofill:focus,
    select:-webkit-autofill,
    select:-webkit-autofill:hover,
    select:-webkit-autofill:focus {
      -webkit-text-fill-color: #333;
      box-shadow: 0 0 0px 1000px #fff inset;
      -webkit-box-shadow: 0 0 0px 1000px #fff inset;
      transition: background-color 5000s ease-in-out 0s;
    }
    .v-input {
      font-size: 14px !important;
      &.error--text {
        border-color: variables.$color-red !important;
      }
    }
    .v-messages {
      text-align: left !important; /* Align the whole message block to the left */
    }

    .v-messages__message {
      text-align: left !important; /* Align individual messages to the left */
      color: #e87c64 !important;
    }

    .border-bottom-0 {
      border-bottom: 0 !important;
    }
    .border-top-0 {
      border-top: 0 !important;
    }
    .border-left-0 {
      border-left: 0 !important;
    }
    .border-right-0 {
      border-right: 0 !important;
    }
    .border {
      border: 1px solid variables.$text-grey;
    }
    .searchable-box {
      .card-title {
        border-bottom: 1px solid variables.$light-grey;
      }
    }
    .visibility-hidden {
      visibility: hidden;
    }
    .v-picker__body {
      & > * {
        margin: 18px;
        border: 1px solid variables.$text-grey;
        border-radius: 4px;
        width: 88%;
      }
      table {
        thead {
          tr {
            th {
              color: variables.$text-grey;
              &:nth-child(1) {
                color: variables.$color-red;
              }
              &:nth-child(7) {
                color: variables.$color-blue;
              }
            }
          }
        }
      }
    }

    .v-data-table tr td:first-child,
    .v-data-table tr th:first-child {
      border-bottom: none;
    }

    .v-data-table tr td:last-child,
    .v-data-table tr th:last-child {
      border-bottom: none;
    }

    .v-data-table-rows-no-data {
      color: rgba(0, 0, 0, 0.38) !important;
    }

    .v-data-table {
      box-shadow: variables.$card-shadow;
      border-radius: 5px;
      table {
        tbody {
          tr {
            td {
              font-weight: 400;
              height: 48px !important;
            }
            &:last-child {
              td {
                &:first-child {
                  border-bottom-left-radius: 5px;
                }
                &:last-child {
                  border-bottom-right-radius: 5px;
                }
              }
            }
          }
          tr:hover {
            background-color: variables.$light-primary !important;
          }
        }
        thead {
          tr {
            background-color: variables.$primary;
            th {
              font-weight: 500;
              &.desc {
                .swap-sorting-icon {
                  transform: rotate(180deg);
                  transition: all 0.3s ease-in-out;
                }
              }
              &:last-child {
                border-top-right-radius: 5px;
              }
              &:first-child {
                border-top-left-radius: 5px;
              }
            }
          }
        }
      }
    }
    .w-100 {
      width: 100%;
    }
    .no-background-hover {
      cursor: default !important;
      &:hover {
        &::before {
          background-color: transparent !important;
          opacity: 0;
        }
      }
    }
    .transparent {
      &:before,
      &:after {
        display: none;
      }
    }
    .auto-block-images {
      width: auto;
      height: 100%;
      margin: auto;
      display: block;
      max-width: 100%;
    }
    .no-shadow {
      box-shadow: none !important;
      & > * {
        box-shadow: none !important;
        &.v-card {
          box-shadow: none !important;
        }
      }
    }
    .cursor-pointer {
      cursor: pointer !important;
    }
    .theme--light {
      &.v-pagination {
        .v-pagination__item {
          background-color: transparent !important;
          color: variables.$color-secondary-333 !important;
          box-shadow: none !important;
          font-size: 14px !important;
          font-weight: 500 !important;
        }
        .v-pagination__navigation {
          background-color: transparent !important;
          color: variables.$color-secondary-333 !important;
          box-shadow: none !important;
          font-size: 14px !important;
          font-weight: 500 !important;
        }
        .v-pagination__item--active {
          color: #fff !important;
          font-size: 14px !important;
          font-weight: 500 !important;
          background-color: variables.$primary !important;
          box-shadow: none !important;
        }
      }
    }

    .page-title {
      font-size: 2.4rem;
      color: variables.$text-grey;
      margin-bottom: 0;
    }
    .v-card {
      box-shadow: variables.$card-shadow !important;
      .v-card__title p {
        color: variables.$card-title-color;
        line-height: 1.33;
        margin: 0;
        font-size: 20px;
        font-weight: 400;
      }
      .card-dark-grey {
        color: variables.$title-grey !important;
      }
      .card-light-grey {
        color: variables.$text-color !important;
      }
    }

    h1 {
      font-size: variables.$h1-font-size;
      font-weight: 400;
      margin-bottom: 16px;
      line-height: 1;
    }
    h2 {
      font-size: variables.$h2-font-size;
      font-weight: 400;
      margin-bottom: 16px;
      line-height: 1;
    }
    h3 {
      font-size: variables.$h3-font-size;
      font-weight: 400;
      margin-bottom: 16px;
      line-height: 1;
    }
    h4 {
      font-size: variables.$h4-font-size;
      font-weight: 400;
      margin-bottom: 16px;
      line-height: 1;
    }
    h5 {
      font-size: variables.$h5-font-size;
      font-weight: 400;
      margin-bottom: 16px;
    }
    h6 {
      font-size: variables.$h6-font-size;
      font-weight: 400;
      margin-bottom: 16px;
    }
    .bg-e14d56 {
      background-color: #e14d56 !important;
    }
    .bg-f9f9f9 {
      background-color: #f9f9f9;
    }
    .btn-pink {
      background: linear-gradient(135deg, #ff7283 0%, #fa7c92 100%) !important;
      box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25) !important;
      border-radius: 3px !important;
    }
    .btn-primary {
      background: variables.$primary !important;
    }
    .bg-primary {
      background: variables.$primary !important;
    }
    .text-primary {
      color: variables.$primary !important;
    }
    .font-Noto-Sans {
      font-family: 'Noto Sans JP';
    }
    .font-time-new-roman {
      font-family: 'Time New Roman';
      font-family: 500;
    }
    .fw-900 {
      font-weight: 900;
    }
    .fw-800 {
      font-weight: 800;
    }
    .fw-700 {
      font-weight: 700;
    }
    .fw-600 {
      font-weight: 600;
    }
    .fw-500 {
      font-weight: 500;
    }
    .fw-400 {
      font-weight: 400;
    }

    .font-9px {
      font-size: 9px !important;
    }
    .font-10px {
      font-size: 10px !important;
    }
    .font-11px {
      font-size: 11px !important;
    }
    .font-12px {
      font-size: 12px !important;
    }
    .font-13px {
      font-size: 13px !important;
    }
    .font-14px {
      font-size: 14px !important;
    }
    .font-15px {
      font-size: 14px !important;
    }
    .font-16px {
      font-size: 16px !important;
    }
    .font-18px {
      font-size: 18px !important;
    }
    .font-20px {
      font-size: 20px !important;
    }
    .font-22px {
      font-size: 22px !important;
    }
    .font-24px {
      font-size: 24px !important;
    }
    .font-25px {
      font-size: 25px !important;
    }
    .font-28px {
      font-size: 28px !important;
    }
    .font-30px {
      font-size: 30px !important;
    }
    .font-32px {
      font-size: 32px !important;
    }
    .font-36px {
      font-size: 36px !important;
    }
    .font-38px {
      font-size: 38px !important;
    }
    .font-40px {
      font-size: 40px !important;
    }
    .font-48px {
      font-size: 48px !important;
    }
    .font-64px {
      font-size: 64px !important;
      line-height: 88px !important;
    }
    .font-96px {
      font-size: 96px !important;
    }
    textarea {
      margin-right: 8px;
    }
    .text-E87C64 {
      color: #e87c64 !important;
    }
    .v-btn[type='button'] {
      display: inline-flex !important;
      align-items: center !important;
      justify-content: center !important;
      &:before,
      &:after {
        display: none;
      }
      .v-btn__content {
        line-height: 1.1;
        padding: 0 0 1px;
      }
    }
  }
}
.cancel-button {
  background: #fff !important;
  color: #12aba3 !important;
  border: 1px solid #12aba3;
}
.text-decoration-none {
  text-decoration: none !important;
}
@media screen and (min-width: 1904px) {
  .container {
    max-width: 1500px;
  }
}
.v-list-item-title {
  line-height: 1.5rem !important;
}
.v-menu__content.theme--light.menuable__content__active.v-autocomplete__content {
  z-index: 0 !important;
}
// ::v-deep thead th {
//   height: 48px !important; /* Ensure it applies even if there are other conflicting styles */
// }

.text-ff862f {
  color: #ff862f !important;
}

.text-E87C64 {
  color: #e87c64 !important;
}

.v-container {
  padding: 12px !important;
}

.v-label {
  opacity: 1 !important;
}

.text-1f2020 {
  color: #1f2020 !important;
}

.text-00000099 {
  color: #00000099 !important;
}

.btn-primary {
  background: variables.$primary !important;
}
.bg-primary {
  background: variables.$primary !important;
}
.text-primary {
  color: variables.$primary !important;
}
.font-Noto-Sans {
  font-family: 'Noto Sans JP';
}
.font-time-new-roman {
  font-family: 'Time New Roman';
  font-family: 500;
}
.fw-900 {
  font-weight: 900;
}
.fw-800 {
  font-weight: 800;
}
.fw-700 {
  font-weight: 700;
}
.fw-600 {
  font-weight: 600;
}
.fw-500 {
  font-weight: 500;
}
.fw-400 {
  font-weight: 400;
}

.font-9px {
  font-size: 9px !important;
}
.font-10px {
  font-size: 10px !important;
}
.font-11px {
  font-size: 11px !important;
}
.font-12px {
  font-size: 12px !important;
}
.font-13px {
  font-size: 13px !important;
}
.font-14px {
  font-size: 14px !important;
}
.font-15px {
  font-size: 14px !important;
}
.font-16px {
  font-size: 16px !important;
}
.font-18px {
  font-size: 18px !important;
}
.font-20px {
  font-size: 20px !important;
}
.font-22px {
  font-size: 22px !important;
}
.font-24px {
  font-size: 24px !important;
}
.font-25px {
  font-size: 25px !important;
}
.font-28px {
  font-size: 28px !important;
}
.font-30px {
  font-size: 30px !important;
}
.font-32px {
  font-size: 32px !important;
}
.font-36px {
  font-size: 36px !important;
}
.font-38px {
  font-size: 38px !important;
}
.font-40px {
  font-size: 40px !important;
}
.font-48px {
  font-size: 48px !important;
}
.font-64px {
  font-size: 64px !important;
  line-height: 88px !important;
}
.font-96px {
  font-size: 96px !important;
}

.error--text {
  color: variables.$error !important;
  caret-color: variables.$error !important;
}

.v-expansion-panel-title__overlay {
  background: transparent !important;
}

.v-expansion-panel-text__wrapper {
  padding: 0px !important;
}

.v-expansion-panel-title {
  min-height: 30px !important;
}

.v-text-field .v-input__details {
  padding-inline: 0 !important;
}

.text-introduction {
  font-family: inherit; /* Use the same font as the rest of your app */
  white-space: pre-wrap; /* Ensures text wraps */
}

.v-select .v-field .v-field__input > input {
  top: 10px!important;
}

.rotate-icon {
  margin-bottom: 6px;
  transform: rotate(180deg) translateY(-5px);
}
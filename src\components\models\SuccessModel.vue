<template>
  <!-- Success Dialog -->
  <v-dialog
    v-model="selfDialog"
    persistent
    max-width="775"
    content-class="no-shadow"
  >
    <template v-slot:default>
      <v-card class="pa-15">
        <v-card-text class="pb-5">
          <template v-if="Array.isArray(text)">
            <div
              v-for="(tt, index) in text"
              :key="index"
              class="my-1 text-center d-flex justify-center align-center"
              :class="[index == 0 ? 'font-20px mb-2' : 'font-20px']"
              v-html="tt"
            ></div>
          </template>
          <template v-else>
            <div
              class="my-1 d-flex justify-center align-center font-20px"
              v-html="text"
            ></div>
          </template>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn
            variant="elevated"
            class="bg-primary white--text"
            min-width="236"
            @click="handleButtonClick"
          >
            {{ buttonText }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </template>
  </v-dialog>
  <!-- Success Dialog end -->
</template>

<script>
import { ref, computed, watch } from 'vue';
import { useRouter } from 'vue-router';

export default {
  name: 'SuccessModel',
  props: {
    dialog: {
      type: Boolean,
      default: false,
    },
    text: {
      type: [String, Array],
      default: '承認された登録',
    },
    buttonText: {
      type: String,
      default: 'へ戻る',
    },
    routeName: {
      type: String,
      default: '',
    },
    specificId: {
      type: Number,
      default: null,
    },
  },
  setup(props, { emit }) {
    /**
     * Router Instance
     * Used for programmatic navigation after success
     */
    const router = useRouter();

    /**
     * Dialog State Management
     * Local reactive state for dialog visibility
     */
    const selfDialog = ref(props.dialog);

    /**
     * Dialog Control
     * Two-way binding for dialog visibility with parent component
     * Handles synchronization between local and parent dialog states
     */
    const dialogComputed = computed({
      get() {
        return selfDialog.value; // Get local dialog state
      },
      set(value) {
        emit('closeModel', true); // Notify parent of closure
        selfDialog.value = value; // Update local state
      },
    });

    /**
     * Dialog State Synchronization
     * Watches for changes in parent dialog prop
     * Updates local state to match parent state
     */
    watch(
      () => props.dialog,
      (newVal) => {
        selfDialog.value = newVal; // Sync local state with parent
      }
    );

    /**
     * Success Action Handler
     * Manages navigation and dialog closure after successful action
     * If routeName is provided, navigates to specified route
     * Otherwise, simply closes the dialog
     */
    const handleButtonClick = () => {
      if (props.routeName) {
        router.push({
          name: props.routeName,
          ...(props.specificId ? { params: { id: props.specificId } } : {}),
        });
      }
    };

    return {
      selfDialog: dialogComputed,
      handleButtonClick,
    };
  },
};
</script>

<style scoped></style>

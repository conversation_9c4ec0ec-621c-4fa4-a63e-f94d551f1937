import axios from '@/services/api';

function initialState() {
  return {
    status: '',
    feedbackUnaddress: [],
    singleFeedbackUnaddress: {},
    feedbackUnaddressCounts: {},
    feedbackUnaddressPagination: 0,
    feedbackUnaddressDraft: false,
  };
}

const state = initialState();

const getters = {
  getFeedbackUnaddressStatus: (state) => state.status,
  getAllFeedbackUnaddress: (state) => state.feedbackUnaddress,
  getSingleFeedbackUnaddress: (state) => state.singleFeedbackUnaddress,
  getFeedbackUnaddressCounts: (state) => state.feedbackUnaddressCounts,
  getFeedbackUnaddressPagination: (state) => state.feedbackUnaddressPagination,
  getFeedbackUnaddressDraft: (state) => state.feedbackUnaddressDraft,
};

const actions = {
  ['FEEDBACK_UNADDRESS_GET_ALL']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('FEEDBACK_UNADDRESS_REQUEST');
      axios
        .get(`/admin/unadddressed-feedback`, { params })
        .then((response) => {
          let data = response.data;
          commit('FEEDBACK_UNADDRESS_GET_ALL_SUCCESS', { data });

          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['FEEDBACK_UNADDRESS_GET']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('FEEDBACK_UNADDRESS_REQUEST');
      axios
        .get(`/admin/unadddressed-feedback/${params.id}`)
        .then((response) => {
          let data = response.data;
          commit('FEEDBACK_UNADDRESS_GET_SUCCESS', { data });

          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['FEEDBACK_UNADDRESS_CREATE']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('FEEDBACK_UNADDRESS_REQUEST');
      axios
        .post(`/admin/unadddressed-feedback`, params)
        .then((response) => {
          resolve(response);
          dispatch('GET_COUNTS_DATA');
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['FEEDBACK_UNADDRESS_UPDATE']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('FEEDBACK_UNADDRESS_REQUEST');
      axios
        .put(`/admin/unadddressed-feedback/${params.id}`, params)
        .then((response) => {
          resolve(response);
          dispatch('GET_COUNTS_DATA');
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['FEEDBACK_UNADDRESS_DELETE']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('FEEDBACK_UNADDRESS_REQUEST');
      axios
        .delete(`/admin/unadddressed-feedback/${params.id}`, params)
        .then((response) => {
          resolve(response);
          dispatch('GET_COUNTS_DATA');
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['FEEDBACK_UNADDRESS_STATUS_UPDATE']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      axios
        .post(`/admin/unadddressed-feedback-status/${params.id}`, params)
        .then((response) => {
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['FEEDBACK_UNADDRESS_EXPORT_CSV']: ({ commit, dispatch }) => {
    dispatch('API_PROCESSING', true, { root: true });
    return new Promise((resolve, reject) => {
      axios
        .get('/admin/export/unadddressed-feedback')
        .then((response) => {
          commit(
            'FEEDBACK_UNADDRESS_EXPORT_CSV_SUCCESS',
            response.data.data.csv
          );
          resolve(response);
        })
        .catch((error) => {
          reject(error);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },
};

const mutations = {
  ['FEEDBACK_UNADDRESS_DRAFT_UPDATE']: (state, payload) => {
    state.feedbackUnaddressDraft = payload;
  },
  ['FEEDBACK_UNADDRESS_REQUEST']: (state) => {
    state.status = 'loading';
  },

  ['FEEDBACK_UNADDRESS_GET_ALL_SUCCESS']: (state, { data }) => {
    state.status = 'success';
    state.feedbackUnaddress = data.data.data;
    state.feedbackUnaddressPagination = data.pagination;
    state.feedbackUnaddressCounts = data.counts;
  },

  ['FEEDBACK_UNADDRESS_GET_SUCCESS']: (state, { data }) => {
    state.status = 'success';
    state.singleFeedbackUnaddress = data.data.data;
  },

  ['FEEDBACK_UNADDRESS_EXPORT_CSV_SUCCESS']: (state, payload) => {
    state.csvData = payload;
  },
};

export default {
  state,
  getters,
  actions,
  mutations,
};

<template>
  <div class="doughnut-main-blk align-center justify-center mt-10">
    <div class="d-flex flex-column align-center justify-center">
      <div class="chart-parent mb-12">
        <canvas ref="chart"></canvas>
        <slot name="bottom-text">
          <div style="margin-top: 80px" class="text-center">
            <div class="font-18px">学生総数</div>
            <div class="mt-8">
              <span class="font-30px">{{ totalPrice }}</span>
              <span class="pt-6">人</span>
            </div>
          </div>
        </slot>
      </div>
    </div>
    <div v-if="showLabels">
      <!-- chart labels here !!! -->
      <div class="row-container">
        <v-row>
          <v-col
            xl="5"
            lg="12"
            cols="12"
            class="d-flex flex-wrap justify-space-between w-100 px-2"
          >
            <ul class="pa-0 ma-0 column">
              <li
                v-for="(label, index) in labels?.slice(0, 5)"
                :key="index"
                class="d-flex items-center justify-space-between my-1 mb-3"
              >
                <span class="d-flex align-center">
                  <div
                    class="color-thumbanil mr-1 my-1"
                    :style="{ background: label.color }"
                  ></div>
                  <span
                    style="min-width: 50px"
                    class="font-12px font-Noto-Sans"
                    >{{ label.text || '未設定' }}</span
                  >
                </span>
                <span
                  style="min-width: 100px"
                  class="font-18px text-55555 font-weight-medium ml-2 text-rght"
                >
                  {{ calculatePercentage(label?.price) }}%
                </span>
              </li>
            </ul>
          </v-col>
          <v-col xl="2"></v-col>
          <v-col
            xl="5"
            lg="12"
            cols="12"
            class="d-flex flex-wrap justify-space-between w-100 px-2"
          >
            <ul class="pa-0 ma-0 column">
              <li
                v-for="(label, index) in labels?.slice(6)"
                :key="index"
                class="d-flex items-center justify-space-between my-1 mb-3"
              >
                <span class="d-flex align-center">
                  <div
                    class="color-thumbanil mr-1 my-1"
                    :style="{ background: label.color }"
                  ></div>
                  <span
                    style="min-width: 50px"
                    class="font-12px font-Noto-Sans"
                    >{{ label.text || '未設定' }}</span
                  >
                </span>
                <span
                  style="min-width: 100px"
                  class="font-18px text-55555 font-weight-medium ml-2 text-rght"
                >
                  {{ calculatePercentage(label?.price) }}%
                </span>
              </li>
            </ul>
          </v-col>
        </v-row>
      </div>
    </div>
  </div>
</template>
<script>
import Chart from 'chart.js/auto';
export default {
  mounted() {
    this.initPieChart();
  },
  props: {
    showLabels: {
      type: Boolean,
      required: true,
    },
    labels: {
      type: Array,
      required: false,
      default: () => [],
    },
  },
  computed: {
    totalPrice() {
      return (this.labels || []).reduce(
        (sum, label) => sum + (label.price || 0),
        0
      );
    },
  },
  methods: {
    calculatePercentage(price) {
      if (!this.totalPrice) return 0; // Avoid division by zero

      const percentage = (price / this.totalPrice) * 100;

      // Determine how many decimal places to show
      if (percentage % 1 === 0) {
        return percentage.toFixed(0); // No decimals for whole numbers like 9%
      } else if ((percentage * 10) % 1 === 0) {
        return percentage.toFixed(1); // One decimal place for numbers like 9.3%
      } else {
        return percentage.toFixed(2); // Two decimal places for numbers like 9.35%
      }
    },
    initPieChart() {
      const labels = this.labels || []; // Fallback to an empty array
      new Chart(this.$refs.chart, {
        type: 'doughnut',
        data: {
          datasets: [
            {
              label: 'data',
              data: Array.prototype.map.call(
                this.labels,
                (val) => val.percentage
              ),
              backgroundColor: Array.prototype.map.call(
                this.labels,
                (val) => val.color
              ),
              hoverOffset: 4,
              cutoutPercentage: 40,
            },
          ],
        },
        options: {
          cutout: 80,
        },
      });
    },
  },
};
</script>
<style scoped lang="scss">
.doughnut-main-blk {
  .color-thumbanil {
    width: 13px;
    height: 13px;
    border-radius: 3px;
  }
  .label {
    font-size: 12px;
    margin: 0;
  }
  .sublabel {
    font-size: 10px;
    transform: translateX(10px);
  }
  .chart-parent {
    height: 224px;
    width: 224px;
    border-radius: 50%;
    position: relative;
    background-color: transparent;
  }
  canvas {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 3;
  }
  .text-55555 {
    color: #555555;
  }
}
</style>

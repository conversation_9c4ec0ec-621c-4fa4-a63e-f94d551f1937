<template>
  <div>
    <PageTitle
      v-show="!loading && !$store.getters.getApiProcessingStatus"
      :items="{
        title: '求人広告',
        subTitle: '特徴管理',
        buttons: [
          {
            title: '詳細条件検索',
            action: () => (toggleSearch = !toggleSearch),
            class: 'bg-white',
            variant: 'outlined',
          },
          {
            title: '新規作成',
            icon: 'mdi-plus-circle',
            action: () => (launchNew = true),
          },
        ],
      }"
    ></PageTitle>

    <v-fade-transition>
      <SearchBox
        class="mb-5"
        :searchPlaceholder="'プルダウン表示項目'"
        :toggleSearch="toggleSearch"
        @toggleSearch="toggleSearch = false"
        @search-table="searchTable"
        v-if="toggleSearch"
      />
    </v-fade-transition>

    <DataTable
      v-show="!loading && !$store.getters.getApiProcessingStatus"
      class="school-table"
      :headers="headers"
      :items="getAllInternshipFeatures"
      :total-records="totalRecords"
      :number-of-pages="totalPages"
      @update:options="updateTable"
      @row-clicked="handleRowClick"
    >
      <template v-slot:item.delete="{ item }">
        <v-btn
          variant="text"
          color="transparent"
          @click.stop="deleteInitiate(item.id)"
        >
          <v-icon size="20">$delete</v-icon>
        </v-btn>
      </template>
    </DataTable>

    <InternshipFeatureDialog
      v-model:launch="launchNew"
      :edit="false"
      @refresh="getDataFromApi"
    />

    <InternshipFeatureDialog
      v-model:launch="launchEdit"
      :item="editItem"
      @refresh="getDataFromApi"
    />

    <SimpleModel
      text="この特徴を削除してよろしいですか？"
      :dialog="dialog.delete"
      @submitSuccess="deleteInternshipFeature"
      :submitButtonText="'削除する'"
      @closeModel="dialog.delete = false"
    />

    <SuccessModel
      :text="alertText"
      :buttonText="`とじる`"
      :routeName="routeName"
      :dialog="successDialog"
      @closeModel="successDialog = false"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted, defineAsyncComponent } from 'vue';
import { useStore } from 'vuex';
const DataTable = defineAsyncComponent(
  () => import('@/components/ui/DataTable.vue')
);
const InternshipFeatureDialog = defineAsyncComponent(
  () => import('@/components/models/InternshipFeatureDialog.vue')
);
const SimpleModel = defineAsyncComponent(
  () => import('@/components/models/SimpleModel.vue')
);
const SearchBox = defineAsyncComponent(
  () => import('@/components/ui/SearchBox.vue')
);
const SuccessModel = defineAsyncComponent(
  () => import('@/components/models/SuccessModel.vue')
);

export default {
  components: {
    DataTable,
    InternshipFeatureDialog,
    SimpleModel,
    SearchBox,
    SuccessModel,
  },
  setup() {
    const store = useStore();

    const alertText = ref([]);
    const successDialog = ref(false);
    const routeName = ref('');
    const launchEdit = ref(false);
    const launchNew = ref(false);
    const loading = ref(true);
    const editItem = ref(null);
    const toggleSearch = ref(false);
    const dialog = ref({
      delete: false,
    });

    const headers = ref([
      {
        title: 'プルダウン表示項目',
        width: '70%',
        align: 'left',
        value: 'name',
        sortable: false,
      },
      {
        title: '表示順位',
        width: '20%',
        value: 'display_order',
        align: 'right',
        sortable: false,
      },
      {
        title: '',
        value: 'delete',
        width: '10%',
        align: 'center',
        sortable: false,
      },
    ]);

    const initialLoad = ref(true);
    const temporaryDeleteId = ref(null);

    const getAllInternshipFeatures = computed(
      () => store.getters.getAllInternshipFeatures
    );

    const totalRecords = computed(
      () => store.getters.getInternshipFeaturesPagination?.records_total || 0
    );

    const totalPages = computed(
      () => store.getters.getInternshipFeaturesPagination?.total_pages || 0
    );

    const searchTable = (search) => {
      getDataFromApi(undefined, { search });
    };

    const deleteInternshipFeature = async () => {
      try {
        const res = await store.dispatch(
          'INTERNSHIP_FEATURES_DELETE',
          temporaryDeleteId.value
        );
        if (res.status === 200) {
          dialog.value.delete = false;
          getDataFromApi();
          temporaryDeleteId.value = null;
        }
      } catch (error) {
        dialog.value.delete = false;
        alertText.value = [
          'インターンシップに使用されています。',
          '削除できません。',
        ];
        routeName.value = 'InternshipFeatures';
        successDialog.value = true;
      }
    };

    const deleteInitiate = (id) => {
      dialog.value.delete = true;
      temporaryDeleteId.value = id;
    };

    const sort_by_order = ref('desc');
    const sort_by = ref('created_at');
    const getDataFromApi = async (e = undefined, obj = {}) => {
      let data = {
        sort_by_order: sort_by_order.value,
        sort_by: sort_by.value,
        page: typeof e === 'number' ? e : (e?.page ?? 1),
        paginate: e?.itemsPerPage || 25,
      };

      data = Object.assign({}, obj, data);
      if (data.search === '') {
        delete data.search;
      }

      await store.dispatch('INTERNSHIP_FEATURES_GET_ALL', data);
      initialLoad.value = false;
      loading.value = false;
    };

    const page = ref(0);
    const updateTable = (e) => {
      sort_by_order.value =
        e && e.sortBy && e.sortBy.length && e?.sortBy[0]?.order
          ? e?.sortBy[0]?.order
          : 'desc';
      sort_by.value =
        e && e.sortBy && e.sortBy.length && e?.sortBy[0]?.key
          ? e?.sortBy[0]?.key
          : 'created_at';
      let option = {};
      if (!e.page) {
        option.page = e;
      } else {
        option;
      }
      // Ensure the page is updated only if it changes
      page.value = e.page; // Correctly update the page ref's value
      if (!initialLoad.value) {
        getDataFromApi(option);
      }
    };

    const handleRowClick = (event) => {
      launchEdit.value = true;
      editItem.value = event;
    };

    onMounted(() => {
      getDataFromApi();
    });

    return {
      alertText,
      successDialog,
      routeName,
      launchEdit,
      launchNew,
      editItem,
      toggleSearch,
      dialog,
      headers,
      initialLoad,
      totalRecords,
      totalPages,
      getAllInternshipFeatures,
      searchTable,
      deleteInternshipFeature,
      deleteInitiate,
      getDataFromApi,
      updateTable,
      handleRowClick,
      loading,
    };
  },
};
</script>

<template>
  <v-container class="pdf-container">
    <div id="pdf-content" ref="pdfContent" style="color: #000000de">
      <!-- <ContractDetailPDF ref="pdfDocumentContract" /> -->
      <!-- header 1 -->
      <div class="d-flex justify-space-between">
        <div class="name-uni font-12px">
          <span>{{ contract?.education_facility }}</span>
          <span class="ml-2"
            >{{ contract?.student?.family_name }}
            {{ contract?.student?.first_name }}
            <span class="ml-2"></span> 殿</span
          >
        </div>
        <div class="w-full font-12px">
          {{
            contract.datetime_contract_sent
              ? formattedDate1(contract.datetime_contract_sent)
              : ''
          }}
        </div>
      </div>
      <!-- header 2 -->
      <div class="d-flex justify-center font-20px">労働条件通知書</div>
      <!-- header 3 -->
      <div class="d-flex align-center justify-center mt-4 font-12px">
        <div>
          <!-- type contract -->
          <v-checkbox
            v-model="contract.type_contract_value"
            :value="1"
            :ripple="false"
            density="compact"
            :hide-details="true"
            color="#8E8E8E"
            class="pa-0 ma-0 login-modal cancel-mouse-event"
          >
            <template #label>
              <div class="fw-400 pa-0 text-black font-12px">新規</div>
            </template>
          </v-checkbox>
        </div>
        <div>
          <!-- type contract -->
          <v-checkbox
            v-model="contract.type_contract_value"
            :value="2"
            :ripple="false"
            density="compact"
            :hide-details="true"
            color="#8E8E8E"
            class="pa-0 ma-0 login-modal ml-8 cancel-mouse-event"
          >
            <template #label>
              <div class="fw-400 pa-0 text-black font-12px">更新</div>
            </template>
          </v-checkbox>
        </div>
        <span class="ml-6" v-if="contract.date_contract_signing"
          >({{ formattedDate1(contract.date_contract_signing) }})</span
        >
        <span class="ml-6" v-else
          >(<span class="mr-4">西暦</span> <span class="mr-4">年</span>
          <span class="mr-4">月</span> 日)
        </span>
      </div>

      <!-- header 4 -->
      <v-row justify="end">
        <v-col cols="6">
          <table>
            <tr style="font-size: 12px;">
              <td style="padding-bottom: 4px; margin-bottom: 1px; width: 120px; vertical-align: top;">事業所所在地</td>
              <td style="padding-bottom: 4px; margin-bottom: 1px; border-bottom: 1px solid black; text-align: left; line-height: 1.2; word-break: break-word;">
              <div class="d-flex justify-center">{{ contract?.business_address }}</div>
              </td>
            </tr>
            <tr style="font-size: 12px;">
              <td style="padding-bottom: 4px; margin-bottom: 1px; margin: 0;">名称</td>
              <td style="padding-bottom: 4px; margin-bottom: 1px; margin: 0; border-bottom: 1px solid black; text-align: center; line-height: 1;">{{ contract?.company?.name }}</td>
            </tr>
            <tr style="font-size: 12px;">
              <td style="padding-bottom: 4px; margin-bottom: 1px; margin: 0;">使用者職氏名</td>
              <td style="padding-bottom: 4px; margin-bottom: 1px; margin: 0; border-bottom: 1px solid black; text-align: center; line-height: 1;">{{ contract?.employee_title_name || '-' }}</td>
            </tr>
          </table>
        </v-col>
      </v-row>

      <!-- main table -->
      <v-row>
        <v-col cols="12" class="text-black">
          <!-- row 1 -->
          <v-row class="font-12px">
            <v-col
              cols="3"
              class="table-header-left d-flex align-center py-1 my-0 mx-0"
            >
              雇用期間
            </v-col>
            <v-col cols="9" class="table-header-right py-1 my-0 mx-0">
              <v-row>
                <v-col cols="12">
                  <div class="d-flex align-center">
                    <v-checkbox
                      v-model="contract.type_employment_period"
                      :value="2"
                      :ripple="false"
                      density="compact"
                      :hide-details="true"
                      color="#8E8E8E"
                      class="pa-0 ma-0 login-modal"
                    >
                      <template #label>
                        <div class="fw-400 pa-0 text-black font-12px">
                          期間の定めあり
                          <span v-if="contract.type_employment_period === 2"
                            >（{{
                              formattedDate1(contract.date_employment_start)
                            }}～{{
                              formattedDate1(contract.date_employment_end)
                            }}）</span
                          >
                        </div>
                      </template>
                    </v-checkbox>
                  </div>
                  <div class="d-flex align-center w-full">
                    <v-checkbox
                      v-model="contract.type_employment_period"
                      :value="1"
                      :ripple="false"
                      density="compact"
                      :hide-details="true"
                      color="#8E8E8E"
                      class="pa-0 ma-0 login-modal"
                    >
                      <template #label>
                        <div class="fw-400 pa-0 text-black font-12px">
                          期間の定めなし<span v-show="false"
                            >（雇入れ日：令和&nbsp;&nbsp;&nbsp;年&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;日））</span
                          >
                          <!--  need confirm reiwa year -->
                        </div>
                      </template>
                    </v-checkbox>
                  </div>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <!-- row 2 -->
          <v-row class="font-12px">
            <v-col
              cols="3"
              class="table-header-left-2 py-1 my-0 mx-0 p d-flex align-center"
            >
              <div>
                <div>更新の有無</div>
                <div>※雇用期間について</div>
                <div>「期間の定めあり」と</div>
                <div>した場合</div>
              </div>
            </v-col>
            <v-col cols="9" class="table-header-right-2 py-1 my-0 mx-0">
              <v-row>
                <v-col cols="12">
                  1. 契約の更新の有無
                  <div class="d-flex align-center ml-8">
                    [
                    <div class="d-flex align-center w-full">
                      <v-checkbox
                        v-model="contract.type_update_procedure"
                        :value="1"
                        :ripple="false"
                        density="compact"
                        :hide-details="true"
                        color="#8E8E8E"
                        class="pa-0 ma-0 login-modal pt-1"
                      >
                        <template #label>
                          <div class="fw-400 pa-0 text-black font-12px">
                            更新する場合があり得る
                          </div>
                        </template>
                      </v-checkbox>
                    </div>
                    <div class="d-flex align-center">
                      <v-checkbox
                        v-model="contract.type_update_procedure"
                        :value="2"
                        :ripple="false"
                        density="compact"
                        :hide-details="true"
                        color="#8E8E8E"
                        class="pa-0 ma-0 login-modal pt-1"
                      >
                        <template #label>
                          <div class="fw-400 pa-0 text-black font-12px">
                            契約の更新はしない
                          </div>
                        </template>
                      </v-checkbox>
                    </div>
                    ]
                  </div>
                  2. 契約の更新は、次のいずれかにより判断する
                  <ul class="ml-4">
                    <li>契約期間満了時の業務量</li>
                    <li>従事している業務の進捗状況</li>
                    <li>本人の能力、業務成績、勤務態度</li>
                    <li>会社の経営状況</li>
                  </ul>
                  3. 更新上限 有（更新４回まで／通算契約期間５年まで）
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <!-- row 3 -->
          <v-row class="font-12px">
            <v-col
              cols="3"
              class="table-header-left-2 py-1 my-0 mx-0 p d-flex align-center"
            >
              <div>就業の場所</div>
            </v-col>
            <v-col cols="9" class="table-header-right-2 py-1 my-0 mx-0">
              <v-row>
                <v-col cols="12">
                  {{ contract.work_place }}
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <!-- row 4 -->
          <v-row class="font-12px">
            <v-col
              cols="3"
              class="table-header-left-2 py-1 my-0 mx-0 p d-flex align-center"
            >
              <div>従事すべき業務の内容</div>
            </v-col>
            <v-col cols="9" class="table-header-right-2 py-1 my-0 mx-0">
              <v-row>
                <v-col cols="12">
                  {{ contract.job_detail }}
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <!-- row 5 -->
          <v-row class="font-12px">
            <v-col
              cols="3"
              class="table-header-left-2 py-1 my-0 mx-0 p d-flex align-center"
            >
              <div>
                始業・終業の時刻、勤務時間、休憩時間、所定時間外労働、休日労働の有無に関する事項
              </div>
            </v-col>
            <v-col cols="9" class="table-header-right-2 py-1 my-0 mx-0">
              <v-row>
                <v-col cols="12">
                  1. 始業・終業の時刻
                  <div class="ml-4">
                    <div>始業および終業の時間は本人の決定に委ねる。</div>
                    <div>ただし、５：００～２２：００の時間内とする。</div>
                  </div>
                  2. 勤務時間
                  <div class="ml-4">
                    <div>勤務時間は本人の決定に委ねる。</div>
                    <div>
                      ただし、５：００～２２：００の時間内で以下を上限とする。
                    </div>
                    <ul>
                      <li>１日について８時間</li>
                      <li>
                        １週間について４０時間（毎週日曜日を起算日とする）
                      </li>
                      <li>
                        １ヶ月について１１９時間（賃金締切日の翌日を起算日とする）
                      </li>
                    </ul>
                    副業・兼業を行う場合には当社と副業・兼業先で合算した勤務時間は１日について８時間、１週
                    間について４０時間を上限とする。
                  </div>
                  3. 休憩時間
                  <div class="ml-4">
                    <div>
                      １日について勤務時間が６時間を超える場合 ４５分以上
                    </div>
                    <div>
                      １日について勤務時間が８時間を超える場合 ６０分以上
                    </div>
                  </div>
                  4. 法定時間外労働・法定休日労働 無
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <!-- row 6 -->
          <v-row class="font-12px">
            <v-col
              cols="3"
              class="table-header-left-2 py-1 my-0 mx-0 p d-flex align-center"
            >
              <div>労働時間の記録</div>
            </v-col>
            <v-col cols="9" class="table-header-right-2 py-1 pr-0 my-0 mx-0">
              <v-row>
                <v-col cols="12">
                  <div>
                    <div>
                      労働時間の管理は自己申告制とし、以下の点に留意する
                    </div>
                    <ul>
                      <li>
                        勤務を行った日ごとに当社指定の勤怠管理表に勤務時間・業務内容を入力すること。
                      </li>
                      <li>
                        勤務時間は実際に勤務した適正な時間を申告すること。
                      </li>
                      <li>
                        始業終業時刻、勤務時間についての上限を守ること。ただし、特別な理由があり上限時間を超えて勤務する場合は管理者に報告の上、承認を得ること。承認を得ずに行った時間外労働、休日労働、深夜労働時間については業務上必要ないものとみなし、賃金は支給しない。
                      </li>
                      <li>
                        会社は申告された勤務時間と実際の労働時間との間に乖離がないか、定期的に確認することがある。
                      </li>
                      <li>
                        労働時間の自己申告制は業務の効率化やワークライフバランスの実現の観点から導入しているものである。
                      </li>
                    </ul>
                  </div>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <!-- row 7 -->
          <v-row class="font-12px">
            <v-col
              cols="3"
              class="table-header-left-2 py-1 my-0 mx-0 p d-flex align-center"
            >
              <div>休日</div>
            </v-col>
            <v-col cols="9" class="table-header-right-2 py-1 my-0 mx-0">
              <v-row>
                <v-col cols="12">
                  週休２日以上とし、法定休日は４週を通じて４日とする。
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-col>
      </v-row>

      <!-- main table 2 -->
      <v-row class="pt-16" style="margin-top: 10em">
        <v-col cols="12" class="text-black">
          <!-- row 8 -->
          <v-row class="font-12px">
            <v-col
              cols="3"
              class="table-header-left py-1 my-0 mx-0 p d-flex align-center"
            >
              <div>賃金</div>
            </v-col>
            <v-col cols="9" class="table-header-right py-1 my-0 mx-0">
              <v-row>
                <v-col cols="12">
                  <div
                    style="
                      font-size: 12px;
                      line-height: 1.2;
                      margin: 0;
                      padding: 0;
                    "
                  >
                    <p style="margin: 0">1. 時給制（{{ contract.wage }}円）</p>

                    <p style="margin: 0">
                      2. 時間外に対して支払われる割増賃金率
                    </p>
                    <ul
                      style="
                        list-style-type: none;
                        padding-left: 15px;
                        margin: 0;
                      "
                    >
                      <li style="margin: 0">
                        法定時間外/月６０時間以内（{{
                          contract.overtime_pay_rate_under_60_hours
                        }}％）
                      </li>
                      <li style="margin: 0">
                        法定時間外/月６０時間超（{{
                          contract.overtime_pay_rate_over_60_hours
                        }}％）
                      </li>
                      <li style="margin: 0">
                        法定休日（{{ contract.holiday_pay_rate }}％）
                      </li>
                      <li style="margin: 0">
                        深夜（{{ contract.night_shift_pay_rate }}％）
                      </li>
                    </ul>

                    <p style="margin: 0">
                      3. 賃金締切日　毎月 {{ contract.wage_closing_date }}日
                    </p>
                    <p style="margin: 0">
                      4. 賃金支払日　毎月　翌月{{
                        contract.wage_payment_date
                      }}日
                    </p>
                    <p style="margin: 0">
                      5.
                      支払方法　　本人の同意を得て直接銀行口座に振込にて全額支払う。ただし、法令等で定められているものは、支払の際に控除する。
                    </p>
                    <p style="margin: 0">
                      6. 昇給 {{ contract.flg_salary_increase ? '有' : '無' }}
                    </p>
                    <p style="margin: 0">
                      7. 賞与 {{ contract.flg_bonus ? '有' : '無' }}
                    </p>
                    <p style="margin: 0">
                      8. 退職金
                      {{ contract.flg_retirement_allowance ? '有' : '無' }}
                    </p>
                  </div>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <!-- row 9 -->
          <v-row class="font-12px">
            <v-col
              cols="3"
              class="table-header-left-2 py-1 my-0 mx-0 p d-flex align-center"
            >
              <div>退職に関する事項</div>
            </v-col>
            <v-col cols="9" class="table-header-right-2 py-1 my-0 mx-0">
              <v-row>
                <v-col cols="12">
                  <div>
                    1.
                    自己都合退職の場合の手続については、原則として３０日前までに申し出なければならない
                  </div>
                  <div>
                    2. 解雇の事由及び手続
                    <div class="ml-4">
                      <div>
                        ①&nbsp;&nbsp;
                        身体、精神の障害により、業務に耐えられないとき
                      </div>
                      <div>
                        ②&nbsp;&nbsp;
                        勤務成績が不良で、就業に適さないと認められたとき
                      </div>
                      <div>
                        ③&nbsp;&nbsp;
                        協調性がなく、注意および指導しても改善の見込みがないと認められるとき
                      </div>
                      <div>
                        ④&nbsp;&nbsp;
                        事業の縮小等、やむを得ない業務の都合により必要のあるとき
                      </div>
                      <div>
                        ⑤&nbsp;&nbsp;
                        事業の運営上、やむを得ない事情、または天災事変その他これに準ずるやむを得ない事情により事業の継続が困難になったとき
                      </div>
                    </div>
                  </div>
                  <div class="ml-4">
                    上記に該当する事由があった場合は、３０日前に予告するか解雇予告手当を支払って解雇する。
                  </div>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <!-- row 10 -->
          <v-row class="font-12px">
            <v-col
              cols="3"
              class="table-header-left-2 py-1 my-0 mx-0 p d-flex align-center"
            >
              社会保険の加入状況
            </v-col>
            <v-col cols="9" class="table-header-right-2 py-1 my-0 mx-0">
              <v-row>
                <v-col cols="12">
                  <div class="d-flex align-center w-full">
                    <v-checkbox
                      v-model="
                        contract.flg_insurance_industrial_accident_compensation
                      "
                      :value="true"
                      :ripple="false"
                      density="compact"
                      :hide-details="true"
                      color="#8E8E8E"
                      class="pa-0 ma-0 login-modal"
                    >
                      <template #label>
                        <div class="fw-400 pa-0 text-black font-12px">
                          労災保険
                        </div>
                      </template>
                    </v-checkbox>
                  </div>
                  <div class="d-flex align-center">
                    <v-checkbox
                      v-model="contract.flg_insurance_unemployment"
                      :value="true"
                      :ripple="false"
                      density="compact"
                      :hide-details="true"
                      color="#8E8E8E"
                      class="pa-0 ma-0 login-modal"
                    >
                      <template #label>
                        <div class="fw-400 pa-0 text-black font-12px">
                          雇用保険
                        </div>
                      </template>
                    </v-checkbox>
                  </div>
                  <div class="d-flex align-center">
                    <v-checkbox
                      v-model="contract.flg_insurance_public_health"
                      :value="true"
                      :ripple="false"
                      density="compact"
                      :hide-details="true"
                      color="#8E8E8E"
                      class="pa-0 ma-0 login-modal"
                    >
                      <template #label>
                        <div class="fw-400 pa-0 text-black font-12px">
                          健康保険
                        </div>
                      </template>
                    </v-checkbox>
                  </div>
                  <div class="d-flex align-center">
                    <v-checkbox
                      v-model="contract.flg_insurance_employee_pension"
                      :value="true"
                      :ripple="false"
                      density="compact"
                      :hide-details="true"
                      color="#8E8E8E"
                      class="pa-0 ma-0 login-modal"
                    >
                      <template #label>
                        <div class="fw-400 pa-0 text-black font-12px">
                          厚生年金保険
                        </div>
                      </template>
                    </v-checkbox>
                  </div>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <!-- row 12 -->
          <v-row class="font-12px">
            <v-col
              cols="3"
              class="table-header-left-2 py-1 my-0 mx-0 p d-flex align-center"
            >
              <div>雇用管理の改善等に関する事項に係る相談窓口</div>
            </v-col>
            <v-col cols="9" class="table-header-right-2 py-1 my-0 mx-0">
              <v-row>
                <v-col cols="12">
                  <div>
                    担当者職氏名：{{ contract.consultation_user_title_name }}
                  </div>
                  <div>
                    email:
                    {{ contract.consultation_user_mail_address }}
                  </div>
                  <div>
                    電話:
                    {{ contract.consultation_user_tel }}
                  </div>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </div>
  </v-container>
</template>

<script>
/**
 * Contract Detail PDF Component
 * Handles the generation and display of employment contract PDFs in Japanese format
 * Supports multi-page PDF generation with proper page numbering
 */
import { ref, computed, onMounted, defineExpose } from 'vue';
import { useStore } from 'vuex';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import moment from 'moment';

export default {
  setup() {
    const store = useStore();
    const showContent = ref(false);

    const formattedDate1 = (item) => {
      return moment(item).format('YYYY年M月D日');
    };

    // Using Vuex getter
    const contract = computed(() => store.getters.getSingleContract);

    const generatePdf = async () => {
      try {
        store.dispatch('API_PROCESSING', true); // Start loading

        const element = document.getElementById('pdf-content');
        const canvas = await html2canvas(element, { scale: 2 });
        const imgData = canvas.toDataURL('image/png');
        const pdf = new jsPDF('p', 'mm', 'a4');

        const pageHeight = 297; // A4 height in mm
        const pageWidth = 210; // A4 width in mm
        const imgWidth = 210;
        const imgHeight = (canvas.height * imgWidth) / canvas.width;

        let heightLeft = imgHeight;
        const pages = [];

        // Calculate total pages
        while (heightLeft > 0) {
          pages.push(heightLeft);
          heightLeft -= pageHeight;
        }

        const totalPages = pages.length;
        heightLeft = imgHeight;
        let position = 0;
        let currentPage = 1;

        // Reset heightLeft for actual content rendering
        while (heightLeft > 0) {
          if (currentPage > 1) {
            pdf.addPage();
          }

          pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
          pdf.setFontSize(10); // Adjust font size as needed
          pdf.text(`${currentPage}/${totalPages}`, pageWidth - 16, 10); // Move 10mm from the right edge

          heightLeft -= pageHeight;
          position -= pageHeight;
          currentPage++;
        }

        // Open the PDF in a new tab using a Blob URL
        const pdfBlob = pdf.output('blob');
        const url = URL.createObjectURL(pdfBlob);
        window.open(url, '_blank');
      } catch (error) {
        console.error('Error generating PDF:', error);
      } finally {
        store.dispatch('API_PROCESSING', false); // Stop loading after completion
      }
    };

    defineExpose({ generatePdf });
    return {
      showContent,
      contract,
      generatePdf,
      formattedDate1,
      moment,
    };
  },
};
</script>

<style scoped>
#pdf-content {
  width: 210mm; /* A4 width */
  padding: 40px; /* Add some padding to avoid content cutting off */
  padding-top: 60px;
}

.pdf-container {
  z-index: -9999999;
  position: absolute;
  left: 0;
}

.pdf-content {
  background: white;
  font-family: 'Arial', sans-serif;
  font-size: 12px;
  line-height: 1.5;
}

.name-uni {
  border-radius: 0px !important;
  border-bottom: 1px solid black;
}
.page {
  position: relative;
  page-break-after: always;
}

.page:last-child {
  page-break-after: auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-number {
  position: absolute;
  top: 10mm;
  right: 10mm;
  font-size: 12px;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

td {
  padding: 5px 10px;
  vertical-align: top;
}

ul,
ol {
  padding-left: 20px;
}

h2,
h3,
h4 {
  margin-top: 0;
}

:deep(.v-input--selection-controls__input + .v-label) {
  color: #000000de !important;
}

.border-b-header-4 {
  border-bottom: 1px solid black;
}

.table-header {
  border-collapse: collapse;
}
.table-header-left {
  border: 1px solid #000;
}

.table-header-right {
  border: 1px solid #000;
  border-left: 0;
}

.table-header-left-2 {
  border: 1px solid #000;
  border-top: 0px;
}

.table-header-right-2 {
  border-right: 1px solid #000;
  border-bottom: 1px solid #000;
  border-top: 0px;
}

@media (max-width: 960px) {
}
</style>

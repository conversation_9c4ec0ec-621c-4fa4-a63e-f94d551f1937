import axios from '@/services/api';

function initialState() {
  return {
    status: '',
    feedbacks: [],
    singleFeedback: {},
    feedbackCounts: {},
    feedbackPagination: 0,
    feedbackDraft: false,
    csvData: null,
    months: [],
    super_power_default: [
      {
        "id": 1,
        "value": "◯◯という仕事を依頼しました。関係者が多い仕事で、主体性やチームを意識した行動が求められます。\n▲▲さんはその仕事に対し、自ら考え積極的に提案し、目標の達成に向かってチームによい影響を与え、時には牽引することができていました。\n具体的には、◯◯での活躍がありました。周囲からも、「◯◯◯」というコメントをもらっています。ありがとうございます。"
      },
      {
        "id": 2,
        "value": "◯◯という仕事を依頼しました。正解のない仕事で、失敗を恐れず行動をすることが求められます。\n▲▲さんはその仕事に対し、さまざまな情報にアンテナを張り、それらに基づき考えたアイデアを積極的に試していました。特に、〇〇という提案には懐疑的な意見や、反対の声もありましたが、それでも周囲を説得して行動したことを評価しています。周囲からも、「◯◯◯」というコメントをもらっています。ありがとうございます。"
      },
      {
        "id": 3,
        "value": "◯◯という仕事を依頼しました。初対面の人たちにも積極的にアプローチしたり、初めて使う情報ソースも活用することが求められます。\n▲▲さんはその仕事に対し、興味関心を高くもって、さまざまなリソースにアクセスし、自分や組織の外にある情報を積極的に活用していました。具体的には、〇〇の場面において〇〇という提案があったことを評価しています。周囲からも、「◯◯◯」というコメントをもらっています。ありがとうございます。"
      },
      {
        "id": 4,
        "value": "◯◯という仕事を依頼しました。自分なりの考えや新しい視点でアイデアを出したり、切り口を変えた提案が求められます。\n▲▲さんはその仕事に対し、周囲にも協力を得ながら、これまで社内では検討したことがないような〇〇という観点で、〇〇を提案してくれました。これまでにない新しい視点からのアプローチを高く評価しています。周囲からも、「◯◯◯」というコメントをもらっています。ありがとうございます。"
      },
      {
        "id": 5,
        "value": "◯◯という仕事を依頼しました。チームの意見をしっかり聴き、誰もが同じ目標に向かって取り組めるよう働きかける力が求められます。\n▲▲さんはその仕事に対し、丁寧にチームメンバーとのコミュニケーションを重ね、少数派の意見も注意深く聴き、〇〇という目標を全員が目指せる環境づくりをしてくれました。具体的には、〇〇での活躍が印象的です。周囲からも、「◯◯◯」というコメントをもらっています。ありがとうございます。"
      },
      {
        "id": 6,
        "value": "◯◯という仕事を依頼しました。関係者も多く、専門的な知識を求められる仕事なので、潜在的なリスクに注意を払い判断することが求められます。\n▲▲さんはその仕事に対し、〇〇プロジェクトの過去の経緯など詳細まで把握し、意思決定を支援するための情報や選択肢をつくることに貢献してくれました。具体的には、〇〇での活躍が印象的です。周囲からも、「◯◯◯」というコメントをもらっています。ありがとうございます。"
      },
      {
        "id": 7,
        "value": "◯◯という仕事を依頼しました。初めてやる仕事が多いだけに、自分の発言や行動を振り返り、そこから学びを得て次に進むスキルが求められます。\n▲▲さんはその仕事に対し、必ず反省会を開き、その内容を記録して、チームでも共有できるようにしながら、反省と学びを繰り返して仕事の質をあげてくれました。具体的には、〇〇での活躍が印象的です。周囲からも、「◯◯◯」というコメントをもらっています。ありがとうございます。"
      },
      {
        "id": 8,
        "value": "◯◯という仕事を依頼しました。「物事の構造を理解するスキルや筋道を立てて話す力」が求められます。\n▲▲さんはその仕事に対し、細かい点ばかりを気にするのではなく、目的と全体像の把握をまずやってくれました。その上で、自分なりの仮説を立て、それを検証しながらよりよい選択ができるよう工夫していました。〇〇プロジェクトでの、筋道の立った説明には、誰もが納得していました。周囲からも、「◯◯◯」というコメントをもらっています。ありがとうございます。"
      }
    ],
    growth_idea_default: [
      {
        "id": 1,
        "value": "◯◯という仕事は、▲▲さんにとってもチャレンジだったと思います。\nもしかすると、これまでにあまり経験のない「チームを牽引するスキル」が必要だからです。\n▲▲さんはその仕事に対し、◯◯を試したり、◯◯に相談したことはすごくよかったと思うのですが、◯◯という方法もあったと思います。\n何か相談したいことがあったら、いつでも声をかけてください。これからも、宜しくお願いします。"
      },
      {
        "id": 2,
        "value": "◯◯という仕事は、▲▲さんにとってもチャレンジだったと思います。\nもしかすると、これまでにあまり経験のない「失敗を恐れず行動をすること」が必要だからです。\n▲▲さんはその仕事に対し、◯◯を試したり、◯◯に相談したことはすごくよかったと思うのですが、◯◯という方法もあったと思います。\n何か相談したいことがあったら、いつでも声をかけてください。これからも、宜しくお願いします。"
      },
      {
        "id": 3,
        "value": "◯◯という仕事は、▲▲さんにとってもチャレンジだったと思います。\nもしかすると、これまでにあまり経験のない「自ら外の世界にアプローチする力」が必要だからです。\n▲▲さんはその仕事に対し、◯◯を試したり、◯◯に相談したことはすごくよかったと思うのですが、◯◯という方法もあったと思います。\n何か相談したいことがあったら、いつでも声をかけてください。これからも、宜しくお願いします。"
      },
      {
        "id": 4,
        "value": "◯◯という仕事は、▲▲さんにとってもチャレンジだったと思います。\nもしかすると、これまでにあまり経験のない「自分なりの考えや新しい視点でアイデア」が必要だからです。\n▲▲さんはその仕事に対し、◯◯を試したり、◯◯に相談したことはすごくよかったと思うのですが、◯◯という方法もあったと思います。\n何か相談したいことがあったら、いつでも声をかけてください。これからも、宜しくお願いします。"
      },
      {
        "id": 5,
        "value": "◯◯という仕事は、▲▲さんにとってもチャレンジだったと思います。\nもしかすると、これまでにあまり経験のない「誰もが同じ目標に向かって取り組むための環境づくりスキル」が必要だからです。\n▲▲さんはその仕事に対し、◯◯を試したり、◯◯に相談したことはすごくよかったと思うのですが、◯◯という方法もあったと思います。\n何か相談したいことがあったら、いつでも声をかけてください。これからも、宜しくお願いします。"
      },
      {
        "id": 6,
        "value": "◯◯という仕事は、▲▲さんにとってもチャレンジだったと思います。\nもしかすると、これまでにあまり経験のない「詳細まで意識した下調べとリスク管理スキル」が必要だからです。\n▲▲さんはその仕事に対し、◯◯を試したり、◯◯に相談したことはすごくよかったと思うのですが、◯◯という方法もあったと思います。\n何か相談したいことがあったら、いつでも声をかけてください。これからも、宜しくお願いします。"
      },
      {
        "id": 7,
        "value": "◯◯という仕事は、▲▲さんにとってもチャレンジだったと思います。\nもしかすると、これまでにあまり経験のない「自分の発言や行動、その結果起きたことを振り返る力」が必要だからです。\n▲▲さんはその仕事に対し、◯◯を試したり、◯◯に相談したことはすごくよかったと思うのですが、◯◯という方法もあったと思います。\n何か相談したいことがあったら、いつでも声をかけてください。これからも、宜しくお願いします。"
      },
      {
        "id": 8,
        "value": "◯◯という仕事は、▲▲さんにとってもチャレンジだったと思います。\nもしかすると、これまでにあまり経験のない「物事の構造を理解するスキル」が必要だからです。\n▲▲さんはその仕事に対し、◯◯を試したり、◯◯に相談したことはすごくよかったと思うのですが、◯◯という方法もあったと思います。\n何か相談したいことがあったら、いつでも声をかけてください。これからも、宜しくお願いします。"
      }
    ]
  };
}

const state = initialState();

const getters = {
  getFeedbacksStatus: (state) => state.status,
  getAllFeedbacks: (state) => state.feedbacks,
  getSingleFeedback: (state) => state.singleFeedback,
  getFeedbackCounts: (state) => state.feedbackCounts,
  getFeedbackPagination: (state) => state.feedbackPagination,
  getFeedbackDraft: (state) => state.feedbackDraft,
  getFeedbackCsvData: (state) => state.csvData,
  getFeedbackMonths: (state) => state.months,
  getStatusAlert: (state) => state.showStatusAlert,
  getStatusAlertMessage: (state) => state.statusAlertMessage,
  getSuperPowerDefault: (state) => state.super_power_default,
  getGrowthIdeaDefault: (state) => state.growth_idea_default,
};

const actions = {
  ['FEEDBACK_GET_ALL']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('FEEDBACK_REQUEST');
      axios
        .get(`/admin/feedback`, { params })
        .then((response) => {
          let data = response.data.data;
          commit('FEEDBACK_GET_ALL_SUCCESS', { data });

          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['FEEDBACK_GET']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('FEEDBACK_REQUEST');
      axios
        .get(`/admin/feedback/${params.id}`)
        .then((response) => {
          let data = response.data;
          commit('FEEDBACK_GET_SUCCESS', { data });

          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['FEEDBACK_CREATE']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('FEEDBACK_REQUEST');
      axios
        .post(`/admin/feedback`, params)
        .then((response) => {
          resolve(response);
          dispatch('GET_COUNTS_DATA');
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['FEEDBACK_UPDATE']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('FEEDBACK_REQUEST');
      axios
        .put(`/admin/feedback/${params.id}`, params)
        .then((response) => {
          resolve(response);
          dispatch('GET_COUNTS_DATA');
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  updateStatusAlert({ commit }, { showStatusAlert, statusAlertMessage }) {
    commit('SET_STATUS_ALERT', { showStatusAlert, statusAlertMessage });
  },

  ['FEEDBACK_DELETE']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      commit('FEEDBACK_REQUEST');
      axios
        .delete(`/admin/feedback/${params.id}`, params)
        .then((response) => {
          resolve(response);
          dispatch('GET_COUNTS_DATA');
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['FEEDBACK_STATUS_UPDATE']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      dispatch('API_PROCESSING', true, { root: true });
      axios
        .post(`/admin/feedback-status/${params.id}`, params)
        .then((response) => {
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['FEEDBACK_EXPORT_CSV']: ({ commit, dispatch }) => {
    dispatch('API_PROCESSING', true, { root: true });
    return new Promise((resolve, reject) => {
      axios
        .get('/admin/export/feedback')
        .then((response) => {
          commit('FEEDBACK_EXPORT_CSV_SUCCESS', response.data);
          resolve(response);
        })
        .catch((error) => {
          reject(error);
        })
        .finally(() => {
          dispatch('API_PROCESSING', false, { root: true });
        });
    });
  },

  ['FEEDBACK_GET_MONTH']: ({ commit, dispatch }, params) => {
    return new Promise((resolve, reject) => {
      commit('FEEDBACK_REQUEST');
      axios
        .get(
          `/admin/available-month-feedback-target?company_id=${params.companyId}&student_id=${params.studentId}`
        )
        .then((response) => {
          let data = response.data;
          commit('FEEDBACK_GET_MONTH_SUCCESS', { data });

          resolve(response);
        })
        .catch((err) => {
          reject(err);
        })
        .finally(() => {
        });
    });
  },

  ['ADD_FEEDBACK_MONTH']: ({ commit }, newMonth) => {
    commit('ADD_FEEDBACK_MONTH', newMonth);
  },

  ['REMOVE_SINGLE_FEEDBACK']: ({ commit }) => {
    commit('REMOVE_SINGLE_FEEDBACK');
  },
};

const mutations = {
  ['FEEDBACK_DRAFT_UPDATE']: (state, payload) => {
    state.feedbackDraft = payload;
  },
  ['FEEDBACK_REQUEST']: (state) => {
    state.status = 'loading';
  },

  ['FEEDBACK_GET_ALL_SUCCESS']: (state, { data }) => {
    state.status = 'success';
    state.feedbacks = data.data;
    state.feedbackPagination = data.paginate;
    state.feedbackCounts = data.counts;
  },
  
  ['FEEDBACK_ITEM_UPDATED']: (state, updatedFeedbackItem) => {
    // Find the index of the item to be updated
    const index = state.feedbacks.findIndex(
      (feedback) => feedback.id === updatedFeedbackItem.id
    );

    if (index !== -1) {
      // If found, replace the old item with the updated one
      // Using Vue.set (Vue 2) or a new array with splice (Vue 3) for reactivity
      // For Vue 3, a direct assignment often works due to Proxy-based reactivity
      state.feedbacks[index] = updatedFeedbackItem;

      // For older Vue 2 or if you encounter reactivity issues:
      // Vue.set(state.feedbacks, index, updatedFeedbackItem);
    }
  },

  ['FEEDBACK_GET_SUCCESS']: (state, { data }) => {
    state.status = 'success';
    state.singleFeedback = data.data.data;
  },

  ['REMOVE_SINGLE_FEEDBACK']: (state) => {
    state.singleFeedback = '';
  },

  ['FEEDBACK_EXPORT_CSV_SUCCESS']: (state, payload) => {
    state.csvData = payload;
  },

  ['FEEDBACK_GET_MONTH_SUCCESS']: (state, payload) => {
    const combinedData = payload?.data?.data?.flatMap((contract) =>
      contract.available_months.map((month) => ({
        ...month,
        month_feedback_target: month.month_feedback_target.replace(/-/g, '/'),
        work_month_feedback_target: month.work_month_feedback_target.replace(
          /-/g,
          '/'
        ),
        contract_id: contract.contract_id,
        label:
          month.month_feedback_target.replace(/-/g, '/') +
          ` (稼働月: ${month.work_month_feedback_target.replace(/-/g, '/')})`,
      }))
    );
    state.months = combinedData;
  },

  ['ADD_FEEDBACK_MONTH']: (state, newMonth) => {
    // Check if the month already exists to avoid duplicates
    if (!state.months.includes(newMonth)) {
      state.months.push(newMonth);
      state.months.sort((a, b) => new Date(a) - new Date(b));
    }
  },

  SET_STATUS_ALERT(state, { showStatusAlert, statusAlertMessage }) {
    state.showStatusAlert = showStatusAlert;
    state.statusAlertMessage = statusAlertMessage;
  },
};

export default {
  state,
  getters,
  actions,
  mutations,
};

<template>
  <div class="">
    <PageTitle
      :items="{
        title: '学生',
        subTitle: '新規登録',
        back: {
          action: () => {
            $router.push({
              name: 'Students',
            });
          },
        },
      }"
    ></PageTitle>
    <Form @submit="submit">
      <v-row>
        <v-col cols="8">
          <!-- Main card container -->
          <v-card class="w-100 pb-12">
            <div class="d-flex align-center justify-center">
              <div class="mt-20" style="width: 642px">
                <!-- Loop through fields and create form inputs dynamically -->
                <v-sheet
                  style="width: 642px"
                  :class="field.class"
                  color="transparent"
                  v-for="(field, index) in fields"
                  :key="field.name"
                >
                  <template v-if="field.type == 'text'">
                    <div class="mb-2 d-flex align-center mt-2">
                      <div>{{ field.label }}</div>
                      <div :class="{ 'pb-1': mdAndUp }"></div>
                      <div class="ml-2 text-97">{{ field?.second_label }}</div>
                      <span v-if="field.required_text" class="text-light-red">{{
                        field.required_text
                      }}</span>
                    </div>
                    <Field
                      v-slot="{
                        field: { value, ...fieldWithoutValue },
                        errors,
                      }"
                      :name="field.name"
                      :value="field.value"
                      :rules="field.rules"
                    >
                      <v-text-field
                        v-bind="fieldWithoutValue"
                        v-model="field.value"
                        :error-messages="errors"
                        :error="errors.length !== 0"
                        :hide-details="errors.length <= 0"
                        single-line
                        color="#13ABA3"
                        variant="outlined"
                        density="compact"
                        :placeholder="field.placeholder"
                      ></v-text-field>
                    </Field>
                  </template>

                  <template v-if="field.type == 'number'">
                    <div class="mb-2 d-flex align-content-center mt-2">
                      <div>{{ field.label }}</div>
                      <div class="ml-2 text-97">{{ field?.second_label }}</div>
                      <span v-if="field.required_text" class="text-light-red">{{
                        field.required_text
                      }}</span>
                    </div>
                    <Field
                      v-slot="{
                        field: { value, ...fieldWithoutValue },
                        errors,
                      }"
                      :name="field.name"
                      :value="field.value"
                      :rules="field.rules"
                    >
                      <v-text-field
                        v-bind="fieldWithoutValue"
                        v-model="field.value"
                        :error-messages="errors"
                        :error="errors.length !== 0"
                        :hide-details="errors.length <= 0"
                        single-line
                        color="#13ABA3"
                        variant="outlined"
                        density="compact"
                        type="number"
                        :placeholder="field.placeholder"
                      ></v-text-field>
                    </Field>
                  </template>

                  <template v-if="field.type == 'textarea'">
                    <div class="mb-2 mt-2">
                      {{ field.label }}
                      <span v-if="field.required_text" class="text-light-red">{{
                        field.required_text
                      }}</span>
                    </div>
                    <Field
                      v-slot="{
                        field: { value, ...fieldWithoutValue },
                        errors,
                      }"
                      :name="field.name"
                      :value="field.value"
                      :rules="field.rules"
                    >
                      <v-textarea
                        v-bind="fieldWithoutValue"
                        v-model="field.value"
                        :error-messages="errors"
                        :error="errors.length !== 0"
                        :hide-details="errors.length <= 0"
                        single-line
                        color="#13ABA3"
                        variant="outlined"
                        density="compact"
                        :placeholder="field.placeholder"
                      ></v-textarea>
                    </Field>
                  </template>

                  <template v-if="field.type == 'autocomplete'">
                    <div class="full-width" style="z-index: 10">
                      <div
                        class="mb-2 mt-2 d-flex align-content-center"
                        :class="{
                          'font-14px': smAndDown,
                          'font-18px': mdAndUp,
                          'mt-4': mdAndUp && field.label,
                        }"
                        bvv
                      >
                        <div>{{ field.label }}</div>
                        <div></div>
                      </div>
                      <Field
                        v-slot="{
                          field: { value, ...fieldWithoutValue },
                          errors,
                        }"
                        :name="field.name"
                        :value="field.value"
                        :rules="field.rules"
                      >
                        <v-autocomplete
                          :id="`autocomplete-${field.name}`"
                          v-bind="fieldWithoutValue"
                          style="z-index: 10"
                          id="autocomplete"
                          v-model="field.value"
                          variant="outlined"
                          density="compact"
                          color="#13ABA3"
                          class="mt-1 autocomplete"
                          :error-messages="errors"
                          :error="errors.length !== 0"
                          :hide-details="errors.length <= 0"
                          :hide-no-data="!field.noDataText"
                          hide-selected
                          :items="field.items"
                          :item-title="field.item_text"
                          :item-value="field.item_value"
                          :placeholder="field.placeholder"
                          :menu-props="customProps"
                          menu-icon="$greyExpansionDropdown"
                          @update:modelValue="
                            field.hasOwnProperty('addNewDataField')
                              ? (field.addNewDataField.trigger = false)
                              : null
                          "
                          @update:search="hideOrDisplaySuggestions"
                          autocomplete="new-password"
                        >
                          <template v-slot:no-data>
                            <div
                              v-if="
                                field.noDataText &&
                                field.hasOwnProperty('addNewDataField') &&
                                field.addNewDataField.hasOwnProperty('trigger')
                              "
                              style="cursor: pointer; padding: 15px"
                              @click="selectOther()"
                            >
                              {{ field.noDataText }}
                            </div>
                          </template>
                        </v-autocomplete>
                      </Field>
                    </div>
                  </template>
                  <!-- nested -->
                  <div
                    v-if="
                      field.hasOwnProperty('addNewDataField') &&
                      !!field.addNewDataField.trigger
                    "
                  >
                    <v-sheet
                      style="width: 642px"
                      color="transparent"
                      v-for="(fieldNested, index) in field.addNewDataField
                        .fields"
                      :key="index"
                    >
                      <template v-if="fieldNested.type == 'text'">
                        <div class="mb-2 d-flex align-center mt-2">
                          <div>{{ fieldNested.label }}</div>
                          <div :class="{ 'pb-1': mdAndUp }"></div>
                          <div class="ml-2 text-97">
                            {{ field?.second_label }}
                          </div>
                          <span
                            v-if="fieldNested.required_text"
                            class="text-light-red"
                            >{{ fieldNested.required_text }}</span
                          >
                        </div>
                        <Field
                          v-slot="{
                            field: { value, ...fieldWithoutValue },
                            errors,
                          }"
                          :name="fieldNested.name"
                          :rules="fieldNested.rules"
                        >
                          <v-text-field
                            v-bind="fieldWithoutValue"
                            v-model="fieldNested.value"
                            :error-messages="errors"
                            :error="errors.length !== 0"
                            :hide-details="errors.length <= 0"
                            single-line
                            color="#13ABA3"
                            variant="outlined"
                            density="compact"
                            :placeholder="fieldNested.placeholder"
                          ></v-text-field>
                        </Field>
                      </template>
                      <template v-if="fieldNested.type == 'dropdown'">
                        <div class="full-width">
                          <div class="mb-2 mt-2 d-flex align-center">
                            <div>{{ fieldNested.label }}</div>
                            <div></div>
                          </div>
                          <Field
                            v-slot="{
                              field: { value, ...fieldWithoutValue },
                              errors,
                            }"
                            :name="fieldNested.name"
                            :rules="fieldNested.rules"
                          >
                            <v-select
                              v-bind="fieldWithoutValue"
                              v-model="fieldNested.value"
                              variant="outlined"
                              density="compact"
                              class="mt-1"
                              :error-messages="errors"
                              :error="errors.length !== 0"
                              :hide-details="errors.length <= 0"
                              :items="fieldNested.items"
                              :disabled="fieldNested.disabled"
                              :filled="fieldNested.disabled"
                              :item-title="fieldNested.item_text"
                              :item-value="fieldNested.item_value"
                              :placeholder="fieldNested.placeholder"
                              :no-data-text="fieldNested.no_data_text"
                              menu-icon="$greyExpansionDropdown"
                            >
                            </v-select>
                          </Field>
                        </div>
                      </template>
                    </v-sheet>
                  </div>
                  <!-- stop nested -->

                  <template v-if="field.type == 'dropdown'">
                    <div class="full-width">
                      <div class="mb-2 mt-2 d-flex align-center">
                        <div>{{ field.label }}</div>
                        <div></div>
                      </div>
                      <Field
                        v-slot="{
                          field: { value, ...fieldWithoutValue },
                          errors,
                        }"
                        :name="field.name"
                        :value="field.value"
                        :rules="field.rules"
                      >
                        <v-select
                          v-bind="fieldWithoutValue"
                          v-model="field.value"
                          variant="outlined"
                          density="compact"
                          class="mt-1"
                          :error-messages="errors"
                          :error="errors.length !== 0"
                          :hide-details="errors.length <= 0"
                          :items="field.items"
                          :disabled="field.disabled"
                          :filled="field.disabled"
                          :item-title="field.item_text"
                          :item-value="field.item_value"
                          :placeholder="field.placeholder"
                          :no-data-text="field.no_data_text"
                          menu-icon="$greyExpansionDropdown"
                        >
                        </v-select>
                      </Field>
                    </div>
                  </template>
                </v-sheet>
              </div>
            </div>
          </v-card>
        </v-col>

        <v-col cols="4">
          <v-card
            class="d-flex align-center justify-center text-center"
            width="100%"
          >
            <div class="my-10">
              <div>
                <v-btn
                  height="35px"
                  width="250px"
                  type="submit"
                  color="primary"
                  class="mb-6 text-white"
                >
                  新規作成
                </v-btn>
              </div>
              <div>
                <v-btn
                  height="35px"
                  width="250px"
                  variant="outlined"
                  type="button"
                  color="#13ABA3"
                  @click="dialog.import = true"
                >
                  csv取り込み
                </v-btn>
              </div>
              <br />
            </div>
          </v-card>
        </v-col>
      </v-row>
    </Form>

    <ModalImportStudent
      text="学生情報CSV取り込み"
      :dialog="dialog.import"
      :errorCSV="errorCSV"
      @submitSuccess="uploadCSV"
      @closeModel="dialog.import = false"
      @downloadError="downloadError"
    ></ModalImportStudent>
  </div>
</template>
<script>
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { useStore } from 'vuex';
import ModalImportStudent from '@/components/models/ModalImportStudent.vue';
import moment from 'moment';
import { useRouter, useRoute } from 'vue-router';
import { useDisplay } from 'vuetify';
import { Field, Form } from 'vee-validate';
import Encoding from 'encoding-japanese';

export default {
  components: {
    ModalImportStudent,
  },
  setup() {
    const { mdAndUp, smAndDown } = useDisplay();
    const store = useStore();
    const router = useRouter();
    const route = useRoute();
    const dialog = reactive({
      import: false,
      delete: false,
      deleteSuccess: false,
      draftSuccess: false,
    });
    const errorCSV = ref(null);
    const newFacilityId = ref(null);
    const yearList = ref(
      Array.from({ length: 11 }, (_, i) => moment().year() + i)
    );
    const monthList = ref(Array.from({ length: 12 }, (_, i) => i + 1));
    const getFacilities = computed(() => store.getters.getEducationFacilities);
    const getEducationFacilityType = computed(
      () => store.getters.getEducationFacilityType
    );
    const getSmallFieldData = computed(() => store.getters.getSmallFieldData);
    const getBigFieldData = computed(() => store.getters.getBigFieldData);
    const getAutoSuggestionText = (item) => `${item.name}`;

    const fields = ref([
      {
        label: 'メールアドレス',
        name: 'email_valid',
        type: 'text',
        value: null,
        placeholder: 'メールアドレスを入力してください',
        width: '642px',
        required: true,
        rules: 'required|email',
        class: 'mb-2',
      },
      {
        label: '姓',
        name: 'family_name',
        type: 'text',
        placeholder: '姓',
        value: '',
        rules: 'required:姓',
      },
      {
        label: '名',
        name: 'first_name',
        type: 'text',
        placeholder: '名',
        value: '',
        rules: 'required:名',
      },
      {
        label: 'セイ',
        name: 'family_name_furigana',
        type: 'text',
        placeholder: 'セイ',
        value: '',
        rules: 'required:セイ|full_width_katakana',
      },
      {
        label: 'メイ',
        name: 'first_name_furigana',
        type: 'text',
        placeholder: 'メイ',
        value: '',
        rules: 'required:メイ|full_width_katakana',
      },
      {
        label: '学校名',
        name: 'education_facility_id',
        type: 'autocomplete',
        noDataText: 'その他',
        placeholder: 'その他の学校名は必須です',
        row_class: '',
        item_value: 'id',
        item_text: getAutoSuggestionText,
        items: [],
        tempItems: [],
        hideNoData: 'その他',
        value: null,
        rules: 'required: 入力されていない項目があります',
        addNewDataField: {
          trigger: false,
          fields: [
            {
              label: 'その他の学校名を入力してください',
              name: 'new_educational_facility_name',
              type: 'text',
              placeholder: 'その他の学校名を入力してください',
              value: '',
              rules: 'required',
            },
            {
              label: '教育機関種別を選択してください',
              name: 'new_educational_facility_type',
              type: 'dropdown',
              placeholder: '教育機関種別を選択してください',
              item_value: 'id',
              item_text: 'name',
              items: getEducationFacilityType.value,
              no_data_text: '',
              value: '',
              rules: 'required:教育機関種別は必須です',
            },
          ],
        },
      },
      {
        label: '学部/専攻',
        name: 'big_field_id',
        type: 'dropdown',
        placeholder: '候補から選択してください',
        item_value: 'id',
        item_text: 'name',
        items: [],
        no_data_text: '',
        value: null,
        rules: 'required: 入力されていない項目があります',
      },
      {
        label: '',
        name: 'small_field_id',
        type: 'dropdown',
        placeholder: '候補から選択してください',
        item_value: 'id',
        item_text: 'name',
        items: [],
        no_data_text: '',
        value: null,
        rules: 'required: 入力されていない項目があります',
        disabled: true,
      },
      {
        label: '',
        name: 'study_detail',
        type: 'text',
        placeholder: '専攻分野詳細',
        value: '',
        rules: '',
      },
      {
        label: '卒業予定',
        name: 'graduate_year',
        type: 'dropdown',
        placeholder: '年を選択してください',
        item_value: 'value',
        item_text: 'text',
        items: [],
        no_data_text: '',
        value: null,
        rules: 'required',
      },
      {
        label: '',
        name: 'graduate_month',
        type: 'dropdown',
        placeholder: '月を選択してください',
        item_value: 'value',
        item_text: 'text',
        items: [],
        no_data_text: '',
        value: null,
        rules: 'required',
      },
      {
        label: '興味のある仕事',
        name: 'interested_job_id_1',
        type: 'dropdown',
        placeholder: '興味のある仕事1',
        item_value: 'id',
        item_text: 'text',
        items: [],
        no_data_text: '',
        value: null,
        rules: '',
      },

      {
        label: '',
        name: 'text_interested_job_1',
        type: 'text',
        placeholder: 'フリーテキスト：Pythonエンジニア,CADなど',
        no_data_text: '',
        value: '',
        rules: '',
      },

      {
        label: '',
        name: 'interested_job_id_2',
        type: 'dropdown',
        placeholder: '興味のある仕事2',
        item_value: 'id',
        item_text: 'text',
        items: [],
        no_data_text: '',
        value: null,
        rules: '',
      },

      {
        label: '',
        name: 'text_interested_job_2',
        type: 'text',
        placeholder: 'フリーテキスト：Pythonエンジニア,CADなど',
        items: [],
        no_data_text: '',
        value: '',
        rules: '',
      },

      {
        label: '',
        name: 'interested_job_id_3',
        type: 'dropdown',
        placeholder: '興味のある仕事3',
        item_value: 'id',
        item_text: 'text',
        items: [],
        no_data_text: '',
        value: null,
        rules: '',
      },

      {
        label: '',
        name: 'text_interested_job_3',
        type: 'text',
        placeholder: 'フリーテキスト：Pythonエンジニア,CADなど',
        items: [],
        no_data_text: '',
        value: '',
        rules: '',
      },
      {
        label: '自己PR',
        name: 'self_introduction',
        type: 'textarea',
        value: null,
        placeholder: '自己PR',
        width: '642px',
        class: 'mb-2',
      },
      {
        label: '履歴書URL',
        name: 'resume_url',
        type: 'text',
        value: null,
        placeholder: 'URLを入力してください',
        width: '642px',
        class: 'mb-2',
      },
      {
        label: '資格・実績URL',
        name: 'qualification_url',
        type: 'text',
        value: null,
        placeholder: 'ポートフォリオやGitのURLを入力してください',
        width: '642px',
        class: 'mb-2',
      },
    ]);

    /**
     * Computed Properties
     */
    // Tracks the selected major field value for dependent field updates
    const selectedBigField = computed(() => {
      const bigFieldIndex = fields.value.findIndex(
        (field) => field.name === 'big_field_id'
      );
      return fields.value[bigFieldIndex]?.value;
    });

    // Minimum date for date inputs (current date)
    const minStartDate = computed(() => moment().format('YYYY-MM-DD'));

    // Custom properties for v-autocomplete component
    const customProps = computed(() => ({
      closeOnClick: false,
      closeOnContentClick: true,
      disabled: true,
      openOnClick: false,
      maxHeight: 300,
    }));

    // Watch for major field changes to update minor field options
    watch(selectedBigField, () => {
      getSmallFieldLocal();
    });

    /**
     * CSV Handling Functions
     */
    // Downloads error CSV file when import validation fails
    const downloadError = () => {
      if (errorCSV.value?.error_file_content) {
        // Convert UTF-8 error content to Shift-JIS
        const utf8Array = Encoding.stringToCode(
          errorCSV.value.error_file_content
        );
        const sjisArray = Encoding.convert(utf8Array, {
          to: 'SJIS',
          from: 'UNICODE',
        });
        const uint8Array = new Uint8Array(sjisArray);
        const blob = new Blob([uint8Array], { type: 'text/csv' });

        // Create and trigger CSV download
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', 'error_records.csv');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }
    };

    // Handles CSV file upload for bulk student import
    const uploadCSV = (fileUpload) => {
      const params = new FormData();
      params.append('csv_file', fileUpload);
      store
        .dispatch('USERS_IMPORT', params)
        .then((response) => {
          errorCSV.value = response.data;
        })
        .catch((error) => {
          if (error?.status === 422) {
            console.log('error');
          }
        });
    };

    /**
     * Form Submission Handlers
     */
    // Main form submission handler - determines if new facility needs to be created
    const submit = async (values) => {
      const isNewEducationalFacility =
        fields.value[5]?.addNewDataField?.fields[0]?.value;
      if (!isNewEducationalFacility) {
        saveToDb(values);
      } else {
        saveNewFacility(values);
      }
    };

    // Saves student data to database
    const saveToDb = (mappedFields) => {
      // Convert field IDs to integers
      mappedFields.big_field_id = parseInt(mappedFields.big_field_id);
      mappedFields.small_field_id = parseInt(mappedFields.small_field_id);
      // Use new facility ID if one was created
      if (newFacilityId.value) {
        mappedFields.education_facility_id = newFacilityId.value;
      }
      store
        .dispatch('USERS_CREATE', mappedFields)
        .then(() => {
          router.push({ name: 'Students' });
        })
        .catch(handleValidationErrors);
    };

    // Creates new educational facility before saving student data
    const saveNewFacility = (mappedFields) => {
      const newFacility = {
        name: fields.value[5]?.addNewDataField?.fields.find(
          (f) => f.name === 'new_educational_facility_name'
        )?.value,
        type: fields.value[5]?.addNewDataField?.fields.find(
          (f) => f.name === 'new_educational_facility_type'
        )?.value,
      };

      store
        .dispatch('FACILITIES_CREATE', newFacility)
        .then((createdFacility) => {
          newFacilityId.value = createdFacility.data.data.data.id;
          saveToDb(mappedFields);
        })
        .catch((err) => {
          dialog.error = err?.data?.errors?.name[0];
        });
    };

    // Handles validation errors from API
    const handleValidationErrors = (error) => {
      if (error?.status === 422) {
        console.log('error');
      }
    };

    /**
     * Student Update Functions
     */
    // Updates existing student record
    const updateStudent = async (observerRef) => {
      fields.value.id = route.params.id;
      store.dispatch('USERS_EDIT', fields.value).catch(handleValidationErrors);
    };

    // Handles selection of "Other" option in school selection
    const selectOther = async () => {
      const field = fields.value.find(
        (field) => field.name === 'education_facility_id'
      );
      field.value = 'other';
      field.addNewDataField.trigger = true;
      moveToNextFocus();
    };

    /**
     * Form Navigation Functions
     */
    // Moves focus to next form element
    const moveToNextFocus = () => {
      const focusableElements = Array.from(
        document.querySelectorAll(
          'input, select, textarea, button, [tabindex]:not([tabindex="-1"])'
        )
      ).filter((el) => !el.disabled && el.offsetParent !== null);

      const currentIndex = focusableElements.indexOf(document.activeElement);

      if (currentIndex !== -1 && currentIndex < focusableElements.length - 1) {
        focusableElements[currentIndex + 1].focus();
      }
    };

    /**
     * Autocomplete Functions
     */
    // Manages suggestion display in school selection
    const hideOrDisplaySuggestions = (input) => {
      const field = fields.value.find(
        (field) => field.name === 'education_facility_id'
      );
      if (input == null || (typeof input === 'string' && input.length === 0)) {
        field.noDataText = false;
        field.items = [];
        field.items.push({
          id: 'other',
          name: 'その他', // Other
        });
      } else {
        field.noDataText = 'その他'; // Other
        field.items = field.tempItems;
      }
    };

    /**
     * Field Management Functions
     */
    // Initializes major field options
    const getBigFieldLocal = () => {
      const bigFieldIndex = fields.value.findIndex(
        (field) => field.name === 'big_field_id'
      );

      if (bigFieldIndex !== -1) {
        const data = getBigFieldData.value || [];
        fields.value[bigFieldIndex] = {
          ...fields.value[bigFieldIndex], // Spread existing properties
          items: data.map((item) => ({
            id: item.id,
            name: item.big_field_name,
          })),
        };
      } else {
        fields.value[bigFieldIndex].items = [];
      }
    };

    // Updates minor field options based on selected major field
    const getSmallFieldLocal = () => {
      const bigFieldIndex = fields.value.findIndex(
        (field) => field.name === 'big_field_id'
      );
      const smallFieldIndex = fields.value.findIndex(
        (field) => field.name === 'small_field_id'
      );
      fields.value[smallFieldIndex].value = null;

      if (bigFieldIndex !== -1 && smallFieldIndex !== -1) {
        const data = getSmallFieldData.value || [];
        fields.value[smallFieldIndex].items = data
          .filter(
            (item) => item.big_field_id === fields.value[bigFieldIndex]?.value
          )
          .map((item) => ({
            id: item.id,
            name: item.small_field_name,
          }));
        fields.value[smallFieldIndex].disabled = false;
      } else {
        fields.value[smallFieldIndex].items = [];
      }
    };

    /**
     * Field Data Initialization
     */
    // Sets initial values for dropdown fields
    const setfieldsItemsData = () => {
      fields.value.forEach((field) => {
        if (field.name === 'education_facility_id') {
          field.items = getFacilities.value;
          field.tempItems = getFacilities.value;
        } else if (field.name === 'graduate_year') {
          field.items = yearList.value;
        } else if (field.name.includes('interested_job')) {
          field.items = store.getters.getMasterData?.interested_jobs?.data.map(
            (job) => ({ id: job.id, text: job.job_name })
          );
        } else if (field.name === 'graduate_month') {
          field.items = monthList.value;
        }
      });
    };

    // Component initialization
    onMounted(async () => {
      try {
        await store.dispatch('GET_EDUCATION__FACILITY_DATA'); // Await the action
        console.log('Data fetched successfully');
      } catch (error) {
        console.error('Error fetching data:', error);
      }
      getBigFieldLocal();
      setfieldsItemsData();
    });

    return {
      fields,
      submit,
      updateStudent,
      hideOrDisplaySuggestions,
      dialog,
      yearList,
      monthList,
      getAutoSuggestionText,
      customProps,
      minStartDate,
      getBigFieldData,
      getSmallFieldData,
      getEducationFacilityType,
      downloadError,
      uploadCSV,
      mdAndUp,
      smAndDown,
      errorCSV,
      selectOther,
    };
  },
};
</script>
<style lang="scss">
.autocomplete {
  :deep(.v-select__slot > input) {
    padding: 0px !important;
  }

  :deep(.v-input__icon--append) {
    display: none !important;
  }
}

:deep(.v-text-field .v-input__control > .v-input__slot input) {
  font-size: 14px !important;
  height: 36px;
}
</style>

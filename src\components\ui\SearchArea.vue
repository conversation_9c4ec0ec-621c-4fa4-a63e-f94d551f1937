<template>
  <v-card class="searchable-box pb-5 px-5">
    <v-card-title
      class="pa-0 d-flex justify-space-between card-title pt-5 pb-2 mb-3 text-333 font-18px"
      v-ripple
    >
      <span>検索</span>
      <v-btn variant="text" @click="ownToggleSearch = !ownToggleSearch">
        <span class="small-line"></span>
      </v-btn>
    </v-card-title>

    <Form @submit="searchSubmit" :initial-values="formValues">
      <h6 class="font-14px mb-1 text-333">検索条件</h6>
      <v-row>
        <v-col cols="12" md="6">
          <div class="d-flex align-start">
            <Field
              name="search_option"
              rules="required"
              v-slot="{ field, errors }"
            >
              <v-select
                v-bind="field"
                :error="errors.length > 0"
                :error-messages="errors"
                variant="outlined"
                density="compact"
                @update:menu="changeInputSearchType"
                v-model="inputSearchType"
                item-title="name"
                item-value="id"
                :items="selectTypeOptions"
                class="mr-6"
                item-color="#13aba3"
                min-width="160px"
                :hide-details="errors.length <= 0"
                menu-icon="$greyExpansionDropdown"
              />
            </Field>
            <div
              v-for="(field, index) in searchFields"
              class="d-flex align-center"
            >
              <template v-if="field.type === 'select'">
                <Field
                  :name="field.name"
                  :rules="field.rules"
                  v-slot="{ field: fieldProps, errors }"
                >
                  <v-select
                    v-bind="computedFieldProps(fieldProps, index)"
                    :placeholder="field.placeholder"
                    :class="field.class"
                    :item-title="field.item_text"
                    :item-value="field.item_value"
                    :items="field.items"
                    variant="outlined"
                    :label="null"
                    density="compact"
                    item-color="#13aba3"
                    :error="errors.length > 0"
                    :error-messages="errors"
                    min-width="160px"
                    :hide-details="errors.length <= 0"
                    menu-icon="$greyExpansionDropdown"
                  />
                </Field>
              </template>
              <template v-else-if="field.type === 'date' && field.pickerType !== 'months'">
                <Field
                  :name="field.name"
                  :rules="field.rules"
                  v-slot="{ field: fieldProps, errors }"
                  v-model="field.value"
                >
                  <DatePicker
                    v-bind="computedFieldProps(fieldProps, index)"
                    :field="field"
                    :errors="errors || []"
                    v-model="field.value"
                    :separator="field.separator ? field.separator : '-'"
                    :min="field.range ? findRangeValue(field.range_input) : ''"
                    :pickerType="field.pickerType"
                    :isShowMessage="errors[0] === '入力されていない項目があります' ? false : true"
                  />
                </Field>
              </template>

              <!-- date with months type - 4 dropdowns for years and months -->
              <template v-else-if="field.type === 'date' && field.pickerType === 'months'">
                <div class="d-flex align-center gap-2">
                  <!-- From Year -->
                  <Field
                    :name="`${field.name}_year`"
                    :rules="field.rules"
                    v-slot="{ field: fieldProps, errors }"
                  >
                    <v-select
                      v-bind="fieldProps"
                      placeholder="年"
                      :items="yearOptions"
                      variant="outlined"
                      :label="null"
                      density="compact"
                      item-color="#13aba3"
                      :error="errors.length > 0"
                      :error-messages="errors"
                      min-width="120px"
                      :hide-details="true"
                      menu-icon="$greyExpansionDropdown"
                    />
                  </Field>

                  <!-- From Month -->
                  <Field
                    :name="`${field.name}_month`"
                    :rules="field.rules"
                    v-slot="{ field: fieldProps, errors }"
                  >
                    <v-select
                      v-bind="fieldProps"
                      placeholder="月"
                      :items="monthOptions"
                      item-title="text"
                      item-value="value"
                      variant="outlined"
                      :label="null"
                      density="compact"
                      item-color="#13aba3"
                      :error="errors.length > 0"
                      :error-messages="errors"
                      min-width="120px"
                      :hide-details="true"
                      menu-icon="$greyExpansionDropdown"
                    />
                  </Field>

                </div>
              </template>

              <template v-else-if="field.type === 'text'">
                <Field
                  :name="field.name"
                  :rules="field.rules"
                  v-slot="{ field: fieldProps, errors }"
                >
                  <v-sheet color="transparent" width="463px">
                    <v-text-field
                      v-bind="computedFieldProps(fieldProps, index)"
                      :label="null"
                      :placeholder="field.placeholder"
                      :class="field.class"
                      density="compact"
                      variant="outlined"
                      :error="errors.length > 0"
                      :error-messages="errors"
                      :hide-details="errors.length <= 0"
                    />
                  </v-sheet>
                </Field>
              </template>
              <span v-if="field.show_after_approx" class="font-11px mx-1"
                >～</span
              >
            </div>
          </div>
        </v-col>
        <v-col cols="12" md="6" class="text-right">
          <v-btn
            type="submit"
            class="white--text"
            color="primary"
            width="114px"
            height="35px"
            :disabled="!isValid"
          >
            検索
          </v-btn>
        </v-col>
      </v-row>
    </Form>
  </v-card>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue';
import { useForm, Form, Field } from 'vee-validate';
import DatePicker from '@/components/ui/DatePicker.vue';
import moment from 'moment';

export default {
  name: 'SearchArea',
  components: { DatePicker, Form, Field },
  props: {
    toggleSearch: {
      type: Boolean,
      default: false,
      required: true,
    },
    searchFields: {
      type: Array,
      default: () => [],
      required: true,
    },
    selectTypeOptions: {
      type: Array,
      default: () => [],
      required: true,
    },
  },
  setup(props, { emit }) {
    const inputSearchType = ref(props.selectTypeOptions[0]?.id || '');

    // Generate year options (2 last years and 1 future year)
    const currentYear = new Date().getFullYear();
    const yearOptions = ref([
      currentYear - 2,
      currentYear - 1,
      currentYear,
      currentYear + 1
    ]);

    // Generate month options in Japanese style
    const monthOptions = ref([
      { text: '1月', value: 1 },
      { text: '2月', value: 2 },
      { text: '3月', value: 3 },
      { text: '4月', value: 4 },
      { text: '5月', value: 5 },
      { text: '6月', value: 6 },
      { text: '7月', value: 7 },
      { text: '8月', value: 8 },
      { text: '9月', value: 9 },
      { text: '10月', value: 10 },
      { text: '11月', value: 11 },
      { text: '12月', value: 12 }
    ]);

    onMounted(() => {
      changeInputSearchType();
    });

    const computedFieldProps = (fieldProps, index) => {
      return {
        ...fieldProps,
        // Add default values or extra props here
        value: fieldProps.value || '', // Example default value
        items: props.searchFields[index].items || '', // Any custom prop you need
      };
    };

    const ownToggleSearch = ref(props.toggleSearch);

    const inputSearchTypeError = computed(() => {
      return !inputSearchType.value ? 'This field is required' : null;
    });

    // Use VeeValidate's useForm to access form state
    const { handleSubmit, errors, validate } = useForm();

    const searchSubmit = (values) => {
      // Process year-month combinations for date fields with months type
      const processedValues = { ...values };

      // Find all year-month field combinations and format them
      const yearMonthFields = {};
      Object.keys(values).forEach(key => {
        if (key.includes('_from_year') || key.includes('_to_year') ||
            key.includes('_from_month') || key.includes('_to_month')) {
          const baseFieldName = key.replace(/_from_year|_to_year|_from_month|_to_month/, '');
          if (!yearMonthFields[baseFieldName]) {
            yearMonthFields[baseFieldName] = {};
          }

          if (key.includes('_from_year')) {
            yearMonthFields[baseFieldName].fromYear = values[key];
          } else if (key.includes('_from_month')) {
            yearMonthFields[baseFieldName].fromMonth = values[key];
          } else if (key.includes('_to_year')) {
            yearMonthFields[baseFieldName].toYear = values[key];
          } else if (key.includes('_to_month')) {
            yearMonthFields[baseFieldName].toMonth = values[key];
          }
        }
      });

      // Format year-month combinations as YYYY-MM
      Object.keys(yearMonthFields).forEach(fieldName => {
        const field = yearMonthFields[fieldName];

        // Format from date (YYYY-MM)
        if (field.fromYear && field.fromMonth) {
          const formattedFromMonth = field.fromMonth.toString().padStart(2, '0');
          processedValues[`${fieldName}_from`] = `${field.fromYear}-${formattedFromMonth}`;
        }

        // Format to date (YYYY-MM)
        if (field.toYear && field.toMonth) {
          const formattedToMonth = field.toMonth.toString().padStart(2, '0');
          processedValues[`${fieldName}_to`] = `${field.toYear}-${formattedToMonth}`;
        }

        // Remove individual year/month fields from processed values
        delete processedValues[`${fieldName}_from_year`];
        delete processedValues[`${fieldName}_from_month`];
        delete processedValues[`${fieldName}_to_year`];
        delete processedValues[`${fieldName}_to_month`];
      });

      const finalSeacrhFields = updateValues(processedValues, props.searchFields);
      emit('searchSubmit', {
        type: processedValues.search_option,
        fields: finalSeacrhFields,
      });
    };

    const updateValues = (initialProps, fields) => {
      // Iterate over each field in the array
      return fields.map((field) => {
        // If a matching key exists in initialProps, update the value of the field
        if (initialProps[field.name] !== undefined) {
          return {
            ...field,
            value: initialProps[field.name],
          };
        }
        // Return the field unmodified if there's no match
        return field;
      });
    };

    const changeInputSearchType = () => {
      emit('changedInputType', inputSearchType.value);
    };

    const getPlacehodler = (field) => {
      return field.placeholder;
    };

    const findRangeValue = (rangeInputName) => {
      const field = props.searchFields.find((el) => el.name === rangeInputName);
      return field ? field.value : '';
    };

    // Computed property to determine if the form is valid
    const isValid = computed(() => {
      return !Object.keys(errors.value).length;
    });

    watch(
      () => props.toggleSearch,
      (newVal) => {
        ownToggleSearch.value = newVal;
      }
    );

    // Initial values
    const formValues = ref({
      search_option: 'search',
      search: '',
      date_from: moment().format('YYYY-MM-DD'),
      date_to: moment().format('YYYY-MM-DD'),
    });

    watch(ownToggleSearch, (newVal) => {
      emit('toggleSearch', newVal);
    });

    return {
      inputSearchType,
      ownToggleSearch,
      inputSearchTypeError,
      searchSubmit,
      changeInputSearchType,
      findRangeValue,
      isValid,
      getPlacehodler,
      formValues,
      computedFieldProps,
      yearOptions,
      monthOptions,
    };
  },
};
</script>

<style lang="scss">
.searchable-box {
  background-color: white;
}

.font-14px {
  font-size: 14px;
}

.text-333 {
  color: #333;
}

.gap-2 {
  gap: 8px;
}
</style>
